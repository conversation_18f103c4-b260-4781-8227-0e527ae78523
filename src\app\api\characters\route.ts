import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createCharacterSchema, characterQuerySchema } from '@/lib/validation/schemas'
import { z } from 'zod'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = {
      project_id: searchParams.get('project_id'),
      role: searchParams.get('role'),
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined
    }

    const validatedQuery = characterQuerySchema.parse(queryParams)

    // Build query with project ownership verification
    let query = supabase
      .from('characters')
      .select(`
        *,
        projects!inner (
          id,
          title,
          user_id
        )
      `)
      .eq('projects.user_id', user.id)

    // Apply filters
    if (validatedQuery.project_id) {
      query = query.eq('project_id', validatedQuery.project_id)
    }
    if (validatedQuery.role) {
      query = query.eq('role', validatedQuery.role)
    }

    // Apply pagination
    if (validatedQuery.limit) {
      query = query.limit(validatedQuery.limit)
    }
    if (validatedQuery.offset) {
      query = query.range(validatedQuery.offset, (validatedQuery.offset + (validatedQuery.limit || 50)) - 1)
    }

    // Order by creation date
    query = query.order('created_at', { ascending: false })

    const { data: characters, error, count } = await query

    if (error) {
      console.error('Error fetching characters:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Clean up the response to remove nested project data
    const cleanCharacters = characters?.map(char => {
      const { projects: _, ...character } = char
      // Suppress unused variable warning for intentionally unused destructured property
      void _
      return character
    })

    return NextResponse.json({ 
      characters: cleanCharacters || [],
      pagination: {
        total: count,
        limit: validatedQuery.limit || 50,
        offset: validatedQuery.offset || 0
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error in characters GET:', error)
    return NextResponse.json(
      { error: 'Failed to fetch characters' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate input
    const validatedData = createCharacterSchema.parse(body)

    // Verify project ownership
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', validatedData.project_id)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Check if character_id already exists in this project
    const { data: existingCharacter } = await supabase
      .from('characters')
      .select('id')
      .eq('project_id', validatedData.project_id)
      .eq('character_id', validatedData.character_id)
      .single()

    if (existingCharacter) {
      return NextResponse.json({ 
        error: 'Character with this ID already exists in the project' 
      }, { status: 409 })
    }

    // Create character
    const { data: character, error } = await supabase
      .from('characters')
      .insert({
        ...validatedData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating character:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ character }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid character data',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error in characters POST:', error)
    return NextResponse.json(
      { error: 'Failed to create character' },
      { status: 500 }
    )
  }
}