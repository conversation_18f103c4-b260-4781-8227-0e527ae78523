import { jsPDF } from 'jspdf'
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx'
import JSZip from 'jszip'
import type { Project, Chapter, Character, PersonalityTraits, CharacterArc } from '@/lib/db/types'

export interface ExportData {
  project: Project
  chapters: Chapter[]
  characters: Character[]
}

export interface ExportOptions {
  includeCharacters?: boolean
  includeOutline?: boolean
  pageBreakBetweenChapters?: boolean
  fontSize?: number
  fontFamily?: string
}

export async function generatePDFExport(data: ExportData, options: ExportOptions = {}): Promise<Buffer> {
  const { project, chapters, characters } = data
  const {
    includeCharacters = true,
    pageBreakBetweenChapters = true,
    fontSize = 12,
    fontFamily = 'Times'
  } = options

  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'pt',
    format: 'a4'
  })

  // Set font
  pdf.setFont(fontFamily)
  
  let yPosition = 100
  const margin = 72 // 1 inch margins
  const lineHeight = fontSize * 1.2
  const pageWidth = pdf.internal.pageSize.getWidth()
  const pageHeight = pdf.internal.pageSize.getHeight()
  const textWidth = pageWidth - (margin * 2)

  // Title page
  pdf.setFontSize(24)
  pdf.text(String(project.title || 'Untitled').toUpperCase(), pageWidth / 2, yPosition, { align: 'center' })
  
  yPosition += 50
  if (project.description) {
    pdf.setFontSize(14)
    const descLines = pdf.splitTextToSize(String(project.description), textWidth)
    pdf.text(descLines, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += descLines.length * lineHeight + 30
  }

  pdf.setFontSize(12)
  pdf.text(`Genre: ${project.primary_genre || 'Unknown'}`, pageWidth / 2, yPosition, { align: 'center' })
  yPosition += lineHeight
  pdf.text(`Word Count: ${Number(project.current_word_count || 0).toLocaleString()} words`, pageWidth / 2, yPosition, { align: 'center' })
  yPosition += lineHeight
  pdf.text(`Chapters: ${chapters.length}`, pageWidth / 2, yPosition, { align: 'center' })

  // Add new page for content
  pdf.addPage()
  yPosition = margin

  // Table of contents
  pdf.setFontSize(18)
  pdf.text('TABLE OF CONTENTS', margin, yPosition)
  yPosition += 30
  
  pdf.setFontSize(12)
  chapters.forEach((chapter, index) => {
    if (yPosition > pageHeight - margin) {
      pdf.addPage()
      yPosition = margin
    }
    const chapterTitle = String(chapter.title || `Chapter ${chapter.chapter_number || index + 1}`)
    pdf.text(`${index + 1}. ${chapterTitle}`, margin, yPosition)
    yPosition += lineHeight
  })

  // Chapters
  chapters.forEach((chapter, index) => {
    if (pageBreakBetweenChapters || index === 0) {
      pdf.addPage()
      yPosition = margin
    }

    // Chapter title
    pdf.setFontSize(16)
    const chapterTitle = `Chapter ${chapter.chapter_number || index + 1}: ${chapter.title || 'Untitled'}`
    pdf.text(chapterTitle, margin, yPosition)
    yPosition += 30

    // Chapter content
    pdf.setFontSize(fontSize)
    if (chapter.content) {
      const contentLines = pdf.splitTextToSize(String(chapter.content), textWidth)
      
      for (const line of contentLines) {
        if (yPosition > pageHeight - margin) {
          pdf.addPage()
          yPosition = margin
        }
        pdf.text(line, margin, yPosition)
        yPosition += lineHeight
      }
    }
    yPosition += 20
  })

  // Character reference
  if (includeCharacters && characters.length > 0) {
    pdf.addPage()
    yPosition = margin
    
    pdf.setFontSize(18)
    pdf.text('CHARACTER REFERENCE', margin, yPosition)
    yPosition += 30

    characters.forEach(char => {
      if (yPosition > pageHeight - margin - 100) {
        pdf.addPage()
        yPosition = margin
      }

      pdf.setFontSize(14)
      pdf.text(`${char.name || 'Unnamed'} (${char.role || 'Unknown'})`, margin, yPosition)
      yPosition += 20

      pdf.setFontSize(fontSize)
      if (char.description) {
        const descLines = pdf.splitTextToSize(String(char.description), textWidth)
        pdf.text(descLines, margin, yPosition)
        yPosition += descLines.length * lineHeight + 10
      }

      if (char.backstory) {
        const backstoryLines = pdf.splitTextToSize(`Backstory: ${String(char.backstory)}`, textWidth)
        pdf.text(backstoryLines, margin, yPosition)
        yPosition += backstoryLines.length * lineHeight + 15
      }
    })
  }

  return Buffer.from(pdf.output('arraybuffer'))
}

export async function generateDOCXExport(data: ExportData, options: ExportOptions = {}): Promise<Buffer> {
  const { project, chapters, characters } = data
  const {
    includeCharacters = true,
    pageBreakBetweenChapters = true
  } = options

  const children: Paragraph[] = []

  // Title page
  children.push(
    new Paragraph({
      alignment: AlignmentType.CENTER,
      children: [
        new TextRun({
          text: String(project.title || 'Untitled').toUpperCase(),
          size: 48,
          bold: true,
        }),
      ],
      spacing: { after: 400 },
    })
  )

  if (project.description) {
    children.push(
      new Paragraph({
        alignment: AlignmentType.CENTER,
        children: [
          new TextRun({
            text: String(project.description),
            size: 28,
          }),
        ],
        spacing: { after: 400 },
      })
    )
  }

  children.push(
    new Paragraph({
      alignment: AlignmentType.CENTER,
      children: [
        new TextRun({
          text: `Genre: ${project.primary_genre || 'Unknown'}`,
          size: 24,
        }),
      ],
      spacing: { after: 200 },
    }),
    new Paragraph({
      alignment: AlignmentType.CENTER,
      children: [
        new TextRun({
          text: `Word Count: ${Number(project.current_word_count || 0).toLocaleString()} words`,
          size: 24,
        }),
      ],
      spacing: { after: 200 },
    }),
    new Paragraph({
      alignment: AlignmentType.CENTER,
      children: [
        new TextRun({
          text: `Chapters: ${chapters.length}`,
          size: 24,
        }),
      ],
      spacing: { after: 400 },
    })
  )

  // Page break
  children.push(
    new Paragraph({
      pageBreakBefore: true,
      children: [],
    })
  )

  // Table of contents
  children.push(
    new Paragraph({
      heading: HeadingLevel.HEADING_1,
      children: [
        new TextRun({
          text: 'TABLE OF CONTENTS',
          bold: true,
        }),
      ],
      spacing: { after: 400 },
    })
  )

  chapters.forEach((chapter, index) => {
    const chapterTitle = String(chapter.title || `Chapter ${chapter.chapter_number || index + 1}`)
    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `${index + 1}. ${chapterTitle}`,
            size: 24,
          }),
        ],
        spacing: { after: 200 },
      })
    )
  })

  // Chapters
  chapters.forEach((chapter, index) => {
    if (pageBreakBetweenChapters) {
      children.push(
        new Paragraph({
          pageBreakBefore: true,
          children: [],
        })
      )
    }

    // Chapter title
    const chapterTitle = `Chapter ${chapter.chapter_number || index + 1}: ${chapter.title || 'Untitled'}`
    children.push(
      new Paragraph({
        heading: HeadingLevel.HEADING_1,
        children: [
          new TextRun({
            text: chapterTitle,
            bold: true,
          }),
        ],
        spacing: { after: 400 },
      })
    )

    // Chapter content
    if (chapter.content) {
      const paragraphs = String(chapter.content).split('\n\n')
      paragraphs.forEach((para: string) => {
        if (para.trim()) {
          children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: para.trim(),
                  size: 24,
                }),
              ],
              spacing: { after: 240 },
              indent: { firstLine: 720 }, // First line indent
            })
          )
        }
      })
    }
  })

  // Character reference
  if (includeCharacters && characters.length > 0) {
    children.push(
      new Paragraph({
        pageBreakBefore: true,
        children: [],
      }),
      new Paragraph({
        heading: HeadingLevel.HEADING_1,
        children: [
          new TextRun({
            text: 'CHARACTER REFERENCE',
            bold: true,
          }),
        ],
        spacing: { after: 400 },
      })
    )

    characters.forEach(char => {
      children.push(
        new Paragraph({
          heading: HeadingLevel.HEADING_2,
          children: [
            new TextRun({
              text: `${char.name || 'Unnamed'} (${char.role || 'Unknown'})`,
              bold: true,
            }),
          ],
          spacing: { after: 200 },
        })
      )

      if (char.description) {
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: String(char.description || ''),
                size: 24,
              }),
            ],
            spacing: { after: 200 },
          })
        )
      }

      if (char.backstory) {
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: `Backstory: ${String(char.backstory || '')}`,
                size: 24,
              }),
            ],
            spacing: { after: 300 },
          })
        )
      }
    })
  }

  const doc = new Document({
    sections: [
      {
        properties: {
          page: {
            margin: {
              top: 1440,    // 1 inch
              right: 1440,  // 1 inch
              bottom: 1440, // 1 inch
              left: 1440,   // 1 inch
            },
          },
        },
        children: children,
      },
    ],
  })

  return await Packer.toBuffer(doc)
}

export async function generateEPUBExport(data: ExportData, options: ExportOptions = {}): Promise<Buffer> {
  const { project, chapters, characters } = data
  const { includeCharacters = true } = options

  const zip = new JSZip()
  const uuid = generateUUID()
  const now = new Date().toISOString()

  // Add mimetype (must be first file and uncompressed)
  zip.file('mimetype', 'application/epub+zip', { compression: 'STORE' })

  // Create META-INF directory
  const metaInf = zip.folder('META-INF')
  if (!metaInf) throw new Error('Failed to create META-INF directory')
  
  metaInf.file('container.xml', `<?xml version="1.0" encoding="UTF-8"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="OEBPS/content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>`)

  // Create OEBPS directory
  const oebps = zip.folder('OEBPS')
  if (!oebps) throw new Error('Failed to create OEBPS directory')

  // Generate table of contents NCX
  let tocNCX = `<?xml version="1.0" encoding="UTF-8"?>
<ncx xmlns="http://www.daisy.org/z3986/2005/ncx/" version="2005-1">
  <head>
    <meta name="dtb:uid" content="${uuid}"/>
    <meta name="dtb:depth" content="1"/>
    <meta name="dtb:totalPageCount" content="0"/>
    <meta name="dtb:maxPageNumber" content="0"/>
  </head>
  <docTitle>
    <text>${escapeXML(project.title || 'Untitled')}</text>
  </docTitle>
  <navMap>`

  let contentOPF = `<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" version="3.0" unique-identifier="uid">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:identifier id="uid">${uuid}</dc:identifier>
    <dc:title>${escapeXML(project.title || 'Untitled')}</dc:title>
    <dc:creator>BookScribe AI</dc:creator>
    <dc:language>en</dc:language>
    <dc:date>${now}</dc:date>
    <meta property="dcterms:modified">${now}</meta>
  </metadata>
  <manifest>
    <item id="ncx" href="toc.ncx" media-type="application/x-dtbncx+xml"/>
    <item id="toc" href="toc.xhtml" media-type="application/xhtml+xml" properties="nav"/>
    <item id="title" href="title.xhtml" media-type="application/xhtml+xml"/>`

  let spine = `  <spine toc="ncx">
    <itemref idref="title"/>
    <itemref idref="toc"/>`

  // Create title page
  const titlePage = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>${escapeXML(project.title || 'Untitled')}</title>
  <meta charset="UTF-8"/>
</head>
<body>
  <h1>${escapeXML(project.title || 'Untitled')}</h1>
  ${project.description ? `<p>${escapeXML(project.description)}</p>` : ''}
  <p>Genre: ${escapeXML(project.primary_genre || 'Unknown')}</p>
  <p>Word Count: ${Number(project.current_word_count || 0).toLocaleString()}</p>
</body>
</html>`
  oebps.file('title.xhtml', titlePage)

  // Create navigation document (EPUB 3.0)
  let navDoc = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops">
<head>
  <title>Table of Contents</title>
  <meta charset="UTF-8"/>
</head>
<body>
  <nav epub:type="toc">
    <h1>Table of Contents</h1>
    <ol>
      <li><a href="title.xhtml">Title Page</a></li>`

  // Add chapters
  chapters.forEach((chapter, index) => {
    const chapterId = `chapter${index + 1}`
    const chapterTitle = chapter.title || `Chapter ${chapter.chapter_number || index + 1}`
    
    // Add to manifest
    contentOPF += `\n    <item id="${chapterId}" href="${chapterId}.xhtml" media-type="application/xhtml+xml"/>`
    
    // Add to spine
    spine += `\n    <itemref idref="${chapterId}"/>`
    
    // Add to NCX
    tocNCX += `\n    <navPoint id="navPoint-${index + 1}" playOrder="${index + 2}">
      <navLabel>
        <text>${escapeXML(chapterTitle)}</text>
      </navLabel>
      <content src="${chapterId}.xhtml"/>
    </navPoint>`
    
    // Add to navigation document
    navDoc += `\n      <li><a href="${chapterId}.xhtml">${escapeXML(chapterTitle)}</a></li>`
    
    // Create chapter file
    const chapterContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>${escapeXML(chapterTitle)}</title>
  <meta charset="UTF-8"/>
</head>
<body>
  <h1>${escapeXML(chapterTitle)}</h1>
  ${chapter.content ? formatContentForEPUB(chapter.content) : '<p>[Chapter content not yet written]</p>'}
</body>
</html>`
    oebps.file(`${chapterId}.xhtml`, chapterContent)
  })

  // Add character reference if requested
  if (includeCharacters && characters.length > 0) {
    contentOPF += `\n    <item id="characters" href="characters.xhtml" media-type="application/xhtml+xml"/>`
    spine += `\n    <itemref idref="characters"/>`
    navDoc += `\n      <li><a href="characters.xhtml">Character Reference</a></li>`
    
    tocNCX += `\n    <navPoint id="navPoint-characters" playOrder="${chapters.length + 2}">
      <navLabel>
        <text>Character Reference</text>
      </navLabel>
      <content src="characters.xhtml"/>
    </navPoint>`
    
    let charactersContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Character Reference</title>
  <meta charset="UTF-8"/>
</head>
<body>
  <h1>Character Reference</h1>`
    
    characters.forEach(char => {
      charactersContent += `\n  <h2>${escapeXML(char.name || 'Unnamed')} (${escapeXML(char.role || 'Unknown')})</h2>`
      if (char.description) {
        charactersContent += `\n  <p>${escapeXML(char.description)}</p>`
      }
      if (char.backstory) {
        charactersContent += `\n  <p><strong>Backstory:</strong> ${escapeXML(char.backstory)}</p>`
      }
    })
    
    charactersContent += `\n</body>\n</html>`
    oebps.file('characters.xhtml', charactersContent)
  }

  // Close navigation structures
  navDoc += `\n    </ol>
  </nav>
</body>
</html>`
  tocNCX += `\n  </navMap>\n</ncx>`
  contentOPF += `\n  </manifest>\n${spine}\n  </spine>\n</package>`

  // Write navigation files
  oebps.file('toc.xhtml', navDoc)
  oebps.file('toc.ncx', tocNCX)
  oebps.file('content.opf', contentOPF)

  // Generate EPUB file
  const epubBuffer = await zip.generateAsync({ type: 'nodebuffer' })
  return epubBuffer
}

// Helper function to escape XML special characters
function escapeXML(str: string): string {
  return String(str)
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;')
}

// Helper function to format content for EPUB
function formatContentForEPUB(content: string): string {
  const paragraphs = String(content).split('\n\n')
  return paragraphs
    .filter(p => p.trim())
    .map(p => `<p>${escapeXML(p.trim())}</p>`)
    .join('\n  ')
}

// Helper function to generate UUID
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

export function generateEnhancedTextExport(data: ExportData, options: ExportOptions = {}): string {
  const { project, chapters, characters } = data
  const { includeCharacters = true, includeOutline = false } = options

  let content = ''

  // Title page with better formatting
  const title = String(project.title || 'Untitled');
  content += `${title.toUpperCase()}\n`
  content += `${'='.repeat(Math.min(title.length, 50))}\n\n`
  
  if (project.description) {
    content += `${String(project.description)}\n\n`
  }

  // Metadata
  content += `STORY DETAILS\n`
  content += `${'-'.repeat(13)}\n`
  content += `Genre: ${project.primary_genre || 'Unknown'}\n`
  if (project.subgenre) content += `Subgenre: ${String(project.subgenre)}\n`
  content += `Target Audience: ${project.target_audience || 'General'}\n`
  content += `Content Rating: ${project.content_rating || 'Not Rated'}\n`
  content += `Word Count: ${Number(project.current_word_count || 0).toLocaleString()} words\n`
  content += `Chapters: ${chapters.length}\n`
  content += `Status: ${project.status}\n\n`

  // Table of contents
  content += `TABLE OF CONTENTS\n`
  content += `${'='.repeat(17)}\n\n`
  chapters.forEach((chapter, index) => {
    const title = chapter.title || `Chapter ${chapter.chapter_number}`
    content += `${String(index + 1).padStart(2, '0')}. ${title}\n`
  })
  content += '\n\n'

  // Story outline (if requested)
  if (includeOutline) {
    content += `STORY OUTLINE\n`
    content += `${'='.repeat(13)}\n\n`
    chapters.forEach(chapter => {
      if (chapter.outline) {
        try {
          const outline = JSON.parse(String(chapter.outline))
          content += `Chapter ${chapter.chapter_number}: ${outline.summary || 'No summary'}\n`
          if (outline.keyEvents) {
            content += `Key Events: ${outline.keyEvents.join(', ')}\n`
          }
          content += '\n'
        } catch {
          content += `Chapter ${chapter.chapter_number}: Outline available\n\n`
        }
      }
    })
    content += '\n'
  }

  // Chapters with enhanced formatting
  chapters.forEach((chapter, index) => {
    content += `\n\n${'*'.repeat(60)}\n`
    content += `CHAPTER ${String(chapter.chapter_number || index + 1).padStart(2, '0')}: ${String(chapter.title || 'UNTITLED').toUpperCase()}\n`
    content += `${'*'.repeat(60)}\n\n`
    
    if (chapter.content) {
      // Add paragraph spacing and indentation
      const paragraphs = String(chapter.content).split('\n\n')
      paragraphs.forEach(para => {
        if (para.trim()) {
          content += `    ${para.trim()}\n\n`
        }
      })
    } else {
      content += `[Chapter content not yet written]\n\n`
    }

    // Chapter stats
    content += `\n${'-'.repeat(30)}\n`
    content += `Words: ${chapter.actual_word_count || 0} | Target: ${chapter.target_word_count || 'Not set'}\n`
  })

  // Character reference with enhanced formatting
  if (includeCharacters && characters && characters.length > 0) {
    content += `\n\n${'='.repeat(60)}\n`
    content += `CHARACTER REFERENCE\n`
    content += `${'='.repeat(60)}\n\n`
    
    characters.forEach(char => {
      const name = String(char.name || 'Unnamed');
      const role = String(char.role || 'Unknown');
      content += `${name.toUpperCase()} (${role})\n`
      content += `${'-'.repeat(name.length + role.length + 3)}\n`
      
      if (char.description) {
        content += `Description: ${char.description}\n\n`
      }
      
      if (char.backstory) {
        content += `Backstory: ${char.backstory}\n\n`
      }

      if (char.personality_traits && typeof char.personality_traits === 'object') {
        const personalityData = char.personality_traits as PersonalityTraits
        const traits = personalityData.traits || (char.personality_traits as string[])
        if (Array.isArray(traits)) {
          content += `Personality: ${traits.join(', ')}\n\n`
        }
      }

      if (char.character_arc && typeof char.character_arc === 'object') {
        const arcData = char.character_arc as CharacterArc
        if (arcData.starting_point || arcData.transformation || arcData.ending_point) {
          content += `Character Arc: ${arcData.starting_point || ''} → ${arcData.transformation || ''} → ${arcData.ending_point || ''}\n\n`
        } else if (typeof char.character_arc === 'string') {
          content += `Character Arc: ${char.character_arc}\n\n`
        }
      }

      content += `${'-'.repeat(30)}\n\n`
    })
  }

  // Footer
  content += `\n\n${'='.repeat(60)}\n`
  content += `Generated by BookScribe AI\n`
  content += `Export Date: ${new Date().toLocaleDateString()}\n`
  content += `${'='.repeat(60)}\n`

  return content
}