import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { LinkButton } from '@/components/ui/link-button'
import { SampleProjectGenerator } from '@/components/onboarding/sample-project-generator'

export default async function SamplesPage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            <LinkButton href="/dashboard" variant="ghost">← Back to Dashboard</LinkButton>
            <h1 className="text-2xl font-bold">Sample Projects</h1>
          </div>
          <LinkButton href="/projects/new">Create Custom Project</LinkButton>
        </div>
      </header>
      
      <main className="container py-8">
        <div className="max-w-6xl mx-auto">
          <SampleProjectGenerator />
        </div>
      </main>
    </div>
  )
}