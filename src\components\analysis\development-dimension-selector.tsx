'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { 
  Filter,
  Settings,
  Eye,
  EyeOff,
  RotateCcw,
  Info,
  CheckCircle2,
  Circle
} from 'lucide-react';
import { DEVELOPMENT_DIMENSIONS } from '@/lib/types/character-development';
import type { DevelopmentDimension } from '@/lib/types/character-development';

interface DevelopmentDimensionSelectorProps {
  selectedDimensions: string[];
  onSelectionChange: (selectedDimensions: string[]) => void;
  dimensionData?: { [dimensionId: string]: { visible: boolean; hasData: boolean; } };
  compact?: boolean;
}

export function DevelopmentDimensionSelector({ 
  selectedDimensions, 
  onSelectionChange, 
  dimensionData = {},
  compact = false 
}: DevelopmentDimensionSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDimension = (dimensionId: string) => {
    const newSelection = selectedDimensions.includes(dimensionId)
      ? selectedDimensions.filter(id => id !== dimensionId)
      : [...selectedDimensions, dimensionId];
    onSelectionChange(newSelection);
  };

  const selectAll = () => {
    onSelectionChange(DEVELOPMENT_DIMENSIONS.map(d => d.id));
  };

  const selectNone = () => {
    onSelectionChange([]);
  };

  const selectDefaults = () => {
    // Select the most important dimensions by default
    const defaultDimensions = DEVELOPMENT_DIMENSIONS
      .filter(d => d.weight >= 0.85)
      .map(d => d.id);
    onSelectionChange(defaultDimensions);
  };

  const getDimensionStatus = (dimensionId: string) => {
    const data = dimensionData[dimensionId];
    return {
      visible: data?.visible ?? true,
      hasData: data?.hasData ?? true
    };
  };

  const getDimensionIcon = (dimension: DevelopmentDimension) => {
    switch (dimension.id) {
      case 'emotional-growth':
        return '💭';
      case 'belief-system':
        return '🧠';
      case 'skill-progression':
        return '⚡';
      case 'relationships':
        return '🤝';
      case 'goal-alignment':
        return '🎯';
      case 'internal-conflict':
        return '⚔️';
      case 'external-agency':
        return '🌟';
      default:
        return '📊';
    }
  };

  const selectedCount = selectedDimensions.length;
  const totalCount = DEVELOPMENT_DIMENSIONS.length;

  if (compact) {
    return (
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="w-4 h-4" />
            {selectedCount === totalCount ? 'All Dimensions' : `${selectedCount} of ${totalCount}`}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="start">
          <DimensionSelectorContent
            selectedDimensions={selectedDimensions}
            toggleDimension={toggleDimension}
            selectAll={selectAll}
            selectNone={selectNone}
            selectDefaults={selectDefaults}
            getDimensionStatus={getDimensionStatus}
            getDimensionIcon={getDimensionIcon}
          />
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Development Dimensions
            </CardTitle>
            <CardDescription>
              Select which character development aspects to analyze
            </CardDescription>
          </div>
          <Badge variant="outline">
            {selectedCount}/{totalCount} selected
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <DimensionSelectorContent
          selectedDimensions={selectedDimensions}
          toggleDimension={toggleDimension}
          selectAll={selectAll}
          selectNone={selectNone}
          selectDefaults={selectDefaults}
          getDimensionStatus={getDimensionStatus}
          getDimensionIcon={getDimensionIcon}
        />
      </CardContent>
    </Card>
  );
}

interface DimensionSelectorContentProps {
  selectedDimensions: string[];
  toggleDimension: (dimensionId: string) => void;
  selectAll: () => void;
  selectNone: () => void;
  selectDefaults: () => void;
  getDimensionStatus: (dimensionId: string) => { visible: boolean; hasData: boolean; };
  getDimensionIcon: (dimension: DevelopmentDimension) => string;
}

function DimensionSelectorContent({
  selectedDimensions,
  toggleDimension,
  selectAll,
  selectNone,
  selectDefaults,
  getDimensionStatus,
  getDimensionIcon
}: DimensionSelectorContentProps) {
  return (
    <div className="space-y-4">
      {/* Quick Actions */}
      <div className="flex items-center space-x-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={selectAll}
          className="text-xs"
        >
          <CheckCircle2 className="w-3 h-3 mr-1" />
          All
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={selectNone}
          className="text-xs"
        >
          <Circle className="w-3 h-3 mr-1" />
          None
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={selectDefaults}
          className="text-xs"
        >
          <RotateCcw className="w-3 h-3 mr-1" />
          Defaults
        </Button>
      </div>

      {/* Dimension List */}
      <div className="space-y-3">
        {DEVELOPMENT_DIMENSIONS.map((dimension) => {
          const isSelected = selectedDimensions.includes(dimension.id);
          const status = getDimensionStatus(dimension.id);
          const icon = getDimensionIcon(dimension);

          return (
            <div 
              key={dimension.id} 
              className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                isSelected 
                  ? 'bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800' 
                  : 'bg-gray-50 dark:bg-gray-950/20 border-gray-200 dark:border-gray-800'
              }`}
            >
              <Checkbox
                id={dimension.id}
                checked={isSelected}
                onCheckedChange={() => toggleDimension(dimension.id)}
                disabled={!status.visible}
              />

              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{icon}</span>
                  <label 
                    htmlFor={dimension.id}
                    className={`text-sm font-medium cursor-pointer ${
                      !status.visible ? 'text-gray-400' : ''
                    }`}
                  >
                    {dimension.name}
                  </label>
                  
                  {/* Status indicators */}
                  <div className="flex items-center space-x-1">
                    {!status.hasData && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <EyeOff className="w-3 h-3 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>No data available for this dimension</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    
                    {status.hasData && isSelected && (
                      <Eye className="w-3 h-3 text-green-500" />
                    )}
                  </div>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-3 h-3 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="max-w-xs">
                          <p className="font-medium">{dimension.name}</p>
                          <p className="text-sm mt-1">{dimension.description}</p>
                          <p className="text-xs mt-1 text-gray-500">
                            Weight: {Math.round(dimension.weight * 100)}%
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                
                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                  {dimension.description}
                </p>
              </div>

              {/* Weight indicator */}
              <div className="flex flex-col items-center">
                <div className={`w-2 h-8 rounded-full bg-gradient-to-t ${
                  dimension.weight >= 0.9 ? 'from-green-200 to-green-500' :
                  dimension.weight >= 0.8 ? 'from-blue-200 to-blue-500' :
                  'from-gray-200 to-gray-400'
                }`} style={{ 
                  background: `linear-gradient(to top, transparent ${(1 - dimension.weight) * 100}%, currentColor ${(1 - dimension.weight) * 100}%)` 
                }} />
                <span className="text-xs text-gray-500 mt-1">
                  {Math.round(dimension.weight * 100)}%
                </span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Selection Summary */}
      <div className="pt-3 border-t">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            {selectedDimensions.length} dimensions selected
          </span>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">Coverage:</span>
            <Badge variant="outline" className="text-xs">
              {Math.round(
                DEVELOPMENT_DIMENSIONS
                  .filter(d => selectedDimensions.includes(d.id))
                  .reduce((sum, d) => sum + d.weight, 0) / 
                DEVELOPMENT_DIMENSIONS.reduce((sum, d) => sum + d.weight, 0) * 100
              )}%
            </Badge>
          </div>
        </div>
      </div>
    </div>
  );
}