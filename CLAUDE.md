## Standard Workflow
1. Think through your problem, read the codebase for relevant files, and write a plan to docs using the files available in that folder 
2. The docs\PRD.md plan should maintain a checklist of all files you need to build/touch & delete it once you've completed it and it's dependencies &/or sub-tasks/api connections.
3. Think through 2-3 ways of completing the task & pick the best one which fits the guardrails that you were provided above, this is the output to prompt me with for sign off
4. Provide high level explanation of the changes being made so I understand what and why but it can be brief - Before you begin working, get my sign off on your plan
5. Make every task and code change as simple & efficient as possible.  We want to maintain existing files versus building new - and avoid making complex changes & minimize your impact where possible - everything is about simplicity and efficiency
6. Maintain shared files where possible (types, connectors, utils, and more if possible) but use best dev practices
7. Develop the code in a modular fashion & ensure that you maintain a clean codebase, as well as files that are less than 700 rows long.  Minimize your spacing and only maintain comments where necessary
8. There are docs on implementing OpenAI SDK in the docs folder, use them for reference on set up.
