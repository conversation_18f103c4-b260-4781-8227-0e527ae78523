#!/usr/bin/env node
import { createClient } from '@supabase/supabase-js'
import 'dotenv/config'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const testEmail = process.env.TEST_EMAIL || '<EMAIL>'
const testPassword = process.env.TEST_PASSWORD || 'test123456'

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAPIRoute(name, endpoint, options = {}) {
  console.log(`\n🧪 Testing ${name}...`)
  
  try {
    const response = await fetch(`http://localhost:3000${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        ...options.headers
      }
    })
    
    const data = await response.json()
    
    if (response.ok) {
      console.log(`✅ ${name}: Success (${response.status})`)
      console.log('   Response preview:', JSON.stringify(data).substring(0, 100) + '...')
    } else {
      console.log(`❌ ${name}: Failed (${response.status})`)
      console.log('   Error:', data.error || data)
    }
    
    return { success: response.ok, data, status: response.status }
  } catch (error) {
    console.log(`❌ ${name}: Network error`)
    console.log('   Error:', error.message)
    return { success: false, error }
  }
}

// Sign in first
console.log('🔐 Signing in...')
const { data: { session }, error: authError } = await supabase.auth.signInWithPassword({
  email: testEmail,
  password: testPassword
})

if (authError || !session) {
  console.error('Authentication failed:', authError?.message || 'No session')
  process.exit(1)
}

console.log('✅ Authentication successful')
console.log('   User ID:', session.user.id)

// Get a test project ID
const { data: projects } = await supabase
  .from('projects')
  .select('id, title')
  .limit(1)

const testProjectId = projects?.[0]?.id

if (!testProjectId) {
  console.error('No test project found. Please create a project first.')
  process.exit(1)
}

console.log('📁 Using test project:', projects[0].title)

// Test all new API routes
console.log('\n\n=== Testing API Routes ===')

// Test Notifications
await testAPIRoute('GET /api/notifications', '/api/notifications')
await testAPIRoute('POST /api/notifications', '/api/notifications', {
  method: 'POST',
  body: JSON.stringify({
    type: 'info',
    title: 'Test Notification',
    message: 'This is a test notification from API testing',
    metadata: { test: true }
  })
})

// Test Writing Sessions
await testAPIRoute('GET /api/writing-sessions', '/api/writing-sessions')
await testAPIRoute('POST /api/writing-sessions', '/api/writing-sessions', {
  method: 'POST',
  body: JSON.stringify({
    project_id: testProjectId,
    chapter_id: 1,
    words_written: 500,
    duration_minutes: 30,
    metadata: { test: true }
  })
})

// Test Project Collaborators
await testAPIRoute('GET /api/project-collaborators', '/api/project-collaborators')
await testAPIRoute('POST /api/project-collaborators', '/api/project-collaborators', {
  method: 'POST',
  body: JSON.stringify({
    project_id: testProjectId,
    invitee_email: '<EMAIL>',
    role: 'viewer',
    message: 'Test collaboration invite'
  })
})

// Test Processing Tasks
await testAPIRoute('GET /api/processing-tasks', '/api/processing-tasks')
await testAPIRoute('POST /api/processing-tasks', '/api/processing-tasks', {
  method: 'POST',
  body: JSON.stringify({
    project_id: testProjectId,
    type: 'test_analysis',
    priority: 'medium',
    metadata: { test: true }
  })
})

// Test AI Suggestions (if we have a test ID)
// Note: This would require a real suggestion ID from the database

// Test Version History (if we have a test ID)
// Note: This would require a real version ID from the database

// Test authenticated routes
console.log('\n\n=== Testing Protected Routes ===')

await testAPIRoute('GET /profile (authenticated)', '/profile')
await testAPIRoute('GET /samples (authenticated)', '/samples')
await testAPIRoute('GET /templates (authenticated)', '/templates')

// Test without authentication
console.log('\n\n=== Testing Authentication Protection ===')

const unauthTests = [
  { name: 'GET /api/notifications (unauth)', endpoint: '/api/notifications' },
  { name: 'GET /profile (unauth)', endpoint: '/profile' },
]

for (const test of unauthTests) {
  const response = await fetch(`http://localhost:3000${test.endpoint}`)
  if (response.status === 401 || response.status === 303) {
    console.log(`✅ ${test.name}: Properly protected (${response.status})`)
  } else {
    console.log(`❌ ${test.name}: Not protected! (${response.status})`)
  }
}

console.log('\n\n✨ API Route Testing Complete!')
process.exit(0)