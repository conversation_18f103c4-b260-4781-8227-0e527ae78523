export const GENRES = {
  FANTASY: 'Fantasy',
  SCIENCE_FICTION: 'Science Fiction',
  MYSTERY_THRILLER: 'Mystery/Thriller',
  ROMANCE: 'Romance',
  HISTORICAL_FICTION: 'Historical Fiction',
  LITERARY_FICTION: 'Literary Fiction',
  HORROR: 'Horror',
  ADVENTURE: 'Adventure',
  YOUNG_ADULT: 'Young Adult',
  CONTEMPORARY_FICTION: 'Contemporary Fiction'
} as const

export const FANTASY_SUBGENRES = {
  EPIC_FANTASY: 'Epic Fantasy',
  URBAN_FANTASY: 'Urban Fantasy',
  DARK_FANTASY: 'Dark Fantasy',
  HIGH_FANTASY: 'High Fantasy',
  LOW_FANTASY: 'Low Fantasy',
  SWORD_SORCERY: 'Sword & Sorcery',
  MAGICAL_REALISM: 'Magical Realism'
} as const

export const SCIFI_SUBGENRES = {
  SPACE_OPERA: 'Space Opera',
  CYBERPUNK: 'Cyberpunk',
  DYSTOPIAN: 'Dystopian',
  TIME_TRAVEL: 'Time Travel',
  HARD_SCIFI: 'Hard Sci-Fi',
  SOFT_SCIFI: 'Soft Sci-Fi',
  STEAMPUNK: 'Steampunk',
  BIOPUNK: 'Biopunk'
} as const

export const NARRATIVE_VOICES = {
  FIRST_PERSON: 'First Person',
  THIRD_PERSON_LIMITED: 'Third Person Limited',
  THIRD_PERSON_OMNISCIENT: 'Third Person Omniscient',
  SECOND_PERSON: 'Second Person',
  MULTIPLE_POV: 'Multiple POV'
} as const

export const TENSES = {
  PRESENT: 'Present',
  PAST: 'Past',
  MIXED: 'Mixed'
} as const

export const TONES = {
  DARK_GRITTY: 'Dark & Gritty',
  LIGHT_HUMOROUS: 'Light & Humorous',
  EPIC_HEROIC: 'Epic & Heroic',
  INTIMATE_PERSONAL: 'Intimate & Personal',
  MYSTERIOUS_SUSPENSEFUL: 'Mysterious & Suspenseful',
  ROMANTIC_PASSIONATE: 'Romantic & Passionate',
  PHILOSOPHICAL_CONTEMPLATIVE: 'Philosophical & Contemplative'
} as const

export const WRITING_STYLES = {
  LITERARY: 'Literary',
  COMMERCIAL: 'Commercial',
  PULP: 'Pulp',
  EXPERIMENTAL: 'Experimental',
  MINIMALIST: 'Minimalist',
  DESCRIPTIVE: 'Descriptive',
  DIALOGUE_HEAVY: 'Dialogue-Heavy'
} as const

export const STRUCTURE_TYPES = {
  THREE_ACT: 'Three-Act Structure',
  HEROS_JOURNEY: "Hero's Journey",
  SAVE_THE_CAT: 'Save the Cat',
  FREYTAGS_PYRAMID: "Freytag's Pyramid",
  SEVEN_POINT: 'Seven-Point Story Structure',
  FICHTEAN_CURVE: 'Fichtean Curve'
} as const

export const PACING_PREFERENCES = {
  FAST_PACED: 'Fast-Paced Action',
  SLOW_BURN: 'Slow Burn',
  BALANCED: 'Balanced',
  CHARACTER_DRIVEN: 'Character-Driven',
  PLOT_DRIVEN: 'Plot-Driven'
} as const

export const CHARACTER_COMPLEXITIES = {
  SIMPLE: 'Simple/Archetypal',
  COMPLEX: 'Complex/Layered',
  MORALLY_AMBIGUOUS: 'Morally Ambiguous',
  ENSEMBLE: 'Ensemble Cast'
} as const

export const TARGET_AUDIENCES = {
  CHILDREN: 'Children (8-12)',
  YOUNG_ADULT: 'Young Adult (13-17)',
  NEW_ADULT: 'New Adult (18-25)',
  ADULT: 'Adult (25+)',
  ALL_AGES: 'All Ages'
} as const

export const CONTENT_RATINGS = {
  G: 'G (General)',
  PG: 'PG (Mild Content)',
  PG13: 'PG-13 (Moderate Content)',
  R: 'R (Mature Content)',
  NC17: 'NC-17 (Explicit Content)'
} as const

export const PROJECT_SCOPES = {
  STANDALONE: 'Standalone Novel',
  DUOLOGY: 'Duology',
  TRILOGY: 'Trilogy',
  SERIES: 'Series (4-7 books)',
  EPIC_SERIES: 'Epic Series (8+ books)',
  ANTHOLOGY: 'Anthology'
} as const

export const CHAPTER_STATUSES = {
  PLANNED: 'planned',
  WRITING: 'writing',
  REVIEW: 'review',
  COMPLETE: 'complete'
} as const

export const PROJECT_STATUSES = {
  PLANNING: 'planning',
  WRITING: 'writing',
  EDITING: 'editing',
  COMPLETE: 'complete'
} as const

export interface ProjectSelections {
  // Genre & Style
  primaryGenre?: string
  subgenre?: string
  customGenre?: string
  narrativeVoice?: string
  tense?: string
  toneOptions?: string[]
  writingStyle?: string
  customStyleDescription?: string
  
  // Story Structure & Pacing
  structureType?: string
  pacingPreference?: string
  chapterStructure?: string
  timelineComplexity?: string
  customStructureNotes?: string
  
  // Character & World Building
  protagonistTypes?: string[]
  antagonistTypes?: string[]
  characterComplexity?: string
  characterArcTypes?: string[]
  customCharacterConcepts?: string
  timePeriod?: string
  geographicSetting?: string
  worldType?: string
  magicTechLevel?: string
  customSettingDescription?: string
  
  // Themes & Content
  majorThemes?: string[]
  philosophicalThemes?: string[]
  socialThemes?: string[]
  customThemes?: string
  targetAudience?: string
  contentRating?: string
  contentWarnings?: string[]
  culturalSensitivityNotes?: string
  
  // Series & Scope
  projectScope?: string
  seriesType?: string
  interconnectionLevel?: string
  customScopeDescription?: string
  
  // Technical Specifications
  targetWordCount?: number
  targetChapters?: number
  chapterCountType?: string
  povCharacterCount?: number
  povCharacterType?: string
  
  // Research & References
  researchNeeds?: string[]
  factCheckingLevel?: string
  customResearchNotes?: string
}