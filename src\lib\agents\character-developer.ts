import { openai, defaultConfig } from '../openai'
import { 
  CharacterDeveloperInput, 
  CharacterProfile, 
  AgentResponse 
} from '../types/agents'

export class CharacterDeveloperAgent {
  private config = {
    ...defaultConfig,
    temperature: 0.8,
    maxTokens: 6000
  }

  async generateCharacters(input: CharacterDeveloperInput): Promise<AgentResponse<CharacterProfile[]>> {
    const startTime = Date.now()
    
    try {
      const systemPrompt = this.buildSystemPrompt(input)
      const userPrompt = this.buildUserPrompt(input)

      const response = await openai.chat.completions.create({
        model: this.config.model,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        response_format: { type: 'json_object' }
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response content received')
      }

      const result = JSON.parse(content)
      const characters = result.characters as CharacterProfile[]
      
      return {
        success: true,
        data: characters,
        executionTime: Date.now() - startTime,
        tokensUsed: response.usage?.total_tokens
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      }
    }
  }

  private buildSystemPrompt(input: CharacterDeveloperInput): string {
    return `You are an expert Character Developer AI agent specializing in creating compelling, multi-dimensional characters for novels. Your task is to develop a complete cast of characters that will drive the story forward and create engaging interpersonal dynamics.

GUIDELINES:
- Create characters that fit the genre (${input.storyStructure.genre})
- Ensure characters serve the story structure and themes
- Develop distinct character voices and personalities
- Create meaningful relationships and conflicts between characters
- Consider character arcs that span the entire novel
- Match character complexity to the story's requirements

CHARACTER TYPES TO CREATE:
- Protagonists: Main characters who drive the story
- Antagonists: Characters who create conflict and opposition
- Supporting Characters: Important secondary characters
- Minor Characters: Brief but memorable characters

CHARACTER DEVELOPMENT REQUIREMENTS:
- Unique and memorable names appropriate to the setting
- Detailed personality traits, motivations, fears, and goals
- Physical descriptions that aid visualization
- Rich backstories that inform present actions
- Character arcs showing growth and change
- Distinctive voice characteristics for dialogue
- Relationship dynamics with other characters

RESPONSE FORMAT:
Return a valid JSON object with a "characters" array containing CharacterProfile objects.
Each character must include all required fields: id, name, role, description, backstory, personality, physicalDescription, relationships, arc, and voiceCharacteristics.

Ensure characters are diverse, interesting, and serve specific functions in advancing the plot and themes.`
  }

  private buildUserPrompt(input: CharacterDeveloperInput): string {
    const storyStructure = input.storyStructure
    const selections = input.projectSelections
    
    return `Please create a comprehensive cast of characters for this story:

STORY STRUCTURE:
Title: ${storyStructure.title}
Genre: ${storyStructure.genre}
Themes: ${storyStructure.themes.join(', ')}

STORY ACTS:
${storyStructure.acts.map(act => `Act ${act.number}: ${act.title} - ${act.description}`).join('\n')}

CONFLICTS:
${storyStructure.conflicts.map(conflict => `${conflict.type}: ${conflict.description}`).join('\n')}

PROJECT SELECTIONS:
- Character Complexity: ${selections.characterComplexity}
- Protagonist Types: ${selections.protagonistTypes?.join(', ') || 'Not specified'}
- Antagonist Types: ${selections.antagonistTypes?.join(', ') || 'Not specified'}
- Character Arc Types: ${selections.characterArcTypes?.join(', ') || 'Not specified'}
- Narrative Voice: ${selections.narrativeVoice}
- Target Audience: ${selections.targetAudience}
- Content Rating: ${selections.contentRating}

CUSTOM CHARACTER CONCEPTS:
${selections.customCharacterConcepts || 'None specified'}

CHARACTER REQUIREMENTS:
${input.characterRequirements ? `
- Protagonists needed: ${input.characterRequirements.protagonistCount}
- Antagonists needed: ${input.characterRequirements.antagonistCount}  
- Supporting characters needed: ${input.characterRequirements.supportingCount}
` : 'Create an appropriate number of characters for the story scope.'}

SETTING CONTEXT:
- Time Period: ${selections.timePeriod}
- Geographic Setting: ${selections.geographicSetting}
- World Type: ${selections.worldType}
- Magic/Tech Level: ${selections.magicTechLevel}

Create characters that will bring this story to life, ensuring each serves a specific purpose in the narrative while being compelling individuals in their own right.`
  }
}