'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  BookOpen, 
  Plus, 
  Search, 
  Users, 
  Heart,
  Sparkles,
  Zap,
  Crown,
  Download,
  Edit,
  Trash2
} from 'lucide-react';
import type { ProjectSettings } from '@/lib/types/project-settings';

interface SelectionProfileDisplay {
  id: string;
  name: string;
  description: string;
  category: string;
  isPublic: boolean;
  isFeatured: boolean;
  settings: ProjectSettings;
  usageCount: number;
  tags: string[];
  createdAt: Date;
  createdBy?: string;
}

interface SelectionProfilesProps {
  userId: string;
  onSelectProfile: (profile: SelectionProfileDisplay) => void;
  onCreateNew: () => void;
}

export function SelectionProfiles({ userId, onSelectProfile, onCreateNew }: SelectionProfilesProps) {
  const [profiles, setProfiles] = useState<SelectionProfileDisplay[]>([]);
  const [userProfiles, setUserProfiles] = useState<SelectionProfileDisplay[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // Fetch profiles
  useEffect(() => {
    const fetchProfiles = async () => {
      try {
        const [publicResponse, userResponse] = await Promise.all([
          fetch('/api/profiles/public'),
          fetch(`/api/profiles/user?userId=${userId}`)
        ]);

        if (publicResponse.ok) {
          const publicData = await publicResponse.json();
          setProfiles(publicData.profiles);
        }

        if (userResponse.ok) {
          const userData = await userResponse.json();
          setUserProfiles(userData.profiles);
        }
      } catch (error) {
        console.error('Error fetching profiles:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfiles();
  }, [userId]);

  // Featured profiles - curated templates
  const featuredProfiles: SelectionProfileDisplay[] = [
    {
      id: 'featured_fantasy_epic',
      name: 'Epic Fantasy Adventure',
      description: 'Classic high fantasy with magic systems, chosen heroes, and world-ending threats',
      category: 'Fantasy',
      isPublic: true,
      isFeatured: true,
      usageCount: 1247,
      tags: ['fantasy', 'magic', 'adventure', 'heroes'],
      createdAt: new Date('2024-01-01'),
      settings: {
        projectName: 'Epic Fantasy Adventure',
        description: 'A classic high fantasy tale',
        initialConcept: 'Heroes journey in magical world',
        primaryGenre: 'fantasy',
        subgenre: 'epic_fantasy',
        narrativeVoice: 'third_person_limited',
        tense: 'past',
        writingStyle: 'descriptive',
        tone: ['epic_heroic'],
        structureType: 'three_act',
        pacingPreference: 'balanced',
        chapterStructure: 'fixed_length',
        timelineComplexity: 'linear',
        protagonistTypes: ['the_hero'],
        antagonistTypes: ['the_villain'],
        characterComplexity: 'complex_layered',
        characterArcTypes: ['positive_change'],
        timePeriod: 'timeless',
        geographicSetting: 'wilderness',
        worldType: 'fantasy_world',
        magicTechLevel: 'high_magic_advanced_tech',
        majorThemes: ['good_vs_evil', 'coming_of_age'],
        philosophicalThemes: [],
        socialThemes: [],
        contentWarnings: [],
        targetAudience: 'young_adult_13_17',
        contentRating: 'PG13',
        projectScope: 'trilogy',
        targetWordCount: 120000,
        chapterCountType: 'fixed',
        povCharacterCount: 1,
        povCharacterType: 'single_pov',
        researchNeeds: [],
        factCheckingLevel: 'minimal',
      }
    },
    {
      id: 'featured_scifi_thriller',
      name: 'Sci-Fi Thriller',
      description: 'Near-future technological thriller with AI, cyberpunk elements, and moral dilemmas',
      category: 'Science Fiction',
      isPublic: true,
      isFeatured: true,
      usageCount: 892,
      tags: ['sci-fi', 'technology', 'thriller', 'cyberpunk'],
      createdAt: new Date('2024-01-01'),
      settings: {
        projectName: 'Sci-Fi Thriller',
        description: 'Near-future technological thriller',
        initialConcept: 'AI ethics and human identity',
        primaryGenre: 'science_fiction',
        subgenre: 'cyberpunk',
        narrativeVoice: 'first_person',
        tense: 'present',
        writingStyle: 'commercial',
        tone: ['dark_gritty', 'mysterious_suspenseful'],
        structureType: 'three_act',
        pacingPreference: 'fast_paced',
        chapterStructure: 'variable_length',
        timelineComplexity: 'linear',
        protagonistTypes: ['the_antihero'],
        antagonistTypes: ['the_shadow'],
        characterComplexity: 'morally_ambiguous',
        characterArcTypes: ['corruption_arc'],
        timePeriod: 'near_future',
        geographicSetting: 'urban',
        worldType: 'cyberpunk',
        magicTechLevel: 'high_magic_advanced_tech',
        majorThemes: ['power_corruption', 'identity'],
        philosophicalThemes: ['technology_vs_humanity'],
        socialThemes: ['class_struggle'],
        contentWarnings: ['violence_moderate'],
        targetAudience: 'adult_25_plus',
        contentRating: 'R',
        projectScope: 'standalone',
        targetWordCount: 85000,
        chapterCountType: 'fixed',
        povCharacterCount: 1,
        povCharacterType: 'single_pov',
        researchNeeds: ['technical_expertise'],
        factCheckingLevel: 'moderate',
      }
    },
    {
      id: 'featured_romance_contemporary',
      name: 'Contemporary Romance',
      description: 'Modern romantic fiction with relatable characters and emotional depth',
      category: 'Romance',
      isPublic: true,
      isFeatured: true,
      usageCount: 2156,
      tags: ['romance', 'contemporary', 'relationships', 'love'],
      createdAt: new Date('2024-01-01'),
      settings: {
        projectName: 'Contemporary Romance',
        description: 'Modern romantic fiction',
        initialConcept: 'Finding love in unexpected places',
        primaryGenre: 'romance',
        subgenre: 'contemporary_romance',
        narrativeVoice: 'first_person',
        tense: 'present',
        writingStyle: 'literary',
        tone: ['intimate_personal', 'romantic_passionate'],
        structureType: 'three_act',
        pacingPreference: 'character_driven',
        chapterStructure: 'scene_based',
        timelineComplexity: 'linear',
        protagonistTypes: ['the_everyman'],
        antagonistTypes: ['the_rival'],
        characterComplexity: 'complex_layered',
        characterArcTypes: ['positive_change'],
        timePeriod: 'contemporary',
        geographicSetting: 'urban',
        worldType: 'real_world',
        magicTechLevel: 'no_magic_current_tech',
        majorThemes: ['love_relationships', 'identity'],
        philosophicalThemes: [],
        socialThemes: [],
        contentWarnings: ['sexual_content_mild'],
        targetAudience: 'adult_25_plus',
        contentRating: 'PG13',
        projectScope: 'standalone',
        targetWordCount: 75000,
        chapterCountType: 'fixed',
        povCharacterCount: 2,
        povCharacterType: 'dual_pov',
        researchNeeds: [],
        factCheckingLevel: 'minimal',
      }
    },
    {
      id: 'featured_mystery_cozy',
      name: 'Cozy Mystery',
      description: 'Small-town mystery with amateur detective and community setting',
      category: 'Mystery',
      isPublic: true,
      isFeatured: true,
      usageCount: 734,
      tags: ['mystery', 'cozy', 'amateur detective', 'small town'],
      createdAt: new Date('2024-01-01'),
      settings: {
        projectName: 'Cozy Mystery',
        description: 'Small-town mystery series',
        initialConcept: 'Amateur detective solves local mysteries',
        primaryGenre: 'mystery_thriller',
        subgenre: 'cozy_mystery',
        narrativeVoice: 'first_person',
        tense: 'past',
        writingStyle: 'commercial',
        tone: ['light_humorous', 'mysterious_suspenseful'],
        structureType: 'three_act',
        pacingPreference: 'balanced',
        chapterStructure: 'fixed_length',
        timelineComplexity: 'linear',
        protagonistTypes: ['the_innocent'],
        antagonistTypes: ['the_villain'],
        characterComplexity: 'simple_archetypal',
        characterArcTypes: ['positive_change'],
        timePeriod: 'contemporary',
        geographicSetting: 'rural',
        worldType: 'real_world',
        magicTechLevel: 'no_magic_current_tech',
        majorThemes: ['justice', 'family'],
        philosophicalThemes: ['morality'],
        socialThemes: [],
        contentWarnings: [],
        targetAudience: 'adult_25_plus',
        contentRating: 'PG',
        projectScope: 'series_4_7',
        targetWordCount: 65000,
        chapterCountType: 'fixed',
        povCharacterCount: 1,
        povCharacterType: 'single_pov',
        researchNeeds: [],
        factCheckingLevel: 'minimal',
      }
    }
  ];

  const allProfiles = [...featuredProfiles, ...profiles, ...userProfiles];

  // Filter profiles
  const filteredProfiles = allProfiles.filter(profile => {
    const matchesSearch = profile.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         profile.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         profile.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || profile.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'fantasy': return <Sparkles className="h-4 w-4" />;
      case 'science fiction': return <Zap className="h-4 w-4" />;
      case 'romance': return <Heart className="h-4 w-4" />;
      case 'mystery': return <Search className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  const handleUseProfile = async (profile: SelectionProfileDisplay) => {
    try {
      // Increment usage count if it's a public profile
      if (profile.isPublic) {
        await fetch(`/api/profiles/${profile.id}/use`, { method: 'POST' });
      }
      onSelectProfile(profile);
    } catch (error) {
      console.error('Error using profile:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <BookOpen className="h-8 w-8 animate-pulse mx-auto mb-4 text-blue-500" />
          <p className="text-slate-600 dark:text-slate-400">Loading templates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Project Templates</h2>
          <p className="text-slate-600 dark:text-slate-400">
            Start with a template or create from scratch
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={onCreateNew} variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Start Fresh
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Save as Template
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Template</DialogTitle>
                <DialogDescription>
                  Save your current project settings as a reusable template
                </DialogDescription>
              </DialogHeader>
              <CreateProfileForm 
                userId={userId} 
                onClose={() => setIsCreateDialogOpen(false)} 
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="Fantasy">Fantasy</SelectItem>
            <SelectItem value="Science Fiction">Science Fiction</SelectItem>
            <SelectItem value="Romance">Romance</SelectItem>
            <SelectItem value="Mystery">Mystery</SelectItem>
            <SelectItem value="Literary Fiction">Literary Fiction</SelectItem>
            <SelectItem value="Thriller">Thriller</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      <Tabs defaultValue="featured" className="space-y-4">
        <TabsList>
          <TabsTrigger value="featured">Featured</TabsTrigger>
          <TabsTrigger value="public">Community</TabsTrigger>
          <TabsTrigger value="personal">My Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="featured" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {featuredProfiles.filter(profile => 
              selectedCategory === 'all' || profile.category === selectedCategory
            ).map((profile) => (
              <Card key={profile.id} className="hover:shadow-lg transition-shadow border-2 border-amber-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <Crown className="h-5 w-5 text-amber-600" />
                      <Badge variant="secondary" className="bg-amber-100 text-amber-800">
                        Featured
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-slate-500">
                      <Users className="h-3 w-3" />
                      {profile.usageCount.toLocaleString()}
                    </div>
                  </div>
                  <CardTitle className="text-lg">{profile.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {profile.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(profile.category)}
                    <span className="text-sm font-medium">{profile.category}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {profile.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {profile.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{profile.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                  <Button 
                    onClick={() => handleUseProfile(profile)}
                    className="w-full"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Use Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="public" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {profiles.filter(profile => 
              selectedCategory === 'all' || profile.category === selectedCategory
            ).map((profile) => (
              <Card key={profile.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-1 text-sm text-slate-500">
                      <Users className="h-3 w-3" />
                      {profile.usageCount}
                    </div>
                  </div>
                  <CardTitle className="text-lg">{profile.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {profile.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(profile.category)}
                    <span className="text-sm font-medium">{profile.category}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {profile.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <Button 
                    onClick={() => handleUseProfile(profile)}
                    className="w-full"
                    variant="outline"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Use Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="personal" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {userProfiles.map((profile) => (
              <Card key={profile.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <Badge variant="outline">Personal</Badge>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <CardTitle className="text-lg">{profile.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {profile.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(profile.category)}
                    <span className="text-sm font-medium">{profile.category}</span>
                  </div>
                  <Button 
                    onClick={() => handleUseProfile(profile)}
                    className="w-full"
                    variant="outline"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Use Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {filteredProfiles.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 mx-auto mb-4 text-slate-400" />
          <h3 className="text-lg font-medium mb-2">No templates found</h3>
          <p className="text-slate-600 dark:text-slate-400 mb-4">
            Try adjusting your search or filters
          </p>
        </div>
      )}
    </div>
  );
}

function CreateProfileForm({ userId, onClose }: { userId: string; onClose: () => void }) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [isPublic] = useState(false);
  const [tags, setTags] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/profiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          name,
          description,
          category,
          isPublic,
          tags: tags.split(',').map(t => t.trim()).filter(Boolean),
          settings: {}, // Current project settings would be passed here
        }),
      });

      if (response.ok) {
        onClose();
      }
    } catch (error) {
      console.error('Error creating profile:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="text-sm font-medium">Template Name</label>
        <Input
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="My Custom Template"
          required
        />
      </div>
      
      <div>
        <label className="text-sm font-medium">Description</label>
        <Textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Describe what makes this template unique..."
          rows={3}
        />
      </div>

      <div>
        <label className="text-sm font-medium">Category</label>
        <Select value={category} onValueChange={setCategory} required>
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Fantasy">Fantasy</SelectItem>
            <SelectItem value="Science Fiction">Science Fiction</SelectItem>
            <SelectItem value="Romance">Romance</SelectItem>
            <SelectItem value="Mystery">Mystery</SelectItem>
            <SelectItem value="Literary Fiction">Literary Fiction</SelectItem>
            <SelectItem value="Custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <label className="text-sm font-medium">Tags (comma-separated)</label>
        <Input
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          placeholder="fantasy, magic, adventure"
        />
      </div>

      <div className="flex items-center justify-between pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit">
          Create Template
        </Button>
      </div>
    </form>
  );
}