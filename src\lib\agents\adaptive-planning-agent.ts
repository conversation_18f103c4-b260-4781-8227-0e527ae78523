import { BaseAgent } from './base-agent';
import { BookContext } from './types';

export interface UserChanges {
  chapterId: string;
  changes: Array<{
    type: 'plot' | 'character' | 'pacing' | 'style' | 'dialogue' | 'setting' | 'other';
    description: string;
    impact: 'low' | 'medium' | 'high';
    affectedElements: string[];
    textChange: {
      from: string;
      to: string;
      startIndex: number;
      endIndex: number;
    };
  }>;
  userNotes: string;
  significantChanges: string[];
  requestsPlanUpdate: boolean;
}

export interface PlanAdjustment {
  type: 'character_development' | 'plot_revision' | 'pacing_adjustment' | 'theme_modification' | 'world_building';
  severity: 'minor' | 'moderate' | 'major';
  description: string;
  affectedChapters: number[];
  specificChanges: {
    chapterNumber: number;
    adjustments: {
      field: string;
      oldValue: unknown;
      newValue: unknown;
      reason: string;
    }[];
  }[];
  characterStateUpdates: {
    characterId: string;
    updates: {
      emotionalState?: string;
      knowledge?: string[];
      relationships?: string;
      arcProgression?: string;
    };
  }[];
  plotThreadUpdates: {
    threadId: string;
    updates: {
      status?: string;
      description?: string;
      nextMilestone?: string;
    };
  }[];
  worldRuleUpdates: {
    rule: string;
    oldValue: string;
    newValue: string;
    reason: string;
  }[];
}

export interface AdaptivePlanningResult {
  needsAdjustment: boolean;
  adjustments: PlanAdjustment[];
  confidence: number;
  reasoning: string;
  futureChapterImpacts: {
    chapterNumber: number;
    impactLevel: 'low' | 'medium' | 'high';
    description: string;
    recommendedActions: string[];
  }[];
}

export class AdaptivePlanningAgent extends BaseAgent {
  constructor(context: BookContext) {
    super(context);
  }

  async analyzeUserChanges(
    userChanges: UserChanges,
    projectContext: {
      projectId: string;
      completedChapters: Array<{
        chapterNumber: number;
        content: string;
        wordCount: number;
      }>;
      upcomingChapters: Array<{
        chapterNumber: number;
        title: string;
        summary: string;
        objectives: string[];
        conflicts: string[];
        scenes: Array<Record<string, unknown>>;
        characterStates: Array<Record<string, unknown>>;
        plotAdvancement: string[];
      }>;
      characters: Array<{
        id: string;
        name: string;
        role: string;
        arc: Record<string, unknown>;
        currentState: Record<string, unknown>;
      }>;
      storyBible: {
        worldRules: Record<string, string>;
        plotThreads: Array<{
          id: string;
          description: string;
          status: string;
          nextMilestone: string;
        }>;
        timeline: Array<{
          event: string;
          chapter: number;
        }>;
      };
      projectSettings: {
        primaryGenre: string;
        structureType: string;
        pacingPreference: string;
        targetWordCount: number;
        targetChapters: number;
      };
    }
  ): Promise<AdaptivePlanningResult> {
    try {
      // Don't proceed if user doesn't request plan updates
      if (!userChanges.requestsPlanUpdate || userChanges.changes.length === 0) {
        return {
          needsAdjustment: false,
          adjustments: [],
          confidence: 1.0,
          reasoning: 'No plan update requested or no significant changes detected.',
          futureChapterImpacts: []
        };
      }

      const analysisPrompt = this.buildAnalysisPrompt(userChanges, projectContext);
      
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: analysisPrompt
          }
        ],
        tools: [
          {
            type: 'function',
            function: {
              name: 'analyze_plan_adjustments',
              description: 'Analyze user changes and recommend plan adjustments',
              parameters: {
                type: 'object',
                properties: {
                  needsAdjustment: { type: 'boolean' },
                  confidence: { type: 'number', minimum: 0, maximum: 1 },
                  reasoning: { type: 'string' },
                  adjustments: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        type: { 
                          type: 'string',
                          enum: ['character_development', 'plot_revision', 'pacing_adjustment', 'theme_modification', 'world_building']
                        },
                        severity: { type: 'string', enum: ['minor', 'moderate', 'major'] },
                        description: { type: 'string' },
                        affectedChapters: { type: 'array', items: { type: 'number' } },
                        specificChanges: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              chapterNumber: { type: 'number' },
                              adjustments: {
                                type: 'array',
                                items: {
                                  type: 'object',
                                  properties: {
                                    field: { type: 'string' },
                                    oldValue: {},
                                    newValue: {},
                                    reason: { type: 'string' }
                                  }
                                }
                              }
                            }
                          }
                        },
                        characterStateUpdates: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              characterId: { type: 'string' },
                              updates: {
                                type: 'object',
                                properties: {
                                  emotionalState: { type: 'string' },
                                  knowledge: { type: 'array', items: { type: 'string' } },
                                  relationships: { type: 'string' },
                                  arcProgression: { type: 'string' }
                                }
                              }
                            }
                          }
                        },
                        plotThreadUpdates: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              threadId: { type: 'string' },
                              updates: {
                                type: 'object',
                                properties: {
                                  status: { type: 'string' },
                                  description: { type: 'string' },
                                  nextMilestone: { type: 'string' }
                                }
                              }
                            }
                          }
                        },
                        worldRuleUpdates: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              rule: { type: 'string' },
                              oldValue: { type: 'string' },
                              newValue: { type: 'string' },
                              reason: { type: 'string' }
                            }
                          }
                        }
                      },
                      required: ['type', 'severity', 'description', 'affectedChapters']
                    }
                  },
                  futureChapterImpacts: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        chapterNumber: { type: 'number' },
                        impactLevel: { type: 'string', enum: ['low', 'medium', 'high'] },
                        description: { type: 'string' },
                        recommendedActions: { type: 'array', items: { type: 'string' } }
                      }
                    }
                  }
                },
                required: ['needsAdjustment', 'confidence', 'reasoning', 'adjustments', 'futureChapterImpacts']
              }
            }
          }
        ],
        tool_choice: { type: 'function', function: { name: 'analyze_plan_adjustments' } },
        temperature: 0.3
      });

      const toolCall = completion.choices[0]?.message?.tool_calls?.[0];
      if (!toolCall || toolCall.function.name !== 'analyze_plan_adjustments') {
        throw new Error('Failed to get structured analysis from AI');
      }

      const result = JSON.parse(toolCall.function.arguments) as AdaptivePlanningResult;
      
      // Validate and enhance the result
      return this.validateAndEnhanceResult(result, userChanges, projectContext);

    } catch (error) {
      console.error('Adaptive planning analysis failed:', error);
      
      // Return a safe fallback result
      return {
        needsAdjustment: false,
        adjustments: [],
        confidence: 0.0,
        reasoning: `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        futureChapterImpacts: []
      };
    }
  }

  private getSystemPrompt(): string {
    return `You are an expert adaptive story planning AI. Your role is to analyze user edits to AI-generated chapters and determine how these changes should impact future chapter plans.

CORE RESPONSIBILITIES:
1. Analyze user changes to understand their narrative intent
2. Identify how changes impact character development, plot progression, pacing, and world-building
3. Recommend specific adjustments to upcoming chapter outlines
4. Update character states and plot thread progressions
5. Maintain story consistency and narrative coherence

ANALYSIS PRINCIPLES:
- User changes always reflect important creative decisions
- Character development changes have cascading effects on future chapters
- Plot modifications require careful adjustment of upcoming conflicts and resolutions
- Pacing changes affect word count distribution and scene planning
- World-building changes must be consistently applied throughout remaining chapters

ADJUSTMENT SEVERITY GUIDELINES:
- Minor: Cosmetic changes, small dialogue adjustments, minor character details
- Moderate: Character relationship changes, subplot modifications, pacing adjustments
- Major: Core plot changes, character arc modifications, major world-building updates

Always prioritize story consistency and character development coherence in your recommendations.`;
  }

  private buildAnalysisPrompt(userChanges: UserChanges, projectContext: Record<string, unknown>): string {
    const currentChapter = (projectContext.completedChapters as Array<Record<string, unknown>>).find((ch: Record<string, unknown>) => 
      (ch.chapterNumber as number) === Math.max(...(projectContext.completedChapters as Array<Record<string, unknown>>).map((c: Record<string, unknown>) => c.chapterNumber as number))
    );

    return `
ANALYZE USER CHANGES TO STORY PLAN

USER CHANGES ANALYSIS:
Chapter Modified: ${currentChapter?.chapterNumber || 'Unknown'}
Change Categories: ${userChanges.changes.map(c => c.type).join(', ')}
Significant Changes:
${userChanges.significantChanges.map(change => `- ${change}`).join('\n')}

Detailed Changes:
${userChanges.changes.map(change => `
Type: ${change.type} (${change.impact} impact)
Description: ${change.description}
Affected Elements: ${change.affectedElements.join(', ')}
Text Change: "${change.textChange.from}" → "${change.textChange.to}"
`).join('\n')}

User Notes: ${userChanges.userNotes || 'None provided'}

CURRENT STORY CONTEXT:

Characters and Their Arcs:
${(projectContext.characters as Array<Record<string, unknown>>).map((char: Record<string, unknown>) => `
${char.name} (${char.role}):
Current Arc Stage: ${(char.arc as Record<string, unknown>)?.currentStage || 'Unknown'}
Current Emotional State: ${(char.currentState as Record<string, unknown>)?.emotionalState || 'Unknown'}
Current Knowledge: ${((char.currentState as Record<string, unknown>)?.knowledge as string[])?.join(', ') || 'Unknown'}
`).join('\n')}

Active Plot Threads:
${((projectContext.storyBible as Record<string, unknown>).plotThreads as Array<Record<string, unknown>>).map((thread: Record<string, unknown>) => `
${thread.id}: ${thread.description}
Status: ${thread.status}
Next Milestone: ${thread.nextMilestone}
`).join('\n')}

Upcoming Chapters (Next 5):
${((projectContext.upcomingChapters as Array<Record<string, unknown>>).slice(0, 5)).map((chapter: Record<string, unknown>) => `
Chapter ${chapter.chapterNumber}: ${chapter.title}
Summary: ${chapter.summary}
Objectives: ${(chapter.objectives as string[]).join(', ')}
Key Conflicts: ${(chapter.conflicts as string[]).join(', ')}
Plot Advancement: ${(chapter.plotAdvancement as string[]).join(', ')}
Characters Featured: ${((chapter.scenes as Array<Record<string, unknown>>)?.flatMap((scene: Record<string, unknown>) => scene.characters as string[]) || [])?.join(', ') || 'Unknown'}
`).join('\n')}

World Rules:
${Object.entries((projectContext.storyBible as Record<string, unknown>).worldRules as Record<string, string>).map(([rule, value]) => `${rule}: ${value}`).join('\n')}

STORY STRUCTURE:
Genre: ${(projectContext.projectSettings as Record<string, unknown>).primaryGenre}
Structure Type: ${(projectContext.projectSettings as Record<string, unknown>).structureType}
Pacing Preference: ${(projectContext.projectSettings as Record<string, unknown>).pacingPreference}
Target Length: ${(projectContext.projectSettings as Record<string, unknown>).targetWordCount} words, ${(projectContext.projectSettings as Record<string, unknown>).targetChapters} chapters

TASK:
Analyze how the user's changes impact the story's future development. Consider:

1. CHARACTER DEVELOPMENT IMPACTS:
   - How do character changes affect their arcs in upcoming chapters?
   - Do relationship changes require adjustment of character interactions?
   - Are there new character knowledge/emotional states that affect future scenes?

2. PLOT PROGRESSION IMPACTS:
   - Do plot changes require adjustment of upcoming conflicts or resolutions?
   - Are there new plot threads introduced that need future development?
   - Do any existing plot threads need status updates?

3. PACING AND STRUCTURE IMPACTS:
   - Do word count changes affect the overall pacing plan?
   - Should scene structures in upcoming chapters be adjusted?
   - Are there new tensions or reveals that need proper setup?

4. WORLD-BUILDING IMPACTS:
   - Do any world rule changes need consistent application?
   - Are there new locations or elements that affect future chapters?
   - Do magic system or technology changes impact planned scenes?

5. THEMATIC IMPACTS:
   - Do tonal changes require adjustment of upcoming chapter themes?
   - Are there new thematic elements that need development?
   - Should character voice consistency be maintained differently?

Provide specific, actionable adjustments that maintain story coherence while honoring the user's creative vision.
    `;
  }

  private validateAndEnhanceResult(
    result: AdaptivePlanningResult,
    userChanges: UserChanges,
    projectContext: Record<string, unknown>
  ): AdaptivePlanningResult {
    // Ensure confidence is within valid range
    result.confidence = Math.max(0, Math.min(1, result.confidence));

    // Validate affected chapters exist
    result.adjustments.forEach(adjustment => {
      adjustment.affectedChapters = adjustment.affectedChapters.filter(chapterNum => 
        (projectContext.upcomingChapters as Array<Record<string, unknown>>).some((ch: Record<string, unknown>) => (ch.chapterNumber as number) === chapterNum)
      );
    });

    // Ensure character updates reference existing characters
    result.adjustments.forEach(adjustment => {
      if (adjustment.characterStateUpdates) {
        adjustment.characterStateUpdates = adjustment.characterStateUpdates.filter(update =>
          (projectContext.characters as Array<Record<string, unknown>>).some((char: Record<string, unknown>) => char.id === update.characterId)
        );
      }
    });

    // Ensure plot thread updates reference existing threads
    result.adjustments.forEach(adjustment => {
      if (adjustment.plotThreadUpdates) {
        adjustment.plotThreadUpdates = adjustment.plotThreadUpdates.filter(update =>
          ((projectContext.storyBible as Record<string, unknown>).plotThreads as Array<Record<string, unknown>>).some((thread: Record<string, unknown>) => thread.id === update.threadId)
        );
      }
    });

    // Filter future chapter impacts to valid chapters
    result.futureChapterImpacts = result.futureChapterImpacts.filter(impact =>
      (projectContext.upcomingChapters as Array<Record<string, unknown>>).some((ch: Record<string, unknown>) => (ch.chapterNumber as number) === impact.chapterNumber)
    );

    // Add any missing high-impact changes based on user changes
    const highImpactChanges = userChanges.changes.filter(change => change.impact === 'high');
    
    if (highImpactChanges.length > 0 && result.adjustments.length === 0) {
      // Generate a basic adjustment for high-impact changes
      result.needsAdjustment = true;
      result.adjustments.push({
        type: 'character_development',
        severity: 'moderate',
        description: `High-impact user changes detected requiring plan review: ${highImpactChanges.map(c => c.description).join(', ')}`,
        affectedChapters: ((projectContext.upcomingChapters as Array<Record<string, unknown>>).slice(0, 3)).map((ch: Record<string, unknown>) => ch.chapterNumber as number),
        specificChanges: [],
        characterStateUpdates: [],
        plotThreadUpdates: [],
        worldRuleUpdates: []
      });
    }

    return result;
  }

  async execute(): Promise<Record<string, unknown>> {
    // This method is required by BaseAgent but not used in this implementation
    // The main functionality is in analyzeUserChanges
    throw new Error('Use analyzeUserChanges method instead of execute');
  }
}