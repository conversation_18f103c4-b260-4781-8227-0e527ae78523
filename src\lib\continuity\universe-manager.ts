export interface SharedUniverse {
  id: string;
  name: string;
  description: string;
  creator: string;
  projects: string[];
  sharedElements: SharedElement[];
  continuityRules: ContinuityRule[];
  timeline: UniverseTimeline;
  created: Date;
  lastUpdated: Date;
}

export interface SharedElement {
  id: string;
  type: 'character' | 'location' | 'event' | 'organization' | 'technology' | 'concept';
  name: string;
  description: string;
  canonicalVersion: ElementVersion;
  versions: ElementVersion[];
  usageRules: string[];
  conflicts: ElementConflict[];
}

export interface ElementVersion {
  id: string;
  projectId: string;
  version: string;
  description: string;
  properties: Record<string, unknown>;
  timestamp: Date;
  approved: boolean;
}

export interface ElementConflict {
  id: string;
  conflictingVersions: string[];
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  autoResolvable: boolean;
  resolution?: string;
}

export interface ContinuityRule {
  id: string;
  name: string;
  description: string;
  elementTypes: string[];
  ruleType: 'constraint' | 'dependency' | 'exclusion' | 'temporal';
  conditions: RuleCondition[];
  active: boolean;
}

export interface RuleCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
  value: unknown;
}

export interface UniverseTimeline {
  epochs: TimelineEpoch[];
  events: CrossProjectEvent[];
  references: TemporalReference[];
}

export interface TimelineEpoch {
  id: string;
  name: string;
  startDate: Date;
  endDate?: Date;
  description: string;
  majorEvents: string[];
}

export interface CrossProjectEvent {
  id: string;
  name: string;
  description: string;
  date: Date;
  relatedProjects: string[];
  importance: number;
}

export interface TemporalReference {
  id: string;
  projectId: string;
  chapterNumber: number;
  reference: string;
  linkedEvent?: string;
}

export class UniverseManager {
  private universes = new Map<string, SharedUniverse>();
  private projectUniverseMap = new Map<string, string>();

  async createUniverse(
    name: string,
    description: string,
    creator: string
  ): Promise<string> {
    const id = `universe_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const universe: SharedUniverse = {
      id,
      name,
      description,
      creator,
      projects: [],
      sharedElements: [],
      continuityRules: [],
      timeline: {
        epochs: [],
        events: [],
        references: []
      },
      created: new Date(),
      lastUpdated: new Date()
    };

    this.universes.set(id, universe);
    return id;
  }

  async addProjectToUniverse(universeId: string, projectId: string): Promise<boolean> {
    const universe = this.universes.get(universeId);
    if (!universe) return false;

    if (!universe.projects.includes(projectId)) {
      universe.projects.push(projectId);
      this.projectUniverseMap.set(projectId, universeId);
      universe.lastUpdated = new Date();
    }

    return true;
  }

  async addSharedElement(
    universeId: string,
    element: Omit<SharedElement, 'id' | 'versions' | 'conflicts'>
  ): Promise<string> {
    const universe = this.universes.get(universeId);
    if (!universe) throw new Error('Universe not found');

    const id = `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const sharedElement: SharedElement = {
      ...element,
      id,
      versions: [element.canonicalVersion],
      conflicts: []
    };

    universe.sharedElements.push(sharedElement);
    universe.lastUpdated = new Date();

    return id;
  }

  async updateElementFromProject(
    projectId: string,
    elementId: string,
    newVersion: Omit<ElementVersion, 'id' | 'timestamp'>
  ): Promise<void> {
    const universeId = this.projectUniverseMap.get(projectId);
    if (!universeId) return;

    const universe = this.universes.get(universeId);
    if (!universe) return;

    const element = universe.sharedElements.find(e => e.id === elementId);
    if (!element) return;

    const versionId = `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const version: ElementVersion = {
      ...newVersion,
      id: versionId,
      timestamp: new Date(),
      approved: false
    };

    element.versions.push(version);

    // Check for conflicts
    await this.detectElementConflicts(element);
    
    universe.lastUpdated = new Date();
  }

  async detectContinuityIssues(projectId: string): Promise<{
    violations: ContinuityViolation[];
    suggestions: string[];
    conflicts: ElementConflict[];
  }> {
    const universeId = this.projectUniverseMap.get(projectId);
    if (!universeId) {
      return { violations: [], suggestions: [], conflicts: [] };
    }

    const universe = this.universes.get(universeId);
    if (!universe) {
      return { violations: [], suggestions: [], conflicts: [] };
    }

    const violations: ContinuityViolation[] = [];
    const suggestions: string[] = [];
    const conflicts: ElementConflict[] = [];

    // Check rule violations
    for (const rule of universe.continuityRules) {
      if (!rule.active) continue;

      const ruleViolations = await this.checkRuleViolations(rule, projectId, universe);
      violations.push(...ruleViolations);
    }

    // Check element conflicts
    for (const element of universe.sharedElements) {
      const elementConflicts = element.conflicts.filter(conflict =>
        conflict.conflictingVersions.some(versionId =>
          element.versions.find(v => v.id === versionId && v.projectId === projectId)
        )
      );
      conflicts.push(...elementConflicts);
    }

    // Generate suggestions
    if (violations.length > 0) {
      suggestions.push('Review continuity violations and adjust story elements accordingly');
    }
    if (conflicts.length > 0) {
      suggestions.push('Resolve element conflicts with other projects in the universe');
    }

    return { violations, suggestions, conflicts };
  }

  async getSharedElementsForProject(projectId: string): Promise<SharedElement[]> {
    const universeId = this.projectUniverseMap.get(projectId);
    if (!universeId) return [];

    const universe = this.universes.get(universeId);
    if (!universe) return [];

    return universe.sharedElements.filter(element =>
      element.versions.some(version => version.projectId === projectId) ||
      element.usageRules.length === 0 // Available to all projects
    );
  }

  async synchronizeElement(
    elementId: string,
    canonicalVersionId: string
  ): Promise<boolean> {
    const universe = Array.from(this.universes.values()).find(u =>
      u.sharedElements.some(e => e.id === elementId)
    );

    if (!universe) return false;

    const element = universe.sharedElements.find(e => e.id === elementId);
    if (!element) return false;

    const canonicalVersion = element.versions.find(v => v.id === canonicalVersionId);
    if (!canonicalVersion) return false;

    element.canonicalVersion = canonicalVersion;
    element.conflicts = element.conflicts.filter(c => 
      !c.conflictingVersions.includes(canonicalVersionId)
    );

    universe.lastUpdated = new Date();
    return true;
  }

  async addContinuityRule(
    universeId: string,
    rule: Omit<ContinuityRule, 'id'>
  ): Promise<string> {
    const universe = this.universes.get(universeId);
    if (!universe) throw new Error('Universe not found');

    const id = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const continuityRule: ContinuityRule = {
      ...rule,
      id
    };

    universe.continuityRules.push(continuityRule);
    universe.lastUpdated = new Date();

    return id;
  }

  async getUniverseTimeline(universeId: string): Promise<UniverseTimeline | null> {
    const universe = this.universes.get(universeId);
    return universe ? universe.timeline : null;
  }

  async addUniverseEvent(
    universeId: string,
    event: Omit<CrossProjectEvent, 'id'>
  ): Promise<string> {
    const universe = this.universes.get(universeId);
    if (!universe) throw new Error('Universe not found');

    const id = `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const universeEvent: CrossProjectEvent = {
      ...event,
      id
    };

    universe.timeline.events.push(universeEvent);
    universe.lastUpdated = new Date();

    return id;
  }

  getUniverseForProject(projectId: string): SharedUniverse | null {
    const universeId = this.projectUniverseMap.get(projectId);
    return universeId ? this.universes.get(universeId) || null : null;
  }

  getAllUniverses(): SharedUniverse[] {
    return Array.from(this.universes.values());
  }

  private async detectElementConflicts(element: SharedElement): Promise<void> {
    const conflicts: ElementConflict[] = [];

    // Compare all versions to find conflicts
    for (let i = 0; i < element.versions.length; i++) {
      for (let j = i + 1; j < element.versions.length; j++) {
        const version1 = element.versions[i];
        const version2 = element.versions[j];

        const conflict = version1 && version2 ? await this.compareVersions(version1, version2) : null;
        if (conflict) {
          conflicts.push(conflict);
        }
      }
    }

    element.conflicts = conflicts;
  }

  private async compareVersions(
    version1: ElementVersion,
    version2: ElementVersion
  ): Promise<ElementConflict | null> {
    // Simple conflict detection - would use AI for more sophisticated analysis
    const conflictingProperties = [];
    
    for (const [key, value1] of Object.entries(version1.properties)) {
      const value2 = version2.properties[key];
      if (value2 !== undefined && value1 !== value2) {
        conflictingProperties.push(key);
      }
    }

    if (conflictingProperties.length > 0) {
      return {
        id: `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        conflictingVersions: [version1.id, version2.id],
        description: `Conflicting properties: ${conflictingProperties.join(', ')}`,
        severity: conflictingProperties.length > 2 ? 'high' : 'medium',
        autoResolvable: false
      };
    }

    return null;
  }

  private async checkRuleViolations(
    rule: ContinuityRule,
    projectId: string,
    universe: SharedUniverse
  ): Promise<ContinuityViolation[]> {
    const violations: ContinuityViolation[] = [];
    
    // Simplified rule checking - would implement comprehensive rule engine
    for (const element of universe.sharedElements) {
      if (!rule.elementTypes.includes(element.type)) continue;

      const projectVersions = element.versions.filter(v => v.projectId === projectId);
      
      for (const version of projectVersions) {
        for (const condition of rule.conditions) {
          const violates = this.evaluateCondition(condition, version);
          if (violates) {
            violations.push({
              id: `violation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              ruleId: rule.id,
              ruleName: rule.name,
              elementId: element.id,
              elementName: element.name,
              description: `Rule "${rule.name}" violated: ${condition.field} ${condition.operator} ${condition.value}`,
              severity: 'medium',
              projectId
            });
          }
        }
      }
    }

    return violations;
  }

  private evaluateCondition(condition: RuleCondition, version: ElementVersion): boolean {
    const value = version.properties[condition.field];
    
    switch (condition.operator) {
      case 'equals':
        return value !== condition.value;
      case 'not_equals':
        return value === condition.value;
      case 'contains':
        return typeof value === 'string' && !value.includes(String(condition.value));
      case 'greater_than':
        return typeof value === 'number' && value <= Number(condition.value);
      case 'less_than':
        return typeof value === 'number' && value >= Number(condition.value);
      default:
        return false;
    }
  }
}

export interface ContinuityViolation {
  id: string;
  ruleId: string;
  ruleName: string;
  elementId: string;
  elementName: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  projectId: string;
}