'use client'

import { useState, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useToast } from '@/hooks/use-toast'

interface StoryBibleData {
  characters: Array<{
    id: string
    name: string
    role: string
    description: string
    backstory?: string
    traits: string[]
  }>
  worldRules: Record<string, string>
  timeline: Array<{
    id: string
    event: string
    chapter: number
  }>
  plotThreads: Array<{
    id: string
    description: string
    status: 'active' | 'resolved'
  }>
}

export function useStoryBible(projectId: string) {
  const [storyBible, setStoryBible] = useState<StoryBibleData>({
    characters: [],
    worldRules: {},
    timeline: [],
    plotThreads: []
  })
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const supabase = createClient()

  const loadStoryBible = useCallback(async () => {
    setIsLoading(true)
    try {
      // Load characters
      const { data: characters } = await supabase
        .from('characters')
        .select('*')
        .eq('project_id', projectId)

      // Load story bible entries
      const { data: bibleEntries } = await supabase
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)

      // Process data
      const worldRules: Record<string, string> = {}
      const timeline: Array<{
        id: string
        event: string
        chapter: number
      }> = []
      const plotThreads: Array<{
        id: string
        description: string
        status: 'active' | 'resolved'
      }> = []

      bibleEntries?.forEach(entry => {
        switch (entry.entry_type) {
          case 'world_rule':
            worldRules[entry.entry_key] = entry.entry_data?.value || ''
            break
          case 'timeline_event':
            timeline.push({
              id: entry.id,
              event: entry.entry_data?.event || '',
              chapter: entry.entry_data?.chapter || 1
            })
            break
          case 'plot_thread':
            plotThreads.push({
              id: entry.entry_key,
              description: entry.entry_data?.description || '',
              status: entry.entry_data?.status || 'active'
            })
            break
        }
      })

      setStoryBible({
        characters: characters?.map(char => ({
          id: char.id,
          name: char.name,
          role: char.role,
          description: char.description || '',
          backstory: char.backstory || '',
          traits: char.personality_traits?.traits || []
        })) || [],
        worldRules,
        timeline,
        plotThreads
      })
    } catch (error) {
      console.error('Error loading story bible:', error)
      toast({
        title: "Load failed",
        description: "Failed to load story bible data.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }, [projectId, supabase, toast])

  const updateStoryBible = useCallback(async (action: string, type: string, data: unknown, id?: string) => {
    try {
      const response = await fetch('/api/story-bible/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          action,
          type,
          data,
          id
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update story bible')
      }

      toast({
        title: "Updated",
        description: "Story bible updated successfully.",
      })

      // Reload data
      await loadStoryBible()
      return true
    } catch (error) {
      console.error('Error updating story bible:', error)
      toast({
        title: "Update failed",
        description: "Failed to update story bible. Please try again.",
        variant: "destructive",
      })
      return false
    }
  }, [projectId, loadStoryBible, toast])

  const addCharacter = useCallback((character: {
    name: string
    role: string
    description: string
    backstory?: string
    traits: string[]
  }) => {
    return updateStoryBible('create', 'character', character)
  }, [updateStoryBible])

  const updateCharacter = useCallback((id: string, character: {
    name: string
    role: string
    description: string
    backstory?: string
    traits: string[]
  }) => {
    return updateStoryBible('update', 'character', character, id)
  }, [updateStoryBible])

  const deleteCharacter = useCallback((id: string) => {
    return updateStoryBible('delete', 'character', {}, id)
  }, [updateStoryBible])

  const addWorldRule = useCallback((key: string, value: string) => {
    return updateStoryBible('create', 'world_rule', { key, value })
  }, [updateStoryBible])

  const updateWorldRule = useCallback((key: string, value: string) => {
    return updateStoryBible('update', 'world_rule', { key, value })
  }, [updateStoryBible])

  const deleteWorldRule = useCallback((key: string) => {
    return updateStoryBible('delete', 'world_rule', {}, key)
  }, [updateStoryBible])

  const addTimelineEvent = useCallback((event: string, chapter: number) => {
    return updateStoryBible('create', 'timeline_event', { event, chapter })
  }, [updateStoryBible])

  const updateTimelineEvent = useCallback((id: string, event: string, chapter: number) => {
    return updateStoryBible('update', 'timeline_event', { event, chapter }, id)
  }, [updateStoryBible])

  const deleteTimelineEvent = useCallback((id: string) => {
    return updateStoryBible('delete', 'timeline_event', {}, id)
  }, [updateStoryBible])

  const addPlotThread = useCallback((description: string, status: 'active' | 'resolved' = 'active') => {
    return updateStoryBible('create', 'plot_thread', { description, status })
  }, [updateStoryBible])

  const updatePlotThread = useCallback((id: string, description: string, status: 'active' | 'resolved') => {
    return updateStoryBible('update', 'plot_thread', { description, status }, id)
  }, [updateStoryBible])

  const deletePlotThread = useCallback((id: string) => {
    return updateStoryBible('delete', 'plot_thread', {}, id)
  }, [updateStoryBible])

  return {
    storyBible,
    isLoading,
    loadStoryBible,
    addCharacter,
    updateCharacter,
    deleteCharacter,
    addWorldRule,
    updateWorldRule,
    deleteWorldRule,
    addTimelineEvent,
    updateTimelineEvent,
    deleteTimelineEvent,
    addPlotThread,
    updatePlotThread,
    deletePlotThread
  }
}