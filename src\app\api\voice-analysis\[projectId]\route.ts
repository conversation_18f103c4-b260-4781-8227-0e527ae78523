import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { VoiceAnalyzer } from '@/lib/analysis/voice-analyzer';
import { createClient } from '@/lib/supabase/server';
import OpenAI from 'openai';
import { config } from '@/lib/config';

const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const supabase = await createClient();
    const { projectId } = await params;

    // Get existing voice profile
    const { data: voiceProfile } = await supabase
      .from('voice_profiles')
      .select('*')
      .eq('project_id', projectId)
      .single();

    if (voiceProfile) {
      return NextResponse.json(voiceProfile);
    }

    // If no profile exists, create one from existing content
    const { data: chapters } = await supabase
      .from('chapters')
      .select('content')
      .eq('project_id', projectId)
      .eq('status', 'complete')
      .limit(5);

    if (!chapters || chapters.length === 0) {
      return NextResponse.json(null);
    }

    const voiceAnalyzer = new VoiceAnalyzer(openai);
    const sampleTexts = chapters.map(ch => ch.content).filter(Boolean);
    
    if (sampleTexts.length === 0) {
      return NextResponse.json(null);
    }

    const newProfile = await voiceAnalyzer.analyzeUserVoice(sampleTexts, projectId);

    // Save the profile
    const { error } = await supabase
      .from('voice_profiles')
      .upsert({
        project_id: projectId,
        profile_data: newProfile,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Error saving voice profile:', error);
    }

    return NextResponse.json(newProfile);

  } catch (error) {
    console.error('Error in voice analysis:', error);
    return NextResponse.json(
      { error: 'Voice analysis failed' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const supabase = await createClient();
    const { projectId } = await params;
    const { content, action } = await request.json();

    const voiceAnalyzer = new VoiceAnalyzer(openai);

    if (action === 'create') {
      // Create new voice profile from provided content
      const sampleTexts = Array.isArray(content) ? content : [content];
      const newProfile = await voiceAnalyzer.analyzeUserVoice(sampleTexts, projectId);

      // Save the profile
      const { error } = await supabase
        .from('voice_profiles')
        .upsert({
          project_id: projectId,
          profile_data: newProfile,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (error) {
        throw error;
      }

      return NextResponse.json(newProfile);

    } else if (action === 'update') {
      // Update existing profile with new content
      const { data: existingProfile } = await supabase
        .from('voice_profiles')
        .select('profile_data')
        .eq('project_id', projectId)
        .single();

      if (!existingProfile) {
        return NextResponse.json(
          { error: 'No existing profile to update' },
          { status: 404 }
        );
      }

      const updatedProfile = await voiceAnalyzer.updateVoiceProfile(
        existingProfile.profile_data,
        content
      );

      // Save updated profile
      const { error } = await supabase
        .from('voice_profiles')
        .update({
          profile_data: updatedProfile,
          updated_at: new Date().toISOString(),
        })
        .eq('project_id', projectId);

      if (error) {
        throw error;
      }

      return NextResponse.json(updatedProfile);
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in voice analysis POST:', error);
    return NextResponse.json(
      { error: 'Voice analysis failed' },
      { status: 500 }
    );
  }
}