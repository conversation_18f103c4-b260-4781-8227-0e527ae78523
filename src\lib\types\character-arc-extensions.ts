// Extensions to existing character types for enhanced arc analysis

import type { Character, CharacterArc, CharacterArcMoment } from '@/lib/agents/types';
import type { DevelopmentGridData, ArcPattern } from './character-development';

// Extended character interface with development tracking
export interface EnhancedCharacter extends Character {
  developmentData?: CharacterDevelopmentTracking;
  arcAnalysis?: CharacterArcAnalysis;
}

export interface CharacterDevelopmentTracking {
  gridData: DevelopmentGridData;
  lastAnalyzed: Date;
  totalChaptersAnalyzed: number;
  developmentVelocity: {
    [dimensionId: string]: number; // Average change per chapter
  };
  peakMoments: DevelopmentPeak[];
  regressionPeriods: RegressionPeriod[];
}

export interface DevelopmentPeak {
  chapter: number;
  dimension: string;
  intensity: number;
  description: string;
  type: 'breakthrough' | 'major_growth' | 'turning_point';
}

export interface RegressionPeriod {
  startChapter: number;
  endChapter: number;
  dimension: string;
  severity: 'minor' | 'moderate' | 'major';
  cause: string;
  recovery?: {
    chapterNumber: number;
    method: string;
  };
}

export interface CharacterArcAnalysis {
  detectedPattern: ArcPattern;
  confidence: number;
  alternativePatterns: ArcPattern[];
  currentPhase: string;
  progressPercentage: number;
  predictedCompletion: {
    earliestChapter: number;
    latestChapter: number;
    confidence: number;
  };
  arcHealth: ArcHealthMetrics;
}

export interface ArcHealthMetrics {
  overallScore: number; // 0-100
  consistency: number; // How well character follows their arc
  development: number; // Amount of growth/change
  believability: number; // How realistic the changes are
  pacing: number; // Appropriate timing of development
  impact: number; // How significant the arc is to the story
  issues: ArcIssue[];
  recommendations: ArcRecommendation[];
}

export interface ArcIssue {
  type: 'inconsistency' | 'pacing' | 'development' | 'believability' | 'impact';
  severity: 'low' | 'medium' | 'high' | 'critical';
  chapter: number;
  description: string;
  affectedDimensions: string[];
}

export interface ArcRecommendation {
  type: 'enhancement' | 'correction' | 'opportunity';
  priority: 'low' | 'medium' | 'high';
  chapter: number;
  description: string;
  expectedImpact: 'minor' | 'moderate' | 'major';
  effort: 'low' | 'medium' | 'high';
}

// Enhanced character arc with pattern tracking
export interface EnhancedCharacterArc extends CharacterArc {
  patternType?: string;
  patternConfidence?: number;
  phaseTracker?: ArcPhaseTracker;
  deviationHistory?: ArcDeviation[];
}

export interface ArcPhaseTracker {
  currentPhase: string;
  phasesCompleted: CompletedPhase[];
  expectedNextPhase: string;
  timelineAlignment: number; // How well actual timing matches expected
}

export interface CompletedPhase {
  name: string;
  startChapter: number;
  endChapter: number;
  keyEvents: string[];
  development: PhaseOutcome;
}

export interface PhaseOutcome {
  dimensionChanges: { [dimensionId: string]: number };
  emotionalState: string;
  relationshipImpacts: RelationshipImpact[];
  skillsGained: string[];
  beliefsChanged: string[];
}

export interface RelationshipImpact {
  characterId: string;
  changeType: 'strengthened' | 'weakened' | 'transformed' | 'new' | 'ended';
  intensity: number;
  description: string;
}

export interface ArcDeviation {
  chapter: number;
  expectedBehavior: string;
  actualBehavior: string;
  deviationScore: number; // How far from expected
  impact: 'negligible' | 'minor' | 'moderate' | 'significant';
  corrected: boolean;
  correctionChapter?: number;
}

// Enhanced character arc moment with development tracking
export interface EnhancedCharacterArcMoment extends CharacterArcMoment {
  developmentDimensions?: {
    [dimensionId: string]: {
      beforeValue: number;
      afterValue: number;
      changeDescription: string;
    };
  };
  significance: number; // 0-100 importance score
  arcPatternRelevance: string; // How this moment fits the pattern
  relationshipEffects?: RelationshipImpact[];
  catalysts?: string[]; // What triggered this moment
  consequences?: string[]; // What this moment led to
}

// Character comparison utilities
export interface CharacterComparison {
  characters: string[]; // Character IDs being compared
  dimensions: string[]; // Which dimensions to compare
  comparisonType: 'development_velocity' | 'arc_progress' | 'pattern_similarity' | 'interaction_impact';
  timeframe: {
    startChapter: number;
    endChapter: number;
  };
  results: ComparisonResult[];
}

export interface ComparisonResult {
  dimension: string;
  characterScores: { [characterId: string]: number };
  analysis: string;
  insights: string[];
  relationships?: CharacterInteractionAnalysis;
}

export interface CharacterInteractionAnalysis {
  frequency: number; // How often characters interact
  impact: number; // How much they affect each other's development
  mutualGrowth: boolean; // Whether they grow together
  conflictLevel: number; // Amount of conflict between them
  complementarity: number; // How well they complement each other
}

// Analytics and reporting
export interface CharacterDevelopmentReport {
  projectId: string;
  characterId: string;
  generatedAt: Date;
  timeframe: {
    startChapter: number;
    endChapter: number;
  };
  summary: ReportSummary;
  detailedAnalysis: DetailedAnalysis;
  recommendations: ArcRecommendation[];
  futurePredictions: FuturePrediction[];
}

export interface ReportSummary {
  overallProgress: number;
  keyAchievements: string[];
  majorChallenges: string[];
  developmentVelocity: number;
  arcAlignment: number;
  characterConsistency: number;
}

export interface DetailedAnalysis {
  dimensionBreakdown: { [dimensionId: string]: DimensionAnalysis };
  phaseProgression: PhaseProgressionAnalysis;
  relationshipEvolution: RelationshipEvolutionAnalysis;
  thematicContribution: ThematicContributionAnalysis;
}

export interface DimensionAnalysis {
  startValue: number;
  endValue: number;
  totalChange: number;
  changeRate: number;
  volatility: number;
  keyMoments: DevelopmentPeak[];
  trends: string[];
}

export interface PhaseProgressionAnalysis {
  currentPhase: string;
  phaseHistory: CompletedPhase[];
  phaseDuration: { [phase: string]: number };
  transitionQuality: number;
  predictedNextPhases: string[];
}

export interface RelationshipEvolutionAnalysis {
  newRelationships: string[];
  strengthenedRelationships: string[];
  weakenedRelationships: string[];
  transformedRelationships: string[];
  relationshipDiversity: number;
  socialNetworkGrowth: number;
}

export interface ThematicContributionAnalysis {
  themesSupported: string[];
  thematicStrength: number;
  thematicConsistency: number;
  uniqueContributions: string[];
}

export interface FuturePrediction {
  chapterRange: { start: number; end: number };
  predictedDevelopments: PredictedDevelopment[];
  confidence: number;
  assumptions: string[];
  alternativeScenarios: AlternativeScenario[];
}

export interface PredictedDevelopment {
  dimension: string;
  expectedChange: number;
  description: string;
  likelihood: number;
  dependsOn: string[];
}

export interface AlternativeScenario {
  name: string;
  description: string;
  probability: number;
  triggers: string[];
  outcomes: PredictedDevelopment[];
}