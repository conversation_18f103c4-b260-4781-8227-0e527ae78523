# Form Validation Implementation Plan

## Overview
Add comprehensive client-side validation using Zod to all forms in BookScribe to ensure data integrity, improve user experience, and prevent submission of invalid data.

## Critical Forms Identified

### 1. Authentication Forms ✓ Basic validation exists
- **Auth Form** (`auth-form.tsx`) - Login/signup
- **Forgot Password Form** (`forgot-password-form.tsx`)
- **Reset Password Form** (`reset-password-form.tsx`)

### 2. Profile Management ✓ Basic validation exists 
- **Profile Form** (`profile-form.tsx`) - Email/password updates

### 3. Project Wizard (PRIORITY - Complex multi-step validation needed)
- **Project Basics Step** (`project-basics-step.tsx`) ✓ Has basic validation
- **Genre Style Step** (`genre-style-step.tsx`) ✓ Has basic validation
- **Structure Pacing Step** (`structure-pacing-step.tsx`)
- **Character World Step** (`character-world-step.tsx`)
- **Themes Content Step** (`themes-content-step.tsx`)
- **Technical Specs Step** (`technical-specs-step.tsx`)
- **Review Step** (`review-step.tsx`)

### 4. Payment Forms
- **Stripe Payment Form** (`stripe-payment-form.tsx`) - Stripe handles validation

## Implementation Strategy

### Phase 1: Create Validation Schemas
- [ ] Create shared Zod schemas for all project configuration data
- [ ] Create authentication form schemas
- [ ] Create profile management schemas

### Phase 2: Enhance Existing Forms
- [ ] Add Zod validation to authentication forms
- [ ] Add Zod validation to profile forms
- [ ] Add comprehensive validation to project wizard steps

### Phase 3: Add Advanced UX Features
- [ ] Real-time field validation with debouncing
- [ ] Form state management with error display
- [ ] Loading states and disabled states during validation
- [ ] Consistent error messaging across forms

### Phase 4: Edge Cases and Testing
- [ ] Cross-field validation rules
- [ ] Conditional validation based on selections
- [ ] Form submission prevention for invalid data
- [ ] Comprehensive error handling

## Files to Create/Modify

### New Files
- `/src/lib/validation/auth-schemas.ts` - Authentication form schemas
- `/src/lib/validation/profile-schemas.ts` - Profile form schemas  
- `/src/lib/validation/project-schemas.ts` - Project wizard schemas
- `/src/hooks/use-form-validation.ts` - Custom validation hook
- `/src/components/ui/form-field.tsx` - Reusable form field with validation

### Modified Files
- `/src/components/auth/auth-form.tsx`
- `/src/components/auth/forgot-password-form.tsx`
- `/src/components/auth/reset-password-form.tsx`
- `/src/components/profile/profile-form.tsx`
- `/src/components/project-wizard/steps/*.tsx` (all step components)

## Validation Requirements

### Project Wizard Validation Rules
- **Required fields**: Name, description, target audience, content rating, project scope
- **Conditional requirements**: Series fields only when multi-book scope selected
- **Field limits**: Name max 255 chars, description min 10 chars
- **Cross-validation**: Ensure selected options are compatible
- **Word count validation**: Within realistic novel ranges

### Authentication Validation Rules
- **Email**: Valid email format, required
- **Password**: Min 6 chars for signup, 8 chars recommended
- **Password confirmation**: Must match new password
- **Reset token**: Valid format if provided

### Profile Validation Rules
- **Email updates**: Valid email format, different from current
- **Password updates**: Current password required, new password strength
- **Confirmation**: Password confirmation must match

## Implementation Details

### Error Display Strategy
- Inline field errors below inputs
- Form-level error summary at top
- Toast notifications for submission errors
- Visual indicators (red borders, icons)

### Loading States
- Disable form during validation
- Show spinner on submit buttons
- Prevent double submissions
- Maintain form state during async operations

### Accessibility
- ARIA labels for error states
- Screen reader announcements
- Keyboard navigation support
- Clear error messaging

## Success Criteria
- [ ] All forms prevent invalid data submission
- [ ] Users receive immediate feedback on validation errors
- [ ] Form state is preserved during validation
- [ ] Loading states provide clear user feedback
- [ ] Error messages are helpful and actionable
- [ ] Validation is consistent across the application