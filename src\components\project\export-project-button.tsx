'use client'

import { UnifiedExport } from '@/components/export/unified-export'
import { Button } from '@/components/ui/button'
import { Download } from 'lucide-react'

interface ExportProjectButtonProps {
  projectId: string
  projectTitle: string
  hasCompletedChapters?: boolean
}

export function ExportProjectButton({ projectId, projectTitle, hasCompletedChapters = false }: ExportProjectButtonProps) {
  return (
    <UnifiedExport
      projectId={projectId}
      projectTitle={projectTitle}
      hasCompletedChapters={hasCompletedChapters}
      mode="dialog"
    >
      <Button variant="outline" className="w-full">
        <Download className="h-4 w-4 mr-2" />
        Export Project
      </Button>
    </UnifiedExport>
  )
}