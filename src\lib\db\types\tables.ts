import type { Database } from '../types'

// Helper Types for easier access
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Specific table types for convenience
export type Profile = Tables<'profiles'>
export type UserSubscription = Tables<'user_subscriptions'>
export type UsageTracking = Tables<'usage_tracking'>
export type UsageEvent = Tables<'usage_events'>
export type Project = Tables<'projects'>
export type StoryArc = Tables<'story_arcs'>
export type Chapter = Tables<'chapters'>
export type Character = Tables<'characters'>
export type StoryBible = Tables<'story_bible'>
export type StoryBibles = Tables<'story_bibles'>
export type ReferenceMaterial = Tables<'reference_materials'>
export type AgentLog = Tables<'agent_logs'>
export type SelectionProfile = Tables<'selection_profiles'>
export type SelectionAnalytics = Tables<'selection_analytics'>
export type WritingSession = Tables<'writing_sessions'>
export type ChapterVersion = Tables<'chapter_versions'>
export type EditingSession = Tables<'editing_sessions'>
export type ProcessingTask = Tables<'processing_tasks'>
export type ContentEmbedding = Tables<'content_embeddings'>

// Insert types for convenience
export type ProfileInsert = TablesInsert<'profiles'>
export type ProjectInsert = TablesInsert<'projects'>
export type ChapterInsert = TablesInsert<'chapters'>
export type CharacterInsert = TablesInsert<'characters'>
export type SelectionProfileInsert = TablesInsert<'selection_profiles'>
export type WritingSessionInsert = TablesInsert<'writing_sessions'>
export type AgentLogInsert = TablesInsert<'agent_logs'>
export type ReferenceMaterialInsert = TablesInsert<'reference_materials'>
export type StoryBibleInsert = TablesInsert<'story_bible'>
export type ProcessingTaskInsert = TablesInsert<'processing_tasks'>
export type ContentEmbeddingInsert = TablesInsert<'content_embeddings'>

// Update types for convenience
export type ProjectUpdate = TablesUpdate<'projects'>
export type ChapterUpdate = TablesUpdate<'chapters'>
export type CharacterUpdate = TablesUpdate<'characters'>
export type SelectionProfileUpdate = TablesUpdate<'selection_profiles'>
export type ProcessingTaskUpdate = TablesUpdate<'processing_tasks'>
export type ContentEmbeddingUpdate = TablesUpdate<'content_embeddings'>