# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage
.nyc_output
jest.config.js
jest.setup.js
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx
__tests__
__mocks__

# Development
.env.local
.env.development
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.DS_Store
*.pem

# Editor
.vscode
.idea
*.swp
*.swo
*~

# Build outputs (Vercel will build fresh)
.next
out
build
dist

# Documentation
docs
*.md
!README.md

# Scripts and configs
scripts
.github
.husky
.eslintcache
.prettierignore

# Temporary files
tmp
temp
*.tmp
*.temp

# OS files
Thumbs.db

# Supabase
supabase/.branches
supabase/.temp