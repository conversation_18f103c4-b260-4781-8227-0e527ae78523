'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { InputField } from '@/components/ui/form-field'

export function SimpleAuthForm({ mode }: { mode: 'login' | 'signup' }) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  const handleFieldChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setError(null)
  }

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setLoading(true)
    
    // For now, just redirect to dashboard
    setTimeout(() => {
      router.push('/dashboard')
    }, 1000)
  }

  return (
    <Card className="w-full border-amber-200 dark:border-stone-700 shadow-xl">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-serif text-center">
          {mode === 'signup' ? 'Create an account' : 'Welcome back'}
        </CardTitle>
        <CardDescription className="text-center">
          {mode === 'signup' 
            ? 'Enter your email below to create your account' 
            : 'Enter your email and password to sign in'
          }
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleAuth}>
        <CardContent className="space-y-4">
          <InputField
            id="email"
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(value) => handleFieldChange('email', value as string)}
            required
          />
          <InputField
            id="password"
            label="Password"
            type="password"
            placeholder="Enter your password"
            value={formData.password}
            onChange={(value) => handleFieldChange('password', value as string)}
            required
          />
          {error && (
            <div className="text-sm text-red-600 dark:text-red-400 text-center">
              {error}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <Button 
            type="submit" 
            className="w-full bg-amber-600 hover:bg-amber-700 text-white"
            disabled={loading}
          >
            {loading ? 'Loading...' : mode === 'signup' ? 'Create Account' : 'Sign In'}
          </Button>
          
          {mode === 'login' && (
            <Link 
              href="/forgot-password" 
              className="text-sm text-amber-600 hover:text-amber-700 hover:underline"
            >
              Forgot your password?
            </Link>
          )}
        </CardFooter>
      </form>
    </Card>
  )
}