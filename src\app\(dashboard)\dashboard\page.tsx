import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DashboardClient } from '@/components/dashboard/dashboard-client'
import { ProgressTracker } from '@/components/onboarding/progress-tracker'

export default async function DashboardPage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  const { data: projects } = await supabase
    .from('projects')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
  
  // Check if user is new (account created in last 24 hours)
  const isNewUser = new Date(user.created_at).getTime() > Date.now() - 24 * 60 * 60 * 1000
  
  return (
    <>
      <div id="main-content" className="py-8">
        <div className="container">
          {/* Progress Tracker for new users or users with few projects */}
          {(isNewUser || (projects && projects.length <= 2)) && (
            <div className="mb-8">
              <ProgressTracker 
                hasProjects={Boolean(projects && projects.length > 0)}
                isNewUser={isNewUser}
              />
            </div>
          )}

          <div className="mb-8 flex items-center justify-between">
            <h2 className="text-3xl font-bold">Your Projects</h2>
            <div className="flex gap-2">
              <Link href="/templates">
                <Button variant="outline">Browse Templates</Button>
              </Link>
              <Link href="/samples">
                <Button variant="outline">Try Samples</Button>
              </Link>
              <Link href="/projects/new">
                <Button>Create New Project</Button>
              </Link>
            </div>
          </div>
          
          {projects && projects.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {projects.map((project) => (
                <Link key={project.id} href={`/projects/${project.id}`}>
                  <Card className="h-full transition-shadow hover:shadow-lg">
                    <CardHeader>
                      <CardTitle>{project.title}</CardTitle>
                      <CardDescription>
                        {project.primary_genre} • {project.target_word_count?.toLocaleString()} words
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {project.description || 'No description yet'}
                      </p>
                      <div className="mt-4 flex items-center justify-between text-sm">
                        <span className="capitalize text-muted-foreground">{project.status}</span>
                        <span className="text-muted-foreground">
                          {project.current_word_count.toLocaleString()} / {project.target_word_count?.toLocaleString()}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <Card className="text-center">
              <CardContent className="py-16">
                <p className="mb-4 text-muted-foreground">You haven&apos;t created any projects yet.</p>
                <div className="flex gap-2 justify-center flex-wrap">
                  <Link href="/templates">
                    <Button variant="outline">Browse Templates</Button>
                  </Link>
                  <Link href="/samples">
                    <Button variant="outline">Try a Sample</Button>
                  </Link>
                  <Link href="/projects/new">
                    <Button>Create Your First Project</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      
      <DashboardClient 
        hasProjects={Boolean(projects && projects.length > 0)}
        isNewUser={isNewUser}
      />
    </>
  )
}