'use client'

import type { ReactNode } from "react";
import { useState, useEffect } from "react";

export function ClientProviders({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything until mounted on client
  if (!mounted) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {children}
    </div>
  );
}
