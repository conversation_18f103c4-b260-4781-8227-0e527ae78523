export interface ServiceConfig {
  name: string;
  version: string;
  status: 'active' | 'inactive' | 'maintenance';
  endpoints: string[];
  dependencies: string[];
  healthCheck: string;
}

export interface ServiceResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
  serviceId: string;
}

export interface AIAgent {
  id: string;
  type: 'writing' | 'editing' | 'analysis' | 'research';
  name: string;
  description: string;
  capabilities: string[];
  status: 'idle' | 'processing' | 'error';
  currentTask?: {
    id: string;
    type: string;
    progress: number;
    startTime: number;
  };
}

export interface WritingTask {
  id: string;
  projectId: string;
  type: 'generate' | 'edit' | 'expand' | 'rewrite';
  priority: 'low' | 'medium' | 'high' | 'critical';
  input: {
    content?: string;
    prompt?: string;
    context?: Record<string, unknown>;
    requirements?: string[];
  };
  output?: {
    content: string;
    metadata: Record<string, unknown>;
    quality: number;
  };
  status: 'pending' | 'processing' | 'completed' | 'failed';
  assignedAgent?: string;
  createdAt: number;
  completedAt?: number;
}

export interface ContextData {
  projectId: string;
  storyBible: {
    characters: Array<Record<string, unknown>>;
    worldBuilding: Array<Record<string, unknown>>;
    plotlines: Array<Record<string, unknown>>;
    themes: string[];
    style: Record<string, unknown>;
  };
  continuity: {
    timeline: Array<Record<string, unknown>>;
    relationships: Array<Record<string, unknown>>;
    consistency: number;
  };
  memory: {
    shortTerm: string[];
    longTerm: string[];
    compressed: string[];
  };
}

export interface AnalyticsEvent {
  id: string;
  userId: string;
  projectId?: string;
  type: 'action' | 'progress' | 'behavior' | 'performance';
  event: string;
  data: Record<string, unknown>;
  timestamp: number;
  sessionId: string;
}

export interface CollaborationSession {
  id: string;
  projectId: string;
  participants: {
    userId: string;
    role: 'owner' | 'editor' | 'viewer' | 'commenter';
    status: 'online' | 'offline';
    cursor?: { line: number; column: number };
  }[];
  document: {
    content: string;
    version: number;
    lastModified: number;
    locks: { section: string; userId: string; timestamp: number }[];
  };
  changes: {
    id: string;
    userId: string;
    type: 'insert' | 'delete' | 'format' | 'comment';
    data: Record<string, unknown>;
    timestamp: number;
  }[];
}