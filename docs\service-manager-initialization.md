# ServiceManager Initialization Guide

## Overview

The ServiceManager is responsible for initializing and managing microservices in the BookScribe application. This document explains how the initialization process works and best practices for using services in your code.

## Initialization Strategy

### 1. Build-Time Safety

The ServiceManager **does not initialize during build time** to prevent build failures. It checks for:
- `NEXT_PHASE === 'phase-production-build'`
- `NEXT_PHASE === 'phase-export'`
- Browser environment (`typeof window !== 'undefined'`)

### 2. Runtime Initialization

Services are initialized at runtime through two mechanisms:

#### A. Instrumentation Hook (Automatic)
```typescript
// /instrumentation.ts
export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    const { ServiceManager } = await import('@/lib/services/service-manager')
    const manager = ServiceManager.getInstance()
    await manager.initialize()
  }
}
```

This runs when the Next.js server starts (not during build).

#### B. Lazy Initialization (Fallback)
Each service getter includes lazy initialization:
```typescript
async getService<T>(serviceName: string): Promise<T | null> {
  const initialized = await this.ensureInitialized();
  if (!initialized) {
    return null;
  }
  return this.registry.get(serviceName) as T | null;
}
```

## Using Services in API Routes

### Basic Pattern
```typescript
export async function POST(request: NextRequest) {
  try {
    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize(); // Safe to call multiple times
    
    const orchestrator = await serviceManager.getAIOrchestrator();
    if (!orchestrator) {
      return NextResponse.json(
        { error: 'Service not available' },
        { status: 503 }
      );
    }
    
    // Use the service...
  } catch (error) {
    // Handle errors...
  }
}
```

### Using Middleware Helper
```typescript
import { withServiceManager } from '@/lib/services/middleware';

export const POST = withServiceManager(async (request: NextRequest) => {
  const serviceManager = ServiceManager.getInstance();
  const orchestrator = await serviceManager.getAIOrchestrator();
  
  // Services are guaranteed to be initialized
  // Handle null services appropriately
});
```

## Important Notes

1. **Always use async service getters**: All service getters are async to support lazy initialization
   ```typescript
   // ✅ Correct
   const service = await serviceManager.getAIOrchestrator();
   
   // ❌ Wrong - This doesn't exist anymore
   const service = serviceManager.getAIOrchestrator();
   ```

2. **Handle null services gracefully**: Services may return null if initialization fails
   ```typescript
   const service = await serviceManager.getAIOrchestrator();
   if (!service) {
     return NextResponse.json({ error: 'Service unavailable' }, { status: 503 });
   }
   ```

3. **Initialization is idempotent**: Calling `initialize()` multiple times is safe

4. **Services fail gracefully**: If initialization fails, services return null rather than throwing

## Debugging

Check service health:
```typescript
import { checkServicesHealth } from '@/lib/services/middleware';

const health = await checkServicesHealth();
console.log(health);
// {
//   initialized: true,
//   services: {
//     'ai-orchestrator': true,
//     'content-generator': true,
//     ...
//   },
//   errors: []
// }
```

## Common Issues

1. **Services not initializing**: Check console logs for initialization errors
2. **Build failures**: Ensure no top-level service calls exist
3. **Runtime 503 errors**: Services failed to initialize - check logs