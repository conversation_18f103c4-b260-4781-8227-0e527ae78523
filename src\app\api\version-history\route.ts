import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { z } from 'zod'

// Validation schemas
const versionHistoryQuerySchema = z.object({
  chapter_id: z.string().uuid(),
  limit: z.number().int().positive().max(100).default(10).optional(),
  offset: z.number().int().min(0).default(0).optional()
})

const createVersionSchema = z.object({
  chapter_id: z.string().uuid(),
  content: z.string().min(1),
  changes_summary: z.string().optional(),
  created_by: z.enum(['user', 'ai_writer', 'ai_editor']).default('user'),
  quality_score: z.record(z.unknown()).optional()
})

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const { searchParams } = new URL(request.url)
    const chapter_id = searchParams.get('chapter_id')
    
    if (!chapter_id) {
      return NextResponse.json({ error: 'Chapter ID is required' }, { status: 400 })
    }
    
    const queryParams = {
      chapter_id,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined
    }

    const validatedQuery = versionHistoryQuerySchema.parse(queryParams)

    const supabase = await createClient()
    
    // First verify user owns the chapter through project
    const { data: chapter } = await supabase
      .from('chapters')
      .select(`
        id,
        title,
        chapter_number,
        content,
        word_count,
        updated_at,
        projects!inner (
          id,
          title,
          user_id
        )
      `)
      .eq('id', validatedQuery.chapter_id)
      .eq('projects.user_id', authResult.user?.id || '')
      .single()

    if (!chapter) {
      return NextResponse.json({ error: 'Chapter not found or unauthorized' }, { status: 404 })
    }
    
    // Build query for chapter versions
    let query = supabase
      .from('chapter_versions')
      .select('*', { count: 'exact' })
      .eq('chapter_id', validatedQuery.chapter_id)
      .order('version_number', { ascending: false })

    // Apply pagination
    const limit = validatedQuery.limit || 10
    const offset = validatedQuery.offset || 0
    query = query.range(offset, offset + limit - 1)


    const { data: versions, error, count } = await query

    if (error) {
      console.error('Error fetching version history:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({
      chapter: {
        id: chapter.id,
        title: chapter.title,
        chapter_number: chapter.chapter_number,
        project_title: Array.isArray(chapter.projects) ? chapter.projects[0]?.title : (chapter.projects as any)?.title,
        current_content: chapter.content,
        current_word_count: chapter.word_count,
        last_updated: chapter.updated_at
      },
      versions: versions || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: error.errors
      }, { status: 400 })
    }

    return handleRouteError(error, 'Version History GET')
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body = await request.json()
    const validatedData = createVersionSchema.parse(body)

    const supabase = await createClient()

    // Verify user owns the chapter
    const { data: chapter } = await supabase
      .from('chapters')
      .select(`
        id,
        projects!inner (
          id,
          user_id
        )
      `)
      .eq('id', validatedData.chapter_id)
      .eq('projects.user_id', authResult.user?.id || '')
      .single()

    if (!chapter) {
      return NextResponse.json({ error: 'Chapter not found or unauthorized' }, { status: 404 })
    }

    // Get the latest version number
    const { data: latestVersion } = await supabase
      .from('chapter_versions')
      .select('version_number')
      .eq('chapter_id', validatedData.chapter_id)
      .order('version_number', { ascending: false })
      .limit(1)
      .single()

    const nextVersionNumber = (latestVersion?.version_number || 0) + 1

    // Calculate word count
    const wordCount = validatedData.content.trim().split(/\s+/).filter(word => word.length > 0).length

    // Create new version
    const { data: newVersion, error: versionError } = await supabase
      .from('chapter_versions')
      .insert({
        chapter_id: validatedData.chapter_id,
        version_number: nextVersionNumber,
        content: validatedData.content,
        word_count: wordCount,
        changes_summary: validatedData.changes_summary,
        quality_score: validatedData.quality_score,
        created_by: validatedData.created_by
      })
      .select()
      .single()

    if (versionError) {
      console.error('Error creating version:', versionError)
      return NextResponse.json({ error: versionError.message }, { status: 500 })
    }

    return NextResponse.json({ version: newVersion }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    return handleRouteError(error, 'Version History POST')
  }
}