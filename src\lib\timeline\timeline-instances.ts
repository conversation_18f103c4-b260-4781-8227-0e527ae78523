import { TimelineValidator } from './timeline-validator';

// Shared timeline validator instances across routes
const timelineValidators = new Map<string, TimelineValidator>();

export function getTimelineValidator(projectId: string): TimelineValidator {
  if (!timelineValidators.has(projectId)) {
    timelineValidators.set(projectId, new TimelineValidator(projectId));
  }
  return timelineValidators.get(projectId)!;
}

export function removeTimelineValidator(projectId: string): boolean {
  return timelineValidators.delete(projectId);
}

export function hasTimelineValidator(projectId: string): boolean {
  return timelineValidators.has(projectId);
}