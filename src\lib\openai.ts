import OpenAI from 'openai'
import { config } from '@/lib/config'

export const openai = new OpenAI({
  apiKey: config.openai.apiKey,
})

export const DEFAULT_MODEL = 'gpt-4'
export const FAST_MODEL = 'gpt-4o-mini'
export const EMBEDDING_MODEL = 'text-embedding-3-small'
export const EMBEDDING_DIMENSIONS = 1536

export interface OpenAIConfig {
  model: string
  temperature: number
  maxTokens: number
}

export const defaultConfig: OpenAIConfig = {
  model: DEFAULT_MODEL,
  temperature: 0.7,
  maxTokens: 4000
}

// Embedding utility functions
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    if (!text || text.trim().length === 0) {
      throw new Error('Text content cannot be empty')
    }

    // Clean and truncate text if necessary (OpenAI has token limits)
    const cleanText = text.trim().substring(0, 8000) // Conservative limit

    const response = await openai.embeddings.create({
      model: EMBEDDING_MODEL,
      input: cleanText,
      dimensions: EMBEDDING_DIMENSIONS,
    })

    if (!response.data || response.data.length === 0) {
      throw new Error('No embedding data received from OpenAI')
    }

    const embedding = response.data[0]?.embedding
    if (!embedding) {
      throw new Error('No embedding data in response')
    }
    return embedding
  } catch (error) {
    console.error('Error generating embedding:', error)
    throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export async function generateEmbeddings(texts: string[]): Promise<number[][]> {
  try {
    if (!texts || texts.length === 0) {
      throw new Error('Texts array cannot be empty')
    }

    // Filter out empty texts and clean them
    const cleanTexts = texts
      .filter(text => text && text.trim().length > 0)
      .map(text => text.trim().substring(0, 8000))

    if (cleanTexts.length === 0) {
      throw new Error('No valid texts provided after cleaning')
    }

    const response = await openai.embeddings.create({
      model: EMBEDDING_MODEL,
      input: cleanTexts,
      dimensions: EMBEDDING_DIMENSIONS,
    })

    if (!response.data || response.data.length === 0) {
      throw new Error('No embedding data received from OpenAI')
    }

    return response.data.map(item => item.embedding)
  } catch (error) {
    console.error('Error generating embeddings:', error)
    throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}