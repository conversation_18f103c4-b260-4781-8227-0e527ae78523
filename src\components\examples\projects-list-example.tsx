'use client'

import { useSupabaseQuery, useSupabaseMutation } from '@/hooks/use-supabase-query'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Plus, Trash2 } from 'lucide-react'

// Example component showing best practices for Supabase data fetching
export function ProjectsListExample() {
  // Fetch projects with automatic revalidation
  const { data: projects, isLoading, error, mutate } = useSupabaseQuery(
    async (supabase) => {
      return await supabase
        .from('projects')
        .select(`
          *,
          chapters(count)
        `)
        .order('updated_at', { ascending: false })
    },
    [], // Dependencies
    {
      revalidateOnFocus: true,
      refreshInterval: 30000, // Refresh every 30 seconds
      onSuccess: (data) => {
        console.log('Projects loaded:', data?.length)
      }
    }
  )

  // Create project mutation
  const createProject = useSupabaseMutation(
    async (supabase, variables: { title: string; description: string }) => {
      return await supabase
        .from('projects')
        .insert({
          title: variables.title,
          description: variables.description,
          genre: 'fiction',
          target_word_count: 50000,
        })
        .select()
        .single()
    },
    {
      onSuccess: (data) => {
        // Revalidate the projects list
        mutate()
        console.log('Project created:', data)
      },
      onMutate: async (variables) => {
        // Optimistic update
        await mutate((current) => {
          if (!current) return current
          return [...current, {
            id: 'temp-id',
            title: variables.title,
            description: variables.description,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            chapters: [{ count: 0 }]
          }]
        }, false)
      }
    }
  )

  // Delete project mutation
  const deleteProject = useSupabaseMutation(
    async (supabase, projectId: string) => {
      return await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)
    },
    {
      onSuccess: () => {
        mutate()
      },
      onMutate: async (projectId) => {
        // Optimistic update - remove from list immediately
        await mutate((current) => {
          if (!current) return current
          return current.filter(p => p.id !== projectId)
        }, false)
      }
    }
  )

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4 mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            Failed to load projects: {error.message}
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">My Projects</h2>
        <Button
          onClick={() => {
            const title = prompt('Project title:')
            if (title) {
              createProject.mutate({
                title,
                description: 'A new writing project'
              })
            }
          }}
          disabled={createProject.isLoading}
        >
          <Plus className="mr-2 h-4 w-4" />
          New Project
        </Button>
      </div>

      {projects?.map((project) => (
        <Card key={project.id}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>{project.title}</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {project.chapters?.[0]?.count || 0} chapters
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  if (confirm('Delete this project?')) {
                    deleteProject.mutate(project.id)
                  }
                }}
                disabled={deleteProject.isLoading}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          {project.description && (
            <CardContent>
              <p className="text-sm">{project.description}</p>
            </CardContent>
          )}
        </Card>
      ))}

      {projects?.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              No projects yet. Create your first one!
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}