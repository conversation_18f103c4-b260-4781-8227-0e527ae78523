import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { StoryBibleEditor } from '@/components/editor/story-bible-editor'
import { CharacterRelationshipViz } from '@/components/project/character-relationship-viz'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default async function StoryBiblePage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')
  
  const { data: project, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', id)
    .eq('user_id', user.id)
    .single()
  
  if (error || !project) notFound()

  // Get story bible data
  const { data: characters } = await supabase
    .from('characters')
    .select('*')
    .eq('project_id', id)
    .order('created_at')

  const { data: storyBibleEntries } = await supabase
    .from('story_bible')
    .select('*')
    .eq('project_id', id)

  // Process story bible data
  const worldRules = Object.fromEntries(
    (storyBibleEntries || [])
      .filter(entry => entry.entry_type === 'world_rule')
      .map(entry => [entry.entry_key, entry.entry_data.value])
  )

  const timeline = (storyBibleEntries || [])
    .filter(entry => entry.entry_type === 'timeline_event')
    .map(entry => ({
      event: entry.entry_data.event,
      chapter: entry.entry_data.chapter
    }))
    .sort((a, b) => a.chapter - b.chapter)

  const plotThreads = (storyBibleEntries || [])
    .filter(entry => entry.entry_type === 'plot_thread')
    .map(entry => ({
      id: entry.entry_key,
      description: entry.entry_data.description,
      status: entry.entry_data.status
    }))

  const storyBible = {
    characters: characters || [],
    worldRules,
    timeline,
    plotThreads
  }
  
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/projects/${id}`}>
              <Button variant="ghost">← Back to Project</Button>
            </Link>
            <h1 className="text-2xl font-bold">Story Bible - {project.title}</h1>
          </div>
          <div className="flex gap-2">
            <Link href={`/projects/${id}/write`}>
              <Button>Continue Writing</Button>
            </Link>
          </div>
        </div>
      </header>
      
      <main className="container py-8">
        <div className="max-w-4xl mx-auto">
          <Tabs defaultValue="editor" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="editor">Story Bible Editor</TabsTrigger>
              <TabsTrigger value="relationships">Character Relationships</TabsTrigger>
            </TabsList>
            
            <TabsContent value="editor" className="space-y-6">
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-2">Edit Story Elements</h2>
                <p className="text-muted-foreground">
                  Modify characters, world rules, timeline events, and plot threads to keep your story consistent.
                </p>
              </div>
              
              <StoryBibleEditor 
                projectId={id} 
                storyBible={storyBible}
                onUpdate={() => {
                  // This would trigger a refresh in a real app
                  // For now, we'll rely on router.refresh() in the component
                }}
              />
            </TabsContent>
            
            <TabsContent value="relationships" className="space-y-6">
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-2">Character Relationships</h2>
                <p className="text-muted-foreground">
                  Visualize connections and relationships between your characters.
                </p>
              </div>
              
              <CharacterRelationshipViz characters={characters || []} />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}