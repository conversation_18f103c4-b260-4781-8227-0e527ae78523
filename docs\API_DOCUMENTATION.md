# BookScribe AI - API Documentation

## Overview

This document outlines the REST API endpoints and data structures for BookScribe AI. The API is built using Next.js App Router with TypeScript and integrates with Supabase for data persistence.

## Base URL

```
Development: http://localhost:3000/api
Production: https://bookscribe.ai/api
```

## Authentication

All API endpoints require authentication via Supabase Auth. Include the JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Data Types

### Project

```typescript
interface Project {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  genre: string;
  target_word_count: number;
  target_chapters: number;
  current_word_count: number;
  status: 'planning' | 'writing' | 'editing' | 'complete';
  created_at: string;
  updated_at: string;
  story_arc?: StoryArc;
  characters?: Character[];
  chapters?: Chapter[];
}
```

### Character

```typescript
interface Character {
  id: string;
  project_id: string;
  name: string;
  role: 'protagonist' | 'antagonist' | 'supporting' | 'minor';
  description: string;
  backstory: string;
  personality_traits: Record<string, any>;
  character_arc: {
    beginning: string;
    middle: string;
    end: string;
    growth: string[];
  };
  relationships: {
    character_id: string;
    relationship_type: string;
    description: string;
  }[];
  physical_description: string;
  goals: string;
  fears: string;
  created_at: string;
  updated_at: string;
}
```

### Chapter

```typescript
interface Chapter {
  id: string;
  project_id: string;
  chapter_number: number;
  title: string;
  target_word_count: number;
  actual_word_count: number;
  outline: string;
  content: string;
  status: 'planned' | 'writing' | 'review' | 'complete';
  ai_notes: {
    generated_at: string;
    agent_used: string;
    context_summary: string;
    quality_metrics: QualityMetrics;
  };
  quality_score: number;
  created_at: string;
  updated_at: string;
}
```

### StoryArc

```typescript
interface StoryArc {
  id: string;
  project_id: string;
  act1: {
    description: string;
    key_events: string[];
    chapters: number[];
  };
  act2: {
    description: string;
    key_events: string[];
    chapters: number[];
  };
  act3: {
    description: string;
    key_events: string[];
    chapters: number[];
  };
  themes: string[];
  conflicts: {
    main: string;
    secondary: string[];
  };
  world_building: {
    setting: string;
    rules: string[];
    history: string;
  };
  created_at: string;
  updated_at: string;
}
```

### AI Agent Types

```typescript
interface AgentExecution {
  id: string;
  project_id: string;
  agent_type: 'story_development' | 'character_development' | 'chapter_planning' | 'writing' | 'editing';
  status: 'pending' | 'running' | 'completed' | 'failed';
  input_data: Record<string, any>;
  output_data: Record<string, any>;
  execution_time: number;
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

interface QualityMetrics {
  consistency_score: number;
  character_voice_score: number;
  plot_coherence_score: number;
  pacing_score: number;
  overall_score: number;
  issues: {
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }[];
}
```

## API Endpoints

### Projects

#### GET /api/projects

Retrieve all projects for the authenticated user.

**Response:**
```json
{
  "projects": [
    {
      "id": "uuid",
      "title": "Epic Adventure",
      "description": "A fantasy epic...",
      "genre": "fantasy",
      "target_word_count": 120000,
      "target_chapters": 24,
      "current_word_count": 45000,
      "status": "writing",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T12:00:00Z"
    }
  ]
}
```

#### POST /api/projects

Create a new project.

**Request Body:**
```json
{
  "title": "Epic Adventure",
  "description": "A fantasy epic about...",
  "genre": "fantasy",
  "target_word_count": 120000,
  "target_chapters": 24,
  "story_prompt": "In a world where magic is forbidden..."
}
```

**Response:**
```json
{
  "project": {
    "id": "uuid",
    "title": "Epic Adventure",
    "status": "planning",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### GET /api/projects/[id]

Retrieve a specific project with all related data.

**Response:**
```json
{
  "project": {
    "id": "uuid",
    "title": "Epic Adventure",
    "story_arc": { /* StoryArc object */ },
    "characters": [ /* Character objects */ ],
    "chapters": [ /* Chapter objects */ ]
  }
}
```

#### PUT /api/projects/[id]

Update a project.

**Request Body:**
```json
{
  "title": "Updated Title",
  "description": "Updated description",
  "status": "writing"
}
```

#### DELETE /api/projects/[id]

Delete a project and all related data.

**Response:**
```json
{
  "message": "Project deleted successfully"
}
```

### Characters

#### GET /api/projects/[id]/characters

Retrieve all characters for a project.

**Response:**
```json
{
  "characters": [
    {
      "id": "uuid",
      "name": "Aria Stormwind",
      "role": "protagonist",
      "description": "A young mage...",
      "relationships": [
        {
          "character_id": "uuid2",
          "relationship_type": "mentor",
          "description": "Gandalf teaches Aria..."
        }
      ]
    }
  ]
}
```

#### POST /api/projects/[id]/characters

Create a new character.

**Request Body:**
```json
{
  "name": "Aria Stormwind",
  "role": "protagonist",
  "description": "A young mage with untapped potential",
  "backstory": "Born in a small village...",
  "goals": "To master her magical abilities",
  "fears": "Losing control of her power"
}
```

#### PUT /api/projects/[id]/characters/[characterId]

Update a character.

#### DELETE /api/projects/[id]/characters/[characterId]

Delete a character.

### Chapters

#### GET /api/projects/[id]/chapters

Retrieve all chapters for a project.

**Query Parameters:**
- `status`: Filter by chapter status
- `limit`: Number of chapters to return
- `offset`: Pagination offset

**Response:**
```json
{
  "chapters": [
    {
      "id": "uuid",
      "chapter_number": 1,
      "title": "The Beginning",
      "target_word_count": 5000,
      "actual_word_count": 4850,
      "status": "complete",
      "quality_score": 0.85
    }
  ],
  "total": 24,
  "has_more": true
}
```

#### POST /api/projects/[id]/chapters

Create a new chapter.

**Request Body:**
```json
{
  "chapter_number": 1,
  "title": "The Beginning",
  "target_word_count": 5000,
  "outline": "Introduce the protagonist..."
}
```

#### GET /api/projects/[id]/chapters/[chapterId]

Retrieve a specific chapter with full content.

**Response:**
```json
{
  "chapter": {
    "id": "uuid",
    "chapter_number": 1,
    "title": "The Beginning",
    "content": "The sun rose over the mountains...",
    "ai_notes": {
      "generated_at": "2024-01-01T12:00:00Z",
      "agent_used": "writing",
      "quality_metrics": {
        "overall_score": 0.85,
        "issues": []
      }
    }
  }
}
```

#### PUT /api/projects/[id]/chapters/[chapterId]

Update a chapter.

**Request Body:**
```json
{
  "title": "Updated Title",
  "content": "Updated chapter content...",
  "status": "complete"
}
```

### Story Arc

#### GET /api/projects/[id]/story-arc

Retrieve the story arc for a project.

#### POST /api/projects/[id]/story-arc

Create or update the story arc.

**Request Body:**
```json
{
  "act1": {
    "description": "Setup and introduction",
    "key_events": ["Hero's call to adventure", "Meeting the mentor"]
  },
  "act2": {
    "description": "Confrontation and development",
    "key_events": ["First major conflict", "Character growth"]
  },
  "act3": {
    "description": "Resolution",
    "key_events": ["Final battle", "Resolution"]
  },
  "themes": ["Good vs Evil", "Coming of Age"],
  "conflicts": {
    "main": "Protagonist vs Dark Lord",
    "secondary": ["Internal doubt", "Political intrigue"]
  }
}
```

## AI Agent Endpoints (OpenAI Agents SDK)

### Trigger Book Writing Workflow
```http
POST /api/agents/book-workflow
Content-Type: application/json
Authorization: Bearer {token}

{
  "projectId": "uuid",
  "prompt": "Write a fantasy novel about...",
  "parameters": {
    "targetLength": 80000,
    "genre": "fantasy",
    "themes": ["redemption", "friendship"],
    "writingStyle": "descriptive"
  }
}
```

**Response:**
```json
{
  "success": true,
  "workflowId": "uuid",
  "status": "running",
  "currentAgent": "story_architect",
  "traceId": "uuid",
  "estimatedCompletion": "2024-01-15T10:30:00Z"
}
```

### Get Workflow Status with Tracing
```http
GET /api/agents/workflow/{workflowId}/status
Authorization: Bearer {token}
```

**Response:**
```json
{
  "workflowId": "uuid",
  "status": "running",
  "currentAgent": "character_developer",
  "progress": {
    "story_architect": "completed",
    "character_developer": "running",
    "chapter_planner": "pending",
    "writing_agent": "pending",
    "editor_agent": "pending"
  },
  "trace": {
    "traceId": "uuid",
    "spans": [
      {
        "agentName": "story_architect",
        "startTime": "2024-01-15T10:00:00Z",
        "endTime": "2024-01-15T10:15:00Z",
        "output": {
          "type": "StoryStructure",
          "data": {...}
        }
      }
    ]
  },
  "startedAt": "2024-01-15T10:00:00Z"
}
```

### Resume or Modify Workflow
```http
POST /api/agents/workflow/{workflowId}/resume
Content-Type: application/json
Authorization: Bearer {token}

{
  "fromAgent": "character_developer",
  "modifications": {
    "characterCount": 7,
    "additionalThemes": ["betrayal"]
  }
}
```

### Get Agent Traces
```http
GET /api/agents/workflow/{workflowId}/traces
Authorization: Bearer {token}
```

**Response:**
```json
{
  "traces": [
    {
      "spanId": "uuid",
      "agentName": "story_architect",
      "operation": "generate_story_structure",
      "duration": 15000,
      "inputTokens": 1200,
      "outputTokens": 2800,
      "toolCalls": [
        {
          "toolName": "save_story_structure",
          "duration": 200,
          "success": true
        }
      ]
    }
  ]
}
```

### Export

#### POST /api/projects/[id]/export

Export the project in various formats.

**Request Body:**
```json
{
  "format": "pdf" | "epub" | "docx" | "txt",
  "include_notes": false,
  "chapters": [1, 2, 3] // Optional: specific chapters
}
```

**Response:**
```json
{
  "download_url": "https://storage.supabase.co/...",
  "expires_at": "2024-01-01T13:00:00Z"
}
```

### Analytics

#### GET /api/projects/[id]/analytics

Get project analytics and statistics.

**Response:**
```json
{
  "analytics": {
    "writing_progress": {
      "total_words": 45000,
      "target_words": 120000,
      "completion_percentage": 37.5,
      "daily_word_count": [
        { "date": "2024-01-01", "words": 2500 },
        { "date": "2024-01-02", "words": 3200 }
      ]
    },
    "character_development": {
      "total_characters": 12,
      "main_characters": 3,
      "relationship_complexity": 0.75
    },
    "quality_metrics": {
      "average_quality_score": 0.82,
      "consistency_score": 0.88,
      "chapters_needing_review": 2
    },
    "ai_usage": {
      "total_generations": 45,
      "successful_generations": 42,
      "average_generation_time": 35000
    }
  }
}
```

## WebSocket Events

For real-time updates during AI agent execution:

### Connection

```javascript
const ws = new WebSocket('ws://localhost:3000/api/ws');

// Authenticate
ws.send(JSON.stringify({
  type: 'auth',
  token: 'jwt_token'
}));

// Subscribe to project updates
ws.send(JSON.stringify({
  type: 'subscribe',
  project_id: 'uuid'
}));
```

### Events

#### Agent Status Updates

```json
{
  "type": "agent_status",
  "project_id": "uuid",
  "agent_type": "writing",
  "status": "running",
  "progress": 0.65,
  "estimated_completion": "2024-01-01T12:05:00Z"
}
```

#### Chapter Updates

```json
{
  "type": "chapter_updated",
  "project_id": "uuid",
  "chapter_id": "uuid",
  "changes": {
    "content": "New content...",
    "word_count": 4850,
    "status": "complete"
  }
}
```

#### Quality Metrics

```json
{
  "type": "quality_update",
  "project_id": "uuid",
  "chapter_id": "uuid",
  "metrics": {
    "overall_score": 0.85,
    "issues": [
      {
        "type": "character_consistency",
        "description": "Character name inconsistency",
        "severity": "medium"
      }
    ]
  }
}
```

## Error Handling

All API endpoints return consistent error responses:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "title",
      "issue": "Title is required"
    }
  }
}
```

### Error Codes

- `AUTHENTICATION_ERROR`: Invalid or missing authentication
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `VALIDATION_ERROR`: Invalid input data
- `NOT_FOUND`: Resource not found
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `AI_SERVICE_ERROR`: AI provider error
- `INTERNAL_ERROR`: Server error

## Rate Limiting

- General API: 100 requests per minute
- AI Agent endpoints: 10 requests per minute
- Export endpoints: 5 requests per hour

## Pagination

For endpoints that return lists, use these query parameters:

- `limit`: Number of items (max 100, default 20)
- `offset`: Number of items to skip
- `sort`: Sort field
- `order`: `asc` or `desc`

**Response includes pagination metadata:**

```json
{
  "data": [...],
  "pagination": {
    "total": 150,
    "limit": 20,
    "offset": 40,
    "has_more": true
  }
}
```

## API Versioning

The API uses URL versioning:

```
/api/v1/projects
/api/v2/projects
```

Current version: v1

This API documentation provides a comprehensive guide for integrating with BookScribe AI's backend services. All endpoints are designed to support the agentic AI workflow for novel writing while maintaining performance and reliability.