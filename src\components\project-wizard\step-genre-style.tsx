'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useWizardStore } from '@/stores/wizard-store'
import { 
  GENRES, 
  FANTASY_SUBGENRES, 
  SCIFI_SUBGENRES, 
  NARRATIVE_VOICES, 
  TENSES, 
  TONES, 
  WRITING_STYLES 
} from '@/lib/types/project'

export function StepGenreStyle() {
  const { selections, updateSelections } = useWizardStore()
  
  const getSubgenres = () => {
    if (selections.primaryGenre === GENRES.FANTASY) {
      return Object.entries(FANTASY_SUBGENRES)
    } else if (selections.primaryGenre === GENRES.SCIENCE_FICTION) {
      return Object.entries(SCIFI_SUBGENRES)
    }
    return []
  }
  
  const handleToneToggle = (tone: string, checked: boolean) => {
    const currentTones = selections.toneOptions || []
    if (checked) {
      updateSelections({ toneOptions: [...currentTones, tone] })
    } else {
      updateSelections({ toneOptions: currentTones.filter(t => t !== tone) })
    }
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Genre Selection</CardTitle>
          <CardDescription>
            Choose your primary genre and subgenre to help shape your story&apos;s direction.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Primary Genre</Label>
            <Select 
              value={selections.primaryGenre} 
              onValueChange={(value) => updateSelections({ primaryGenre: value, subgenre: '' })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select primary genre" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(GENRES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {getSubgenres().length > 0 && (
            <div className="space-y-2">
              <Label>Subgenre</Label>
              <Select 
                value={selections.subgenre} 
                onValueChange={(value) => updateSelections({ subgenre: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select subgenre" />
                </SelectTrigger>
                <SelectContent>
                  {getSubgenres().map(([key, value]) => (
                    <SelectItem key={key} value={value}>{value}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="customGenre">Custom Genre (Optional)</Label>
            <Textarea
              id="customGenre"
              placeholder="Describe any unique genre elements or combinations..."
              value={selections.customGenre || ''}
              onChange={(e) => updateSelections({ customGenre: e.target.value })}
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Writing Style & Voice</CardTitle>
          <CardDescription>
            Define how your story will be told and the overall style.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Narrative Voice</Label>
            <Select 
              value={selections.narrativeVoice} 
              onValueChange={(value) => updateSelections({ narrativeVoice: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select narrative voice" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(NARRATIVE_VOICES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Tense</Label>
            <Select 
              value={selections.tense} 
              onValueChange={(value) => updateSelections({ tense: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select tense" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(TENSES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Writing Style</Label>
            <Select 
              value={selections.writingStyle} 
              onValueChange={(value) => updateSelections({ writingStyle: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select writing style" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(WRITING_STYLES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Tone & Atmosphere</CardTitle>
          <CardDescription>
            Select one or more tones that match your story&apos;s atmosphere. You can choose multiple options.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(TONES).map(([key, value]) => (
              <div key={key} className="flex items-center space-x-2">
                <Checkbox 
                  id={key}
                  checked={selections.toneOptions?.includes(value) || false}
                  onCheckedChange={(checked) => handleToneToggle(value, checked as boolean)}
                />
                <Label htmlFor={key} className="text-sm font-normal">{value}</Label>
              </div>
            ))}
          </div>
          
          <div className="mt-4 space-y-2">
            <Label htmlFor="customStyle">Custom Style Description (Optional)</Label>
            <Textarea
              id="customStyle"
              placeholder="Describe any specific voice, style, or tone requirements..."
              value={selections.customStyleDescription || ''}
              onChange={(e) => updateSelections({ customStyleDescription: e.target.value })}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}