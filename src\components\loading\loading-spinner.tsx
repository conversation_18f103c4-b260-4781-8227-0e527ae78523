'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Loader2, Sparkles, BookOpen, Wand2, FileText, Users } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'dots' | 'pulse' | 'themed'
  text?: string
  className?: string
  theme?: 'writing' | 'ai' | 'project' | 'characters' | 'general'
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6', 
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
}

const textSizeClasses = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg', 
  xl: 'text-xl'
}

function DefaultSpinner({ size = 'md', className }: { size: LoadingSpinnerProps['size'], className?: string }) {
  return (
    <Loader2 
      className={cn(
        'animate-spin text-primary',
        sizeClasses[size!],
        className
      )}
    />
  )
}

function DotsSpinner({ size = 'md', className }: { size: LoadingSpinnerProps['size'], className?: string }) {
  const dotSize = size === 'sm' ? 'h-1 w-1' : size === 'lg' ? 'h-3 w-3' : size === 'xl' ? 'h-4 w-4' : 'h-2 w-2'
  
  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-primary rounded-full animate-bounce',
            dotSize
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '0.8s'
          }}
        />
      ))}
    </div>
  )
}

function PulseSpinner({ size = 'md', className }: { size: LoadingSpinnerProps['size'], className?: string }) {
  return (
    <div className={cn('relative', className)}>
      <div 
        className={cn(
          'absolute rounded-full bg-primary/20 animate-ping',
          sizeClasses[size!]
        )}
      />
      <div 
        className={cn(
          'relative rounded-full bg-primary animate-pulse',
          sizeClasses[size!]
        )}
      />
    </div>
  )
}

function ThemedSpinner({ 
  size = 'md', 
  theme = 'general', 
  className 
}: { 
  size: LoadingSpinnerProps['size']
  theme: LoadingSpinnerProps['theme']
  className?: string 
}) {
  const icons = {
    writing: FileText,
    ai: Wand2,
    project: BookOpen,
    characters: Users,
    general: Sparkles
  }
  
  const Icon = icons[theme!]
  
  return (
    <div className={cn('relative', className)}>
      <div 
        className={cn(
          'absolute rounded-full bg-primary/10 animate-ping',
          sizeClasses[size!]
        )}
      />
      <Icon 
        className={cn(
          'animate-spin text-primary',
          sizeClasses[size!]
        )}
        style={{ animationDuration: '2s' }}
      />
    </div>
  )
}

export function LoadingSpinner({ 
  size = 'md', 
  variant = 'default', 
  text, 
  theme = 'general',
  className 
}: LoadingSpinnerProps) {
  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return <DotsSpinner size={size} />
      case 'pulse':
        return <PulseSpinner size={size} />
      case 'themed':
        return <ThemedSpinner size={size} theme={theme} />
      default:
        return <DefaultSpinner size={size} />
    }
  }

  if (text) {
    return (
      <div className={cn('flex flex-col items-center gap-3', className)}>
        {renderSpinner()}
        <p className={cn(
          'text-muted-foreground animate-pulse',
          textSizeClasses[size]
        )}>
          {text}
        </p>
      </div>
    )
  }

  return (
    <div className={cn('flex items-center justify-center', className)}>
      {renderSpinner()}
    </div>
  )
}

// Specialized loading components for different contexts
export function WritingLoader({ text = 'Writing your story...', ...props }: Omit<LoadingSpinnerProps, 'theme'>) {
  return (
    <LoadingSpinner 
      variant="themed" 
      theme="writing" 
      text={text}
      {...props} 
    />
  )
}

export function AILoader({ text = 'AI is thinking...', ...props }: Omit<LoadingSpinnerProps, 'theme'>) {
  return (
    <LoadingSpinner 
      variant="themed" 
      theme="ai" 
      text={text}
      {...props} 
    />
  )
}

export function ProjectLoader({ text = 'Loading project...', ...props }: Omit<LoadingSpinnerProps, 'theme'>) {
  return (
    <LoadingSpinner 
      variant="themed" 
      theme="project" 
      text={text}
      {...props} 
    />
  )
}

export function CharacterLoader({ text = 'Developing characters...', ...props }: Omit<LoadingSpinnerProps, 'theme'>) {
  return (
    <LoadingSpinner 
      variant="themed" 
      theme="characters" 
      text={text}
      {...props} 
    />
  )
}

// Inline loading component for buttons and small spaces
export function InlineLoader({ className, ...props }: Omit<LoadingSpinnerProps, 'text'>) {
  return (
    <LoadingSpinner 
      size="sm" 
      className={cn('inline-flex', className)}
      {...props} 
    />
  )
}