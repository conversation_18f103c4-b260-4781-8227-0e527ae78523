'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { HelpCircle, Lightbulb, CheckCircle, ArrowRight, ArrowLeft } from 'lucide-react'

interface GuidedStepProps {
  title: string
  description: string
  tips: string[]
  children: React.ReactNode
  isCompleted?: boolean
}

function GuidedStep({ title, description, tips, children, isCompleted = false }: GuidedStepProps) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <h2 className="text-2xl font-bold">{title}</h2>
          {isCompleted && <CheckCircle className="h-5 w-5 text-green-500" />}
        </div>
        <p className="text-muted-foreground">{description}</p>
      </div>

      {tips.length > 0 && (
        <Alert>
          <Lightbulb className="h-4 w-4" />
          <AlertDescription>
            <strong>Tips:</strong>
            <ul className="mt-2 space-y-1">
              {tips.map((tip, index) => (
                <li key={index} className="text-sm">• {tip}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardContent className="pt-6">
          {children}
        </CardContent>
      </Card>
    </div>
  )
}

interface GuidedProjectCreationProps {
  children: React.ReactNode
  currentStep: number
  totalSteps: number
  onNext: () => void
  onPrevious: () => void
  onSubmit: () => void
  canProceed: boolean
  isSubmitting: boolean
}

const stepGuidance = [
  {
    title: "Let's Start with the Basics",
    description: "Give your story a title and brief description. Don't worry about making it perfect - you can always change it later!",
    tips: [
      "Choose a working title that captures the essence of your story",
      "The description helps AI understand your vision - be descriptive but concise",
      "Think about what makes your story unique or interesting"
    ]
  },
  {
    title: "Choose Your Story's Genre and Style",
    description: "These choices will guide how the AI develops your story structure, characters, and writing style.",
    tips: [
      "Genre affects plot structure and character archetypes",
      "Narrative voice determines who tells the story (first person, third person, etc.)",
      "Tense choice impacts the story's immediacy and perspective"
    ]
  },
  {
    title: "Define Your Story Structure",
    description: "How your story unfolds affects reader engagement and pacing throughout your novel.",
    tips: [
      "Three-act structure is classic and reliable for most genres",
      "Pacing preference affects how quickly events unfold",
      "Consider your target audience when choosing structure complexity"
    ]
  },
  {
    title: "Build Your World and Characters",
    description: "Rich characters and immersive settings are the heart of compelling fiction.",
    tips: [
      "Character complexity affects how much development each character gets",
      "Setting influences mood, conflict, and plot possibilities",
      "Time period impacts available technology, social norms, and conflicts"
    ]
  },
  {
    title: "Establish Themes and Content Guidelines",
    description: "Themes give your story depth, while content ratings help target the right audience.",
    tips: [
      "Themes provide deeper meaning beyond the surface plot",
      "Content rating affects language, violence, and romantic content",
      "Consider what message or experience you want readers to take away"
    ]
  },
  {
    title: "Set Your Technical Specifications",
    description: "These details help the AI create appropriately sized chapters and structure the narrative.",
    tips: [
      "Word count affects story scope - more words allow for more subplots and development",
      "Chapter count determines pacing - more chapters create faster pacing",
      "POV characters affect narrative complexity and reader connection"
    ]
  },
  {
    title: "Complete Your Project Setup",
    description: "You're almost there! Complete the payment to unlock all of BookScribe's AI-powered writing features.",
    tips: [
      "One-time project fee includes unlimited AI assistance for this book",
      "Your payment information is secure and encrypted",
      "You'll have immediate access to all features after payment"
    ]
  }
]

export function GuidedProjectCreation({ 
  children, 
  currentStep, 
  totalSteps, 
  onNext, 
  onPrevious, 
  onSubmit, 
  canProceed, 
  isSubmitting 
}: GuidedProjectCreationProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const isGuided = searchParams.get('guided') === 'true'
  
  if (!isGuided) {
    return <>{children}</>
  }

  const currentGuidance = stepGuidance[currentStep - 1]
  
  return (
    <div className="space-y-8">
      {/* Progress indicator */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="secondary">Guided Setup</Badge>
            <span className="text-sm text-muted-foreground">
              Step {currentStep} of {totalSteps}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/projects/new')}
            className="text-muted-foreground"
          >
            Switch to Advanced Mode
          </Button>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Progress</span>
            <span>{Math.round((currentStep / totalSteps) * 100)}%</span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-300 ease-out"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Guided step content */}
      {currentGuidance ? (
        <GuidedStep
          title={currentGuidance.title}
          description={currentGuidance.description}
          tips={currentGuidance.tips}
          isCompleted={canProceed}
        >
          {children}
        </GuidedStep>
      ) : (
        // This shouldn't happen, but provide fallback
        <>{children}</>
      )}

      {/* Navigation */}
      <div className="flex items-center justify-between pt-6 border-t">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex gap-2">
          {currentStep < totalSteps ? (
            <Button
              onClick={onNext}
              disabled={!canProceed}
            >
              Continue
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={onSubmit}
              disabled={!canProceed || isSubmitting}
            >
              {isSubmitting ? 'Creating Project...' : 'Create Project'}
            </Button>
          )}
        </div>
      </div>

      {/* Help hint */}
      <div className="text-center">
        <p className="text-xs text-muted-foreground flex items-center justify-center gap-1">
          <HelpCircle className="h-3 w-3" />
          Need help? Each step includes tips to guide your choices.
        </p>
      </div>
    </div>
  )
}