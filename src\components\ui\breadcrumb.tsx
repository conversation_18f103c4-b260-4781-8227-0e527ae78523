import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ReactNode
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  return (
    <nav 
      aria-label="Breadcrumb" 
      className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}
    >
      <Link 
        href="/dashboard" 
        className="flex items-center hover:text-foreground transition-colors"
        aria-label="Go to dashboard"
      >
        <Home className="h-4 w-4" />
      </Link>
      
      {items.map((item, index) => {
        const isLast = index === items.length - 1
        
        return (
          <div key={index} className="flex items-center">
            <ChevronRight className="h-4 w-4 mx-1" aria-hidden="true" />
            
            {item.href && !isLast ? (
              <Link 
                href={item.href} 
                className="flex items-center gap-1 hover:text-foreground transition-colors"
              >
                {item.icon}
                <span>{item.label}</span>
              </Link>
            ) : (
              <span 
                className={cn(
                  "flex items-center gap-1",
                  isLast && "text-foreground font-medium"
                )}
                aria-current={isLast ? "page" : undefined}
              >
                {item.icon}
                <span>{item.label}</span>
              </span>
            )}
          </div>
        )
      })}
    </nav>
  )
}

// Mobile-friendly breadcrumb that shows only current page
export function MobileBreadcrumb({ items, className }: BreadcrumbProps) {
  const currentItem = items[items.length - 1]
  const parentItem = items.length > 1 ? items[items.length - 2] : null
  
  return (
    <nav 
      aria-label="Breadcrumb" 
      className={cn("flex items-center space-x-2 text-sm", className)}
    >
      {parentItem?.href && (
        <>
          <Link 
            href={parentItem.href} 
            className="text-muted-foreground hover:text-foreground transition-colors"
            aria-label={`Go back to ${parentItem.label}`}
          >
            ← Back
          </Link>
          <span className="text-muted-foreground">·</span>
        </>
      )}
      
      <span className="font-medium flex items-center gap-1">
        {currentItem?.icon}
        {currentItem?.label}
      </span>
    </nav>
  )
}