import { STRIPE_PRICES } from './stripe'

export interface SubscriptionTier {
  id: string
  name: string
  description: string
  price: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
  limits: {
    projects: number
    aiGenerations: number
    exportFormats: string[]
    storageGB: number
    collaborators: number
  }
  stripePriceId: string
  popular?: boolean
}

export const SUBSCRIPTION_TIERS: SubscriptionTier[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for trying out BookScribe AI',
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '1 Active Project',
      '5 AI Generations per month',
      'Basic export (TXT)',
      'Community support'
    ],
    limits: {
      projects: 1,
      aiGenerations: 5,
      exportFormats: ['txt'],
      storageGB: 1,
      collaborators: 0
    },
    stripePriceId: ''
  },
  {
    id: 'basic',
    name: 'Basic',
    description: 'For hobbyist writers getting started',
    price: 9,
    currency: 'usd',
    interval: 'month',
    features: [
      '3 Active Projects',
      '25 AI Generations per month',
      'Basic export formats (TXT, DOCX)',
      'Email support',
      'Story bible editor'
    ],
    limits: {
      projects: 3,
      aiGenerations: 25,
      exportFormats: ['txt', 'docx'],
      storageGB: 5,
      collaborators: 1
    },
    stripePriceId: STRIPE_PRICES.basic
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'For serious writers and published authors',
    price: 19,
    currency: 'usd',
    interval: 'month',
    features: [
      '5 Active Projects',
      '50 AI Generations per month',
      'All export formats',
      'Priority support',
      'Character relationship visualization',
      'Advanced story bible editing'
    ],
    limits: {
      projects: 5,
      aiGenerations: 50,
      exportFormats: ['txt', 'docx', 'pdf', 'epub'],
      storageGB: 10,
      collaborators: 2
    },
    stripePriceId: STRIPE_PRICES.pro,
    popular: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For writing teams and publishing houses',
    price: 49,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited Projects',
      '200 AI Generations per month',
      'All export formats',
      'Priority support',
      'Team collaboration',
      'Custom AI training',
      'API access'
    ],
    limits: {
      projects: -1, // unlimited
      aiGenerations: 200,
      exportFormats: ['txt', 'docx', 'pdf', 'epub'],
      storageGB: 100,
      collaborators: 10
    },
    stripePriceId: STRIPE_PRICES.enterprise
  }
]

export interface UserSubscription {
  id: string
  userId: string
  tierId: string
  status: 'active' | 'canceled' | 'past_due' | 'incomplete'
  stripeSubscriptionId?: string
  stripeCustomerId?: string
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
  usage: {
    aiGenerations: number
    projects: number
    storage: number
  }
  createdAt: Date
  updatedAt: Date
}

export function getTierById(tierId: string): SubscriptionTier | undefined {
  return SUBSCRIPTION_TIERS.find(tier => tier.id === tierId)
}

export function getUserTier(subscription: UserSubscription | null): SubscriptionTier {
  if (!subscription || subscription.status !== 'active') {
    const freeTier = SUBSCRIPTION_TIERS[0]
    if (!freeTier) {
      throw new Error('Free tier not found in subscription tiers')
    }
    return freeTier
  }
  const tier = getTierById(subscription.tierId) || SUBSCRIPTION_TIERS[0]
  if (!tier) {
    throw new Error('No valid subscription tier found')
  }
  return tier
}

export function checkUsageLimit(
  subscription: UserSubscription | null,
  limitType: keyof SubscriptionTier['limits'],
  currentUsage: number
): { allowed: boolean; limit: number; remaining: number } {
  const tier = getUserTier(subscription)
  const limit = tier.limits[limitType]
  
  if (typeof limit === 'number') {
    if (limit === -1) {
      // Unlimited
      return { allowed: true, limit: -1, remaining: -1 }
    }
    
    const remaining = Math.max(0, limit - currentUsage)
    return {
      allowed: currentUsage < limit,
      limit,
      remaining
    }
  }
  
  // For array limits (like exportFormats), check if the feature is included
  if (Array.isArray(limit)) {
    return {
      allowed: true,
      limit: limit.length,
      remaining: limit.length - currentUsage
    }
  }
  
  return { allowed: false, limit: 0, remaining: 0 }
}

export function canUseFeature(
  subscription: UserSubscription | null,
  feature: string
): boolean {
  const tier = getUserTier(subscription)
  
  // Map features to tier checks
  switch (feature) {
    case 'character_visualization':
      return tier.id !== 'free'
    case 'story_bible_editing':
      return tier.id !== 'free'
    case 'collaboration':
      return tier.id === 'enterprise'
    case 'api_access':
      return tier.id === 'enterprise'
    case 'custom_ai_training':
      return tier.id === 'enterprise'
    default:
      return true
  }
}

export function formatPrice(price: number, currency: string = 'usd'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(price)
}