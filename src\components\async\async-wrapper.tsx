'use client'

import { Suspense, useState, useCallback, useEffect, useMemo } from 'react'
import type { ReactNode, ErrorInfo, ComponentType, DependencyList } from 'react'
import { ErrorBoundary } from '@/components/error/error-boundary'
import { APIErrorBoundary } from '@/components/error/api-error-boundary'
import { LoadingSpinner } from '@/components/loading/loading-spinner'
import { SkeletonLoader } from '@/components/loading/skeleton-loader'
import type { Database } from '@/lib/db/types'

type Project = Database['public']['Tables']['projects']['Row']
type Chapter = Database['public']['Tables']['chapters']['Row']

interface AsyncWrapperProps {
  children: ReactNode
  loadingComponent?: ReactNode
  errorComponent?: ReactNode
  loadingText?: string
  loadingVariant?: 'spinner' | 'skeleton'
  skeletonVariant?: 'default' | 'card' | 'list' | 'table' | 'editor' | 'dashboard' | 'form'
  errorBoundaryType?: 'default' | 'api'
  retryable?: boolean
  onRetry?: () => void
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  isolate?: boolean
  showErrorDetails?: boolean
  fallbackToErrorPage?: boolean
}

export function AsyncWrapper({
  children,
  loadingComponent,
  errorComponent,
  loadingText,
  loadingVariant = 'spinner',
  skeletonVariant = 'default',
  errorBoundaryType = 'default',
  retryable = true,
  onRetry,
  onError,
  isolate = true,
  showErrorDetails,
}: AsyncWrapperProps) {
  // Default loading component
  const defaultLoadingComponent = loadingVariant === 'skeleton' ? (
    <SkeletonLoader variant={skeletonVariant} />
  ) : (
    <LoadingSpinner text={loadingText} size="lg" />
  )

  const loading = loadingComponent || defaultLoadingComponent

  // Error boundary selection
  const ErrorBoundaryComponent = errorBoundaryType === 'api' ? APIErrorBoundary : ErrorBoundary

  const errorBoundaryProps = {
    fallback: errorComponent,
    onError,
    isolate,
    showErrorDetails,
    ...(errorBoundaryType === 'api' && { 
      retryable, 
      onRetry: onRetry || (() => window.location.reload())
    })
  }

  return (
    <ErrorBoundaryComponent {...errorBoundaryProps}>
      <Suspense fallback={loading}>
        {children}
      </Suspense>
    </ErrorBoundaryComponent>
  )
}

// HOC version for easier component wrapping
export function withAsyncWrapper<T extends object>(
  Component: ComponentType<T>,
  wrapperProps?: Omit<AsyncWrapperProps, 'children'>
) {
  const WrappedComponent = (props: T) => (
    <AsyncWrapper {...wrapperProps}>
      <Component {...props} />
    </AsyncWrapper>
  )

  WrappedComponent.displayName = `withAsyncWrapper(${Component.displayName || Component.name})`
  return WrappedComponent
}

// Specialized wrappers for common use cases
export function APIAsyncWrapper({
  children,
  ...props
}: Omit<AsyncWrapperProps, 'errorBoundaryType'>) {
  return (
    <AsyncWrapper 
      errorBoundaryType="api" 
      loadingVariant="spinner"
      {...props}
    >
      {children}
    </AsyncWrapper>
  )
}

export function EditorAsyncWrapper({
  children,
  ...props
}: Omit<AsyncWrapperProps, 'loadingVariant' | 'skeletonVariant'>) {
  return (
    <AsyncWrapper 
      loadingVariant="skeleton" 
      skeletonVariant="editor"
      errorBoundaryType="api"
      {...props}
    >
      {children}
    </AsyncWrapper>
  )
}

export function DashboardAsyncWrapper({
  children,
  ...props
}: Omit<AsyncWrapperProps, 'loadingVariant' | 'skeletonVariant'>) {
  return (
    <AsyncWrapper 
      loadingVariant="skeleton" 
      skeletonVariant="dashboard"
      errorBoundaryType="api"
      {...props}
    >
      {children}
    </AsyncWrapper>
  )
}

export function ProjectAsyncWrapper({
  children,
  ...props
}: Omit<AsyncWrapperProps, 'loadingVariant' | 'skeletonVariant'>) {
  return (
    <AsyncWrapper 
      loadingVariant="skeleton" 
      skeletonVariant="card"
      errorBoundaryType="api"
      {...props}
    >
      {children}
    </AsyncWrapper>
  )
}

export function FormAsyncWrapper({
  children,
  ...props
}: Omit<AsyncWrapperProps, 'loadingVariant' | 'skeletonVariant'>) {
  return (
    <AsyncWrapper 
      loadingVariant="skeleton" 
      skeletonVariant="form"
      errorBoundaryType="api"
      {...props}
    >
      {children}
    </AsyncWrapper>
  )
}

// Hook for managing async states manually
export function useAsyncState<T = unknown>(
  initialLoading = false,
  initialError: Error | null = null,
  initialData: T | null = null
) {
  const [loading, setLoading] = useState(initialLoading)
  const [error, setError] = useState<Error | null>(initialError)
  const [data, setData] = useState<T | null>(initialData)

  const execute = useCallback(async (asyncFunction: () => Promise<T>) => {
    try {
      setLoading(true)
      setError(null)
      const result = await asyncFunction()
      setData(result)
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      throw error
    } finally {
      setLoading(false)
    }
  }, [])

  const reset = useCallback(() => {
    setLoading(false)
    setError(null)
    setData(null)
  }, [])

  const retry = useCallback((asyncFunction: () => Promise<T>) => {
    return execute(asyncFunction)
  }, [execute])

  return {
    loading,
    error,
    data,
    execute,
    reset,
    retry,
    isSuccess: !loading && !error && data !== null,
    isError: !loading && error !== null,
    isIdle: !loading && error === null && data === null
  }
}

// Component for handling async operations with state
interface AsyncComponentProps<T> {
  asyncFunction: () => Promise<T>
  children: (state: {
    loading: boolean
    error: Error | null
    data: T | null
    retry: () => void
  }) => ReactNode
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
  executeOnMount?: boolean
  dependencies?: DependencyList
}

export function AsyncComponent<T>({
  asyncFunction,
  children,
  onSuccess,
  onError,
  executeOnMount = true,
  dependencies = []
}: AsyncComponentProps<T>) {
  const { loading, error, data, execute } = useAsyncState<T>()

  const retry = useCallback(() => {
    execute(asyncFunction)
      .then(onSuccess)
      .catch(onError)
  }, [asyncFunction, execute, onSuccess, onError])

  useEffect(() => {
    if (executeOnMount) {
      retry()
    }
  }, [executeOnMount, retry, dependencies])

  const memoizedChildren = useMemo(() => 
    children({ loading, error, data, retry }),
    [children, loading, error, data, retry]
  )

  return <>{memoizedChildren}</>
}

// Specialized async components for common patterns
export function AsyncProjectLoader({
  projectId,
  children
}: {
  projectId: string
  children: (state: { loading: boolean; error: Error | null; project: Project | null; retry: () => void }) => ReactNode
}) {
  const asyncFunction = useCallback(() => 
    fetch(`/api/projects/${projectId}`)
      .then(res => {
        if (!res.ok) throw new Error(`Failed to load project: ${res.statusText}`)
        return res.json()
      }), [projectId])

  const renderFunction = useCallback(
    ({ loading, error, data, retry }: { loading: boolean; error: Error | null; data: Project | null; retry: () => void }) => 
      children({ loading, error, project: data, retry }),
    [children]
  )

  return (
    <AsyncComponent
      asyncFunction={asyncFunction}
      dependencies={[projectId]}
    >
      {renderFunction}
    </AsyncComponent>
  )
}

export function AsyncChapterLoader({
  chapterId,
  children
}: {
  chapterId: string
  children: (state: { loading: boolean; error: Error | null; chapter: Chapter | null; retry: () => void }) => ReactNode
}) {
  const asyncFunction = useCallback(() => 
    fetch(`/api/chapters/${chapterId}`)
      .then(res => {
        if (!res.ok) throw new Error(`Failed to load chapter: ${res.statusText}`)
        return res.json()
      }), [chapterId])

  const renderFunction = useCallback(
    ({ loading, error, data, retry }: { loading: boolean; error: Error | null; data: Chapter | null; retry: () => void }) => 
      children({ loading, error, chapter: data, retry }),
    [children]
  )

  return (
    <AsyncComponent
      asyncFunction={asyncFunction}
      dependencies={[chapterId]}
    >
      {renderFunction}
    </AsyncComponent>
  )
}