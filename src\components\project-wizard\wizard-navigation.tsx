'use client'

import { Button } from '@/components/ui/button'
import { useWizardStore } from '@/stores/wizard-store'

interface WizardNavigationProps {
  onNext: () => void
  onPrevious: () => void
  onSubmit: () => void
  isSubmitting?: boolean
  canProceed?: boolean
}

const steps = [
  { id: 1, title: 'Project Basics' },
  { id: 2, title: 'Genre & Style' },
  { id: 3, title: 'Structure & Pacing' },
  { id: 4, title: 'Characters & World' },
  { id: 5, title: 'Themes & Content' },
  { id: 6, title: 'Technical Specs' },
  { id: 7, title: 'Payment' }
]

export function WizardNavigation({ 
  onNext, 
  onPrevious, 
  onSubmit, 
  isSubmitting = false,
  canProceed = true 
}: WizardNavigationProps) {
  const { currentStep } = useWizardStore()
  
  const isFirstStep = currentStep === 1
  const isLastStep = currentStep === steps.length
  
  return (
    <div className="space-y-4">
      {/* Progress indicator */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>Step {currentStep} of {steps.length}</span>
        <span>{Math.round((currentStep / steps.length) * 100)}% complete</span>
      </div>
      
      {/* Progress bar */}
      <div className="h-2 bg-muted rounded-full overflow-hidden">
        <div 
          className="h-full bg-primary transition-all duration-300 ease-out"
          style={{ width: `${(currentStep / steps.length) * 100}%` }}
        />
      </div>
      
      {/* Step titles */}
      <div className="flex justify-between text-xs">
        {steps.map((step) => (
          <div 
            key={step.id}
            className={`flex-1 text-center ${
              step.id <= currentStep ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            {step.title}
          </div>
        ))}
      </div>
      
      {/* Navigation buttons */}
      <div className="flex justify-between pt-4">
        <Button 
          variant="outline" 
          onClick={onPrevious}
          disabled={isFirstStep || isSubmitting}
        >
          Previous
        </Button>
        
        {isLastStep ? (
          // On the payment step, the payment component handles submission
          <Button 
            onClick={onSubmit}
            disabled={!canProceed || isSubmitting}
            className="opacity-0 pointer-events-none"
          >
            {isSubmitting ? 'Creating Project...' : 'Create Project'}
          </Button>
        ) : currentStep === steps.length - 1 ? (
          // On the technical specs step (before payment)
          <Button 
            onClick={onNext}
            disabled={!canProceed || isSubmitting}
          >
            Proceed to Payment
          </Button>
        ) : (
          <Button 
            onClick={onNext}
            disabled={!canProceed || isSubmitting}
          >
            Next
          </Button>
        )}
      </div>
    </div>
  )
}