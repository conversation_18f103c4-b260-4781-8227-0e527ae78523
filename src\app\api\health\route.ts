import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter'
import { config } from '@/lib/config'

export async function GET(request: NextRequest) {
  try {
    // Rate limiting for health checks
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(10, clientIP); // 10 requests per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Authentication required for system health information
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }

    // Check if user has admin role
    const supabase = await createClient()
    const { data: profile } = await supabase
      .from('users')
      .select('subscription_tier')
      .eq('id', authResult.user?.id || '')
      .single()
    
    // For now, consider 'enterprise' tier users as admins
    // In production, you should have a proper admin role field
    if (profile?.subscription_tier !== 'enterprise') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const startTime = Date.now()
    
    // Check database connection
    const { error: dbError } = await supabase
      .from('projects')
      .select('count')
      .limit(1)
      .single()
    
    const dbLatency = Date.now() - startTime
    
    // Check OpenAI API (optional, commented out to avoid unnecessary API calls)
    // const openaiStart = Date.now()
    // const openaiResponse = await fetch('https://api.openai.com/v1/models', {
    //   headers: {
    //     'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
    //   },
    // })
    // const openaiLatency = Date.now() - openaiStart
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: config.build.version || '0.1.0',
      environment: config.build.phase || 'development',
      services: {
        database: {
          status: dbError ? 'unhealthy' : 'healthy',
          latency: dbLatency,
          error: dbError?.message || null,
        },
        // openai: {
        //   status: openaiResponse.ok ? 'healthy' : 'unhealthy',
        //   latency: openaiLatency,
        // },
      },
      uptime: process.uptime(),
    }

    const status = dbError ? 503 : 200
    
    return NextResponse.json(health, { status })
  } catch (error) {
    return handleRouteError(error, 'Health Check')
  }
}