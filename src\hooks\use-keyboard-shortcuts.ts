import { useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'

export interface KeyboardShortcut {
  key: string
  ctrl?: boolean
  alt?: boolean
  shift?: boolean
  meta?: boolean
  description: string
  action: () => void
  category?: 'navigation' | 'creation' | 'editing' | 'general'
}

const globalShortcuts: KeyboardShortcut[] = [
  {
    key: 'k',
    ctrl: true,
    description: 'Open command menu',
    category: 'general',
    action: () => {
      // This would open a command palette in the future
      console.log('Command menu opened')
    }
  },
  {
    key: '/',
    ctrl: true,
    description: 'Focus search',
    category: 'general',
    action: () => {
      const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
      }
    }
  },
  {
    key: '?',
    shift: true,
    description: 'Show keyboard shortcuts',
    category: 'general',
    action: () => {
      // Dispatch event to show shortcuts modal
      const event = new CustomEvent('show-keyboard-shortcuts')
      window.dispatchEvent(event)
    }
  },
]

export function useKeyboardShortcuts(customShortcuts: KeyboardShortcut[] = []) {
  const router = useRouter()
  const { toast } = useToast()
  
  const shortcuts = [...globalShortcuts, ...customShortcuts]
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in inputs
    if (
      event.target instanceof HTMLInputElement ||
      event.target instanceof HTMLTextAreaElement ||
      event.target instanceof HTMLSelectElement ||
      (event.target as HTMLElement).contentEditable === 'true'
    ) {
      return
    }
    
    shortcuts.forEach(shortcut => {
      const ctrlMatch = shortcut.ctrl ? (event.ctrlKey || event.metaKey) : (!shortcut.ctrl && !event.ctrlKey && !event.metaKey)
      const altMatch = shortcut.alt ? event.altKey : !event.altKey
      const shiftMatch = shortcut.shift ? event.shiftKey : !event.shiftKey
      
      if (
        event.key.toLowerCase() === shortcut.key.toLowerCase() &&
        ctrlMatch &&
        altMatch &&
        shiftMatch
      ) {
        event.preventDefault()
        shortcut.action()
      }
    })
  }, [shortcuts])
  
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])
  
  return shortcuts
}

// Writing-specific shortcuts
export function useWritingShortcuts(
  onSave?: () => void,
  onUndo?: () => void,
  onRedo?: () => void,
  onToggleFullscreen?: () => void,
  onToggleAI?: () => void
) {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 's',
      ctrl: true,
      description: 'Save document',
      action: () => {
        if (onSave) {
          onSave()
        }
      }
    },
    {
      key: 'z',
      ctrl: true,
      description: 'Undo',
      action: () => {
        if (onUndo) {
          onUndo()
        }
      }
    },
    {
      key: 'z',
      ctrl: true,
      shift: true,
      description: 'Redo',
      action: () => {
        if (onRedo) {
          onRedo()
        }
      }
    },
    {
      key: 'y',
      ctrl: true,
      description: 'Redo',
      action: () => {
        if (onRedo) {
          onRedo()
        }
      }
    },
    {
      key: 'f11',
      description: 'Toggle fullscreen',
      action: () => {
        if (onToggleFullscreen) {
          onToggleFullscreen()
        }
      }
    },
    {
      key: 'i',
      ctrl: true,
      shift: true,
      description: 'Toggle AI assistant',
      action: () => {
        if (onToggleAI) {
          onToggleAI()
        }
      }
    },
  ]
  
  return useKeyboardShortcuts(shortcuts)
}

// Dashboard shortcuts
export function useDashboardShortcuts() {
  const router = useRouter()
  
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'n',
      ctrl: true,
      description: 'Create new project',
      action: () => {
        router.push('/projects/new')
      }
    },
    {
      key: 'p',
      ctrl: true,
      description: 'View all projects',
      action: () => {
        router.push('/dashboard')
      }
    },
    {
      key: 't',
      ctrl: true,
      description: 'Browse templates',
      action: () => {
        router.push('/templates')
      }
    },
  ]
  
  return useKeyboardShortcuts(shortcuts)
}