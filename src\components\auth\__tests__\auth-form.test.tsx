import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import { AuthForm } from '../auth-form'

// Mock the Supabase client
const mockSignInWithPassword = jest.fn()
const mockSignUp = jest.fn()

jest.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    auth: {
      signInWithPassword: mockSignInWithPassword,
      signUp: mockSignUp,
    },
  }),
}))

describe('AuthForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders login form correctly', () => {
    render(<AuthForm mode="login" />)
    
    expect(screen.getByText('Welcome back')).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
    expect(screen.getByText(/forgot password/i)).toBeInTheDocument()
  })

  it('renders signup form correctly', () => {
    render(<AuthForm mode="signup" />)
    
    expect(screen.getByText('Create an account')).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument()
  })

  it('handles form submission for login', async () => {
    const user = userEvent.setup()
    mockSignInWithPassword.mockResolvedValueOnce({ error: null })
    
    render(<AuthForm mode="login" />)
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')
    await user.click(screen.getByRole('button', { name: /sign in/i }))
    
    await waitFor(() => {
      expect(mockSignInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      })
    })
  })

  it('handles form submission for signup', async () => {
    const user = userEvent.setup()
    mockSignUp.mockResolvedValueOnce({ error: null })
    
    render(<AuthForm mode="signup" />)
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')
    await user.click(screen.getByRole('button', { name: /sign up/i }))
    
    await waitFor(() => {
      expect(mockSignUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          emailRedirectTo: 'http://localhost/auth/callback',
        },
      })
    })
  })

  it('displays error message on authentication failure', async () => {
    const user = userEvent.setup()
    mockSignInWithPassword.mockResolvedValueOnce({ 
      error: { message: 'Invalid credentials' } 
    })
    
    render(<AuthForm mode="login" />)
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'wrongpassword')
    await user.click(screen.getByRole('button', { name: /sign in/i }))
    
    await waitFor(() => {
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
    })
  })
})