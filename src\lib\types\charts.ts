// Type definitions for chart and visualization components

export interface ChartTooltipProps {
  active?: boolean;
  payload?: ChartPayloadItem[];
  label?: string | number;
}

export interface ChartPayloadItem {
  value: number | string;
  name: string;
  color?: string;
  dataKey?: string;
  payload?: Record<string, unknown>;
  unit?: string;
  type?: string;
  chartType?: string;
}

export interface ChartFormatterProps {
  value: number | string | undefined;
  index?: number;
}

export type ChartFormatter = (value: number | string | undefined, index?: number) => string | [string, string];

export interface AreaChartProps {
  data: Record<string, unknown>[];
  dataKey: string;
  xAxisKey: string;
  color?: string;
  formatter?: ChartFormatter;
}

export interface BarChartProps {
  data: Record<string, unknown>[];
  dataKeys: string[];
  xAxisKey: string;
  colors?: string[];
}

export interface RadarChartProps {
  data: Record<string, unknown>[];
  dataKey: string;
  subjectKey: string;
  color?: string;
  formatter?: ChartFormatter;
}