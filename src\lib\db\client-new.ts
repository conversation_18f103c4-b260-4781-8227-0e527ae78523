import { createBrowserClient } from '@supabase/ssr'
import { createClient as createServerClient } from '@/lib/supabase/server'
import type { Database } from './types'
// For client components
export function createTypedBrowserClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables')
  }

  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
    realtime: {
      params: {
        eventsPerSecond: 10, // Rate limiting
      },
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'x-application-name': 'bookscribe',
      },
    },
  })
}

// For server components and API routes
export async function createTypedServerClient() {
  const client = await createServerClient()
  return client
}

// Export a singleton for client-side usage
let browserClient: ReturnType<typeof createTypedBrowserClient> | null = null

export function getTypedBrowserClient() {
  if (!browserClient && typeof window !== 'undefined') {
    browserClient = createTypedBrowserClient()
  }
  return browserClient
}