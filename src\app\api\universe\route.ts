import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { handleRouteError } from '@/lib/auth';
import { UniverseManager } from '@/lib/continuity/universe-manager';

// Create single instance
const universeManager = new UniverseManager();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (projectId) {
      const universe = universeManager.getUniverseForProject(projectId);
      const continuityIssues = await universeManager.detectContinuityIssues(projectId);
      const sharedElements = await universeManager.getSharedElementsForProject(projectId);
      
      return NextResponse.json({
        success: true,
        universe,
        continuityIssues,
        sharedElements
      });
    } else {
      const allUniverses = universeManager.getAllUniverses();
      return NextResponse.json({
        success: true,
        universes: allUniverses
      });
    }
  } catch (error) {
    return handleRouteError(error, 'Universe GET');
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, description, creator } = await request.json();
    
    if (!name || !description || !creator) {
      return NextResponse.json(
        { error: 'Name, description, and creator are required' },
        { status: 400 }
      );
    }

    const universeId = await universeManager.createUniverse(name, description, creator);
    
    return NextResponse.json({
      success: true,
      universeId
    });
  } catch (error) {
    return handleRouteError(error, 'Universe POST');
  }
}

