'use client'

import { useAuth } from '@/contexts/auth-context'
import { useEffect, useState } from 'react'

interface AuthLoadingState {
  isLoading: boolean
  isAuthenticating: boolean
  isInitializing: boolean
  message?: string
}

export function useAuthLoading() {
  const { loading } = useAuth()
  const [authState, setAuthState] = useState<AuthLoadingState>({
    isLoading: true,
    isAuthenticating: false,
    isInitializing: true,
    message: 'Initializing...'
  })

  useEffect(() => {
    if (loading) {
      setAuthState({
        isLoading: true,
        isAuthenticating: true,
        isInitializing: false,
        message: 'Authenticating...'
      })
    } else {
      setAuthState({
        isLoading: false,
        isAuthenticating: false,
        isInitializing: false,
        message: undefined
      })
    }
  }, [loading])

  return authState
}