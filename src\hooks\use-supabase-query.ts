'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { PostgrestError } from '@supabase/supabase-js'
import { createTypedBrowserClient } from '@/lib/db/client-new'
import { handleSupabaseError } from '@/lib/supabase/errors'
import { useToast } from '@/hooks/use-toast'

interface QueryState<T> {
  data: T | null
  error: Error | null
  isLoading: boolean
  isValidating: boolean
}

interface UseSupabaseQueryOptions<T> {
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
  revalidateOnFocus?: boolean
  revalidateOnReconnect?: boolean
  refreshInterval?: number
  enabled?: boolean
}

export function useSupabaseQuery<T>(
  queryFn: (client: ReturnType<typeof createTypedBrowserClient>) => Promise<{ data: T | null; error: PostgrestError | null }>,
  dependencies: unknown[] = [],
  options: UseSupabaseQueryOptions<T> = {}
) {
  const {
    onSuccess,
    onError,
    revalidateOnFocus = true,
    revalidateOnReconnect = true,
    refreshInterval,
    enabled = true,
  } = options

  const [state, setState] = useState<QueryState<T>>({
    data: null,
    error: null,
    isLoading: true,
    isValidating: false,
  })

  const { toast } = useToast()
  const supabase = createTypedBrowserClient()
  const mountedRef = useRef(true)
  const abortControllerRef = useRef<AbortController | null>(null)

  const execute = useCallback(async (isRevalidation = false) => {
    if (!enabled) return

    // Cancel any in-flight requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController()

    try {
      setState(prev => ({
        ...prev,
        isLoading: !isRevalidation || !prev.data,
        isValidating: isRevalidation,
      }))

      const { data, error } = await queryFn(supabase)

      // Check if component is still mounted and request wasn't aborted
      if (!mountedRef.current || abortControllerRef.current.signal.aborted) {
        return
      }

      if (error) {
        throw error
      }

      setState({
        data,
        error: null,
        isLoading: false,
        isValidating: false,
      })

      onSuccess?.(data)
    } catch (error) {
      // Ignore aborted requests
      if (error instanceof Error && error.name === 'AbortError') {
        return
      }

      if (!mountedRef.current) return

      const errorResponse = handleSupabaseError(error)
      const errorObj = new Error(errorResponse.message)

      setState({
        data: null,
        error: errorObj,
        isLoading: false,
        isValidating: false,
      })

      onError?.(errorObj)

      // Only show toast for non-revalidation errors
      if (!isRevalidation) {
        toast({
          title: 'Error',
          description: errorResponse.message,
          variant: 'destructive',
        })
      }
    }
  }, [enabled, queryFn, supabase, onSuccess, onError, toast])

  // Initial fetch
  useEffect(() => {
    execute()

    return () => {
      mountedRef.current = false
      abortControllerRef.current?.abort()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...dependencies, execute])

  // Revalidate on focus
  useEffect(() => {
    if (!revalidateOnFocus || !enabled) return

    const handleFocus = () => {
      if (document.visibilityState === 'visible' && state.data !== null) {
        execute(true)
      }
    }

    window.addEventListener('focus', handleFocus)
    window.addEventListener('visibilitychange', handleFocus)

    return () => {
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('visibilitychange', handleFocus)
    }
  }, [revalidateOnFocus, enabled, state.data, execute])

  // Revalidate on reconnect
  useEffect(() => {
    if (!revalidateOnReconnect || !enabled) return

    const handleOnline = () => {
      if (state.data !== null) {
        execute(true)
      }
    }

    window.addEventListener('online', handleOnline)

    return () => {
      window.removeEventListener('online', handleOnline)
    }
  }, [revalidateOnReconnect, enabled, state.data, execute])

  // Refresh interval
  useEffect(() => {
    if (!refreshInterval || !enabled) return

    const interval = setInterval(() => {
      execute(true)
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [refreshInterval, enabled, execute])

  const mutate = useCallback(
    async (
      newData?: T | ((currentData: T | null) => T | null),
      shouldRevalidate = true
    ) => {
      if (typeof newData === 'function') {
        setState(prev => ({
          ...prev,
          data: (newData as any)(prev.data),
        }))
      } else if (newData !== undefined) {
        setState(prev => ({
          ...prev,
          data: newData,
        }))
      }

      if (shouldRevalidate) {
        await execute(true)
      }
    },
    [execute]
  )

  return {
    ...state,
    mutate,
    revalidate: () => execute(true),
  }
}

// Hook for mutations with optimistic updates
export function useSupabaseMutation<TData, TVariables>(
  mutationFn: (
    client: ReturnType<typeof createTypedBrowserClient>,
    variables: TVariables
  ) => Promise<{ data: TData | null; error: PostgrestError | null }>,
  options: {
    onSuccess?: (data: TData, variables: TVariables) => void
    onError?: (error: Error, variables: TVariables) => void
    onMutate?: (variables: TVariables) => Promise<unknown> | unknown
    onSettled?: (data: TData | null, error: Error | null, variables: TVariables) => void
  } = {}
) {
  const [state, setState] = useState({
    isLoading: false,
    error: null as Error | null,
  })

  const { toast } = useToast()
  const supabase = createTypedBrowserClient()

  const mutate = useCallback(
    async (variables: TVariables) => {
      setState({ isLoading: true, error: null })

      try {
        // Optimistic update
        if (options.onMutate) {
          await options.onMutate(variables)
        }

        const { data, error } = await mutationFn(supabase, variables)

        if (error) {
          throw error
        }

        setState({ isLoading: false, error: null })
        options.onSuccess?.(data!, variables)
        options.onSettled?.(data, null, variables)

        return data
      } catch (error) {
        const errorResponse = handleSupabaseError(error)
        const errorObj = new Error(errorResponse.message)

        setState({ isLoading: false, error: errorObj })
        options.onError?.(errorObj, variables)
        options.onSettled?.(null, errorObj, variables)

        toast({
          title: 'Error',
          description: errorResponse.message,
          variant: 'destructive',
        })

        // Re-throw for handling in the calling code
        throw errorObj
      }
    },
    [mutationFn, supabase, options, toast]
  )

  return {
    mutate,
    mutateAsync: mutate,
    ...state,
  }
}