"use client";

import { useEffect, useState } from "react";

interface SimpleTypewriterProps {
  words: string[];
  typingSpeed?: number;
  deletingSpeed?: number;
  delay?: number;
}

export function SimpleTypewriter({ 
  words, 
  typingSpeed = 100, 
  deletingSpeed = 50,
  delay = 2000 
}: SimpleTypewriterProps) {
  const [text, setText] = useState("");
  const [wordIndex, setWordIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const currentWord = words[wordIndex];
    
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        // Typing
        if (text !== currentWord) {
          setText(currentWord.slice(0, text.length + 1));
        } else {
          // Finished typing, wait then start deleting
          setTimeout(() => setIsDeleting(true), delay);
        }
      } else {
        // Deleting
        if (text !== "") {
          setText(text.slice(0, -1));
        } else {
          // Finished deleting, move to next word
          setIsDeleting(false);
          setWordIndex((prev) => (prev + 1) % words.length);
        }
      }
    }, isDeleting ? deletingSpeed : typingSpeed);

    return () => clearTimeout(timeout);
  }, [text, wordIndex, isDeleting, words, typingSpeed, deletingSpeed, delay]);

  return (
    <span className="inline-block">
      {text}
      <span className="animate-pulse">|</span>
    </span>
  );
}