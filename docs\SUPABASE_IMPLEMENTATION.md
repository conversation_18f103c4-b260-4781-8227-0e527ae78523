# Supabase Implementation Guide for BookScribe

This document consolidates Supabase best practices for Next.js 14+ App Router applications, covering authentication, Edge Functions, and security patterns.

## Table of Contents
1. [Authentication Architecture](#authentication-architecture)
2. [JWT Security & Management](#jwt-security--management)
3. [Edge Functions](#edge-functions)
4. [Server-Side Implementation](#server-side-implementation)
5. [Client-Side Implementation](#client-side-implementation)
6. [Security Best Practices](#security-best-practices)
7. [Common Patterns](#common-patterns)
8. [Migration Guide](#migration-guide)

## Authentication Architecture

### Recommended Stack
- **Package**: `@supabase/ssr` (replacing `@supabase/auth-helpers`)
- **Session Storage**: Cookies (not localStorage)
- **Architecture**: Separate server/client Supabase instances
- **Middleware**: Required for token refresh

### Key Components
```
app/
├── middleware.ts          # Token refresh & session management
├── utils/
│   └── supabase/
│       ├── client.ts     # Browser client
│       ├── server.ts     # Server client
│       └── middleware.ts # Session update logic
└── app/
    └── auth/
        └── callback/     # OAuth callback handler
```

## JWT Security & Management

### Three JWT Types in Supabase

1. **Anon Key** (`NEXT_PUBLIC_SUPABASE_ANON_KEY`)
   - Safe for client-side exposure
   - Requires Row Level Security (RLS)
   - Used in browser contexts

2. **Service Role Key** (`SUPABASE_SERVICE_ROLE_KEY`)
   - Super admin privileges
   - **NEVER expose to client**
   - Server-side only (API routes, Edge Functions)

3. **User Access Token**
   - Issued on login
   - Contains user metadata
   - Short expiration (1 hour default)
   - Must be refreshed via middleware

### JWT Best Practices
```typescript
// ❌ NEVER DO THIS - Insecure
const { data: { session } } = await supabase.auth.getSession()
const userId = session?.user.id // Can be spoofed!

// ✅ ALWAYS DO THIS - Secure
const { data: { user } } = await supabase.auth.getUser()
const userId = user?.id // Verified server-side
```

## Edge Functions

### Project Structure
```
supabase/
└── functions/
    ├── stripe-webhook/
    │   ├── index.ts
    │   └── deno.json
    ├── ai-generation/
    │   ├── index.ts
    │   └── deno.json
    └── _shared/
        └── cors.ts
```

### Dependencies Management
```json
// deno.json for each function
{
  "imports": {
    "@supabase/supabase-js": "npm:@supabase/supabase-js@2",
    "stripe": "npm:stripe@^13.0.0",
    "openai": "npm:openai@^4.0.0"
  }
}
```

### Authentication in Edge Functions
```typescript
import { createClient } from '@supabase/supabase-js'

Deno.serve(async (req) => {
  // Extract user token from request
  const authHeader = req.headers.get('Authorization')!
  
  // Create authenticated client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_ANON_KEY')!,
    {
      global: {
        headers: { Authorization: authHeader }
      }
    }
  )
  
  // Verify user
  const token = authHeader.replace('Bearer ', '')
  const { data: { user }, error } = await supabaseClient.auth.getUser(token)
  
  if (error || !user) {
    return new Response('Unauthorized', { status: 401 })
  }
  
  // Process authenticated request
})
```

### Webhook Security (Stripe Example)
```typescript
import Stripe from 'stripe'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY')!)

Deno.serve(async (request) => {
  const signature = request.headers.get('Stripe-Signature')
  const body = await request.text() // Important: raw body
  
  try {
    const event = await stripe.webhooks.constructEventAsync(
      body,
      signature!,
      Deno.env.get('STRIPE_WEBHOOK_SIGNING_SECRET')!
    )
    
    // Process webhook event
    switch (event.type) {
      case 'customer.subscription.created':
        // Handle subscription
        break
    }
    
    return new Response(JSON.stringify({ received: true }), { status: 200 })
  } catch (err) {
    return new Response(err.message, { status: 400 })
  }
})
```

## Server-Side Implementation

### Creating Server Clients
```typescript
// utils/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // Server Component - ignore
          }
        },
      },
    }
  )
}
```

### Middleware Configuration
```typescript
// middleware.ts
import { NextResponse, type NextRequest } from 'next/server'
import { updateSession } from '@/utils/supabase/middleware'

export async function middleware(request: NextRequest) {
  return await updateSession(request)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

### Server Actions
```typescript
// app/actions/auth.ts
'use server'

import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'

export async function signIn(formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string
  
  const supabase = await createClient()
  
  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  
  if (error) {
    return { error: error.message }
  }
  
  redirect('/dashboard')
}
```

## Client-Side Implementation

### Creating Browser Clients
```typescript
// utils/supabase/client.ts
import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

### Auth Context (Simplified)
```typescript
'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClient } from '@/utils/supabase/client'
import type { User } from '@supabase/supabase-js'

interface AuthContextType {
  user: User | null
  loading: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    // Get initial session
    supabase.auth.getUser().then(({ data: { user } }) => {
      setUser(user)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setUser(session?.user ?? null)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase])

  return (
    <AuthContext.Provider value={{ user, loading }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext)
```

## Security Best Practices

### 1. Row Level Security (RLS)
```sql
-- Enable RLS on all tables
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Users can only see their own projects
CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT USING (auth.uid() = user_id);

-- Users can only update their own projects
CREATE POLICY "Users can update own projects" ON projects
  FOR UPDATE USING (auth.uid() = user_id);
```

### 2. Environment Variables
```env
# .env.local
# Client-safe
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Server-only
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
STRIPE_SECRET_KEY=sk_test_...
OPENAI_API_KEY=sk-...
```

### 3. API Route Protection
```typescript
// app/api/admin/route.ts
import { createClient } from '@/utils/supabase/server'
import { NextResponse } from 'next/server'

export async function GET() {
  const supabase = await createClient()
  
  // Always verify user server-side
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  
  // Check admin role
  if (user.role !== 'admin') {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
  }
  
  // Process admin request
}
```

## Common Patterns

### 1. Protected Routes
```typescript
// app/dashboard/layout.tsx
import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  return <>{children}</>
}
```

### 2. Real-time Subscriptions
```typescript
'use client'

useEffect(() => {
  const channel = supabase
    .channel('chapters')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'chapters',
        filter: `project_id=eq.${projectId}`,
      },
      (payload) => {
        // Handle real-time updates
        console.log('Change received!', payload)
      }
    )
    .subscribe()
  
  return () => {
    supabase.removeChannel(channel)
  }
}, [projectId, supabase])
```

### 3. File Upload to Storage
```typescript
async function uploadFile(file: File) {
  const supabase = createClient()
  
  const { data, error } = await supabase.storage
    .from('documents')
    .upload(`${userId}/${file.name}`, file, {
      cacheControl: '3600',
      upsert: false,
    })
  
  if (error) throw error
  
  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from('documents')
    .getPublicUrl(data.path)
  
  return publicUrl
}
```

### 4. Calling Edge Functions
```typescript
async function generateChapter(projectId: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase.functions.invoke('ai-generation', {
    body: { projectId, action: 'generate-chapter' },
  })
  
  if (error) throw error
  return data
}
```

## Migration Guide

### From Auth Helpers to SSR Package

1. **Update Dependencies**
```bash
npm uninstall @supabase/auth-helpers-nextjs
npm install @supabase/ssr
```

2. **Update Imports**
```typescript
// Before
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

// After
import { createBrowserClient } from '@supabase/ssr'
```

3. **Update Client Creation**
```typescript
// Before
const supabase = createClientComponentClient()

// After
const supabase = createClient() // Using your utility function
```

### Key Changes
- Middleware is now required for all routes
- Session stored in cookies, not localStorage
- Different client creation for server/browser
- `getUser()` required for server-side auth checks
- Simplified API with better TypeScript support

## Production Checklist

- [ ] Enable RLS on all tables
- [ ] Configure production redirect URLs
- [ ] Set up auth hooks for custom logic
- [ ] Implement proper error handling
- [ ] Configure session expiration
- [ ] Set up monitoring and alerts
- [ ] Test auth flows thoroughly
- [ ] Verify webhook signatures
- [ ] Implement rate limiting
- [ ] Set up backup strategies

## Resources

- [Supabase SSR Package](https://github.com/supabase/auth-helpers)
- [Next.js App Router Docs](https://nextjs.org/docs/app)
- [Supabase Security Best Practices](https://supabase.com/docs/guides/auth/security)