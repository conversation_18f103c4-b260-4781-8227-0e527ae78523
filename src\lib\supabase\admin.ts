import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/db/types'
import { config } from '@/lib/config'

/**
 * Admin Supabase client with service role key
 * This bypasses RLS policies and should only be used for admin operations
 */
export function createAdminClient() {
  const supabaseUrl = config.supabase.url
  const serviceRoleKey = config.supabase.serviceRoleKey

  if (!supabaseUrl || !serviceRoleKey) {
    throw new Error('Missing Supabase admin configuration')
  }

  return createClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

/**
 * Admin operations that require service role key
 */
export const adminOperations = {
  /**
   * Get all users (admin only)
   */
  async getAllUsers() {
    const supabase = createAdminClient()
    const { data, error } = await supabase.auth.admin.listUsers()
    return { data, error }
  },

  /**
   * Delete user by ID (admin only)
   */
  async deleteUser(userId: string) {
    const supabase = createAdminClient()
    const { data, error } = await supabase.auth.admin.deleteUser(userId)
    return { data, error }
  },

  /**
   * Get user by email (admin only)
   */
  async getUserByEmail(email: string) {
    const supabase = createAdminClient()
    const { data, error } = await supabase.auth.admin.getUserById(email)
    return { data, error }
  },

  /**
   * Update user metadata (admin only)
   */
  async updateUserMetadata(userId: string, metadata: Record<string, unknown>) {
    const supabase = createAdminClient()
    const { data, error } = await supabase.auth.admin.updateUserById(userId, {
      user_metadata: metadata
    })
    return { data, error }
  },

  /**
   * Bypass RLS to get any project (admin only)
   */
  async getProjectById(projectId: string) {
    const supabase = createAdminClient()
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()
    return { data, error }
  },

  /**
   * Get all projects (admin only - bypasses RLS)
   */
  async getAllProjects() {
    const supabase = createAdminClient()
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false })
    return { data, error }
  },

  /**
   * Test admin connection
   */
  async testConnection() {
    try {
      const supabase = createAdminClient()
      
      // Test database access
      const { error: dbError } = await supabase
        .from('projects')
        .select('id')
        .limit(1)
      
      if (dbError) {
        return { success: false, error: 'Database connection failed', details: dbError.message }
      }

      // Test auth admin access
      const { data: users, error: authError } = await supabase.auth.admin.listUsers({
        page: 1,
        perPage: 1
      })

      if (authError) {
        return { success: false, error: 'Auth admin access failed', details: authError.message }
      }

      return {
        success: true,
        message: 'Admin client connected successfully',
        stats: {
          canAccessDatabase: true,
          canAccessAuthAdmin: true,
          userCount: users?.users?.length || 0
        }
      }
    } catch (error) {
      return {
        success: false,
        error: 'Admin client test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}
