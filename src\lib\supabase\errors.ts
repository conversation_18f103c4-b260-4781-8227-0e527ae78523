import { PostgrestError } from '@supabase/supabase-js'
import { AuthError } from '@supabase/supabase-js'
import { StorageError } from '@supabase/storage-js'
import { config } from '@/lib/config'

export type SupabaseError = PostgrestError | AuthError | StorageError

interface StorageErrorWithStatus {
  statusCode: number
  error: string
  message: string
}

interface ErrorWithCode {
  code: string
  [key: string]: unknown
}

interface ErrorWithStatusCode {
  statusCode: number
  [key: string]: unknown
}

export interface ErrorResponse {
  message: string
  code?: string
  details?: string
  statusCode?: number
}

// Map of Supabase error codes to user-friendly messages
const ERROR_MESSAGES: Record<string, string> = {
  // PostgreSQL errors
  'PGRST116': 'Resource not found',
  'PGRST204': 'No rows found',
  '23505': 'This item already exists',
  '23503': 'Cannot delete - item is referenced elsewhere',
  '23502': 'Required field is missing',
  '42501': 'Insufficient permissions',
  '42P01': 'Table does not exist',
  '42703': 'Column does not exist',
  '22P02': 'Invalid input format',
  
  // Auth errors
  'invalid_credentials': 'Invalid email or password',
  'email_not_confirmed': 'Please verify your email address',
  'user_already_exists': 'An account with this email already exists',
  'weak_password': 'Password is too weak. Please use a stronger password',
  'over_request_rate_limit': 'Too many requests. Please try again later',
  'invalid_jwt': 'Session expired. Please sign in again',
  'session_not_found': 'Session not found. Please sign in again',
  
  // Storage errors
  'Bucket not found': 'Storage bucket not found',
  'Object not found': 'File not found',
  'Unauthorized': 'You do not have permission to access this file',
  'Payload too large': 'File size exceeds the maximum allowed',
}

// Map PostgreSQL constraint names to user-friendly messages
const CONSTRAINT_MESSAGES: Record<string, string> = {
  'projects_user_id_fkey': 'User not found',
  'chapters_project_id_fkey': 'Project not found',
  'characters_project_id_fkey': 'Project not found',
  'unique_project_name_per_user': 'You already have a project with this name',
  'unique_chapter_number_per_project': 'A chapter with this number already exists',
}

export function handleSupabaseError(error: SupabaseError | Error | unknown): ErrorResponse {
  // If it's not an error object, return generic message
  if (!error || typeof error !== 'object') {
    return {
      message: 'An unexpected error occurred',
      statusCode: 500,
    }
  }

  // Handle PostgreSQL errors
  if ('code' in error && typeof error.code === 'string') {
    const pgError = error as PostgrestError
    
    // Check for specific error codes
    if (ERROR_MESSAGES[pgError.code]) {
      return {
        message: ERROR_MESSAGES[pgError.code] || 'Database error occurred',
        code: pgError.code,
        details: config.isDevelopment ? pgError.message : undefined,
        statusCode: getStatusCodeFromPgError(pgError.code),
      }
    }
    
    // Check for constraint violations
    if (pgError.code === '23503' && pgError.details) {
      const constraintMatch = pgError.details.match(/constraint "([^"]+)"/)
      if (constraintMatch && CONSTRAINT_MESSAGES[constraintMatch[1]]) {
        return {
          message: CONSTRAINT_MESSAGES[constraintMatch[1]],
          code: pgError.code,
          details: config.isDevelopment ? pgError.message : undefined,
          statusCode: 409, // Conflict
        }
      }
    }
  }

  // Handle Auth errors
  if ('name' in error && error.name === 'AuthError') {
    const authError = error as AuthError
    const message = ERROR_MESSAGES[authError.message] || authError.message
    
    return {
      message,
      code: 'AUTH_ERROR',
      details: config.isDevelopment ? authError.message : undefined,
      statusCode: getAuthErrorStatusCode(authError),
    }
  }

  // Handle Storage errors
  if ('statusCode' in error && 'error' in error) {
    const storageError = error as StorageErrorWithStatus
    const message = ERROR_MESSAGES[storageError.message] || storageError.message

    return {
      message,
      code: 'STORAGE_ERROR',
      details: config.isDevelopment ? storageError.message : undefined,
      statusCode: storageError.statusCode || 500,
    }
  }

  // Handle generic Error instances
  if (error instanceof Error) {
    return {
      message: error.message || 'An unexpected error occurred',
      code: 'UNKNOWN_ERROR',
      details: config.isDevelopment ? error.stack : undefined,
      statusCode: 500,
    }
  }

  // Fallback
  return {
    message: 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR',
    statusCode: 500,
  }
}

function getStatusCodeFromPgError(code: string): number {
  switch (code) {
    case 'PGRST116':
    case 'PGRST204':
      return 404 // Not Found
    case '23505':
    case '23503':
      return 409 // Conflict
    case '23502':
    case '22P02':
      return 400 // Bad Request
    case '42501':
      return 403 // Forbidden
    case '42P01':
    case '42703':
      return 500 // Internal Server Error
    default:
      return 500
  }
}

function getAuthErrorStatusCode(error: AuthError): number {
  if (error.message.includes('invalid_credentials')) return 401
  if (error.message.includes('not_authorized')) return 403
  if (error.message.includes('user_already_exists')) return 409
  if (error.message.includes('rate_limit')) return 429
  return 401 // Default to Unauthorized
}

// Retry logic for transient errors
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: {
    maxRetries?: number
    initialDelay?: number
    maxDelay?: number
    backoffFactor?: number
    retryOn?: (error: unknown) => boolean
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    retryOn = isRetryableError,
  } = options

  let lastError: unknown
  let delay = initialDelay

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error

      if (attempt === maxRetries || !retryOn(error)) {
        throw error
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay))

      // Increase delay for next attempt
      delay = Math.min(delay * backoffFactor, maxDelay)
    }
  }

  throw lastError
}

function isRetryableError(error: unknown): boolean {
  if (!error || typeof error !== 'object') return false

  // Retry on network errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return true
  }

  // Retry on specific PostgreSQL errors
  if ('code' in error) {
    const errorWithCode = error as ErrorWithCode
    const code = errorWithCode.code
    // Connection errors, timeouts, etc.
    return ['08000', '08003', '08006', '57P03', '58000', '58030'].includes(code)
  }

  // Retry on rate limit errors
  if ('statusCode' in error) {
    const errorWithStatus = error as ErrorWithStatusCode
    return errorWithStatus.statusCode === 429
  }

  return false
}

// Type guard for Supabase errors
export function isSupabaseError(error: unknown): error is SupabaseError {
  return (
    error !== null &&
    typeof error === 'object' &&
    ('code' in error || 'statusCode' in error || 'name' in error)
  )
}