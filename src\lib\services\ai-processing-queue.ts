import { OpenAI } from 'openai';
import { db } from '@/lib/db/client';
import { config } from '@/lib/config';

export interface ProcessingTask {
  id: string;
  projectId: string;
  userId: string;
  type: 'content_analysis' | 'character_development' | 'plot_consistency' | 'pacing_analysis' | 'voice_matching' | 'world_building' | 'timeline_validation';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  payload: {
    chapterId?: string;
    content?: string;
    context?: Record<string, unknown>;
    parameters?: Record<string, unknown>;
  };
  result?: Record<string, unknown>;
  error?: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  estimatedDuration: number; // in seconds
}

export interface ProcessingProgress {
  projectId: string;
  currentTask?: ProcessingTask;
  queuedTasks: ProcessingTask[];
  completedTasks: ProcessingTask[];
  totalTasks: number;
  completedCount: number;
  isActive: boolean;
  estimatedTimeRemaining: number; // in seconds
}

class AIProcessingQueue {
  private static instance: AIProcessingQueue;
  private openai: OpenAI;
  private processingTasks = new Map<string, ProcessingTask>();
  private projectQueues = new Map<string, ProcessingTask[]>();
  private isProcessing = false;
  private maxConcurrentTasks = 3;
  private activeTasks = new Set<string>();

  private constructor() {
    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });
    this.startProcessingLoop();
  }

  static getInstance(): AIProcessingQueue {
    if (!AIProcessingQueue.instance) {
      AIProcessingQueue.instance = new AIProcessingQueue();
    }
    return AIProcessingQueue.instance;
  }

  async addTask(task: Omit<ProcessingTask, 'id' | 'createdAt' | 'status'>): Promise<string> {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullTask: ProcessingTask = {
      ...task,
      id: taskId,
      status: 'pending',
      createdAt: new Date(),
    };

    this.processingTasks.set(taskId, fullTask);

    if (!this.projectQueues.has(task.projectId)) {
      this.projectQueues.set(task.projectId, []);
    }
    this.projectQueues.get(task.projectId)!.push(fullTask);

    // Sort by priority and creation time
    this.projectQueues.set(task.projectId, 
      this.projectQueues.get(task.projectId)!.sort((a, b) => {
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return a.createdAt.getTime() - b.createdAt.getTime();
      })
    );

    // Store in database
    await this.saveTaskToDatabase(fullTask);

    return taskId;
  }

  async getProjectProgress(projectId: string): Promise<ProcessingProgress> {
    const projectQueue = this.projectQueues.get(projectId) || [];
    const queuedTasks = projectQueue.filter(t => t.status === 'pending');
    const processingTasks = projectQueue.filter(t => t.status === 'processing');
    const completedTasks = projectQueue.filter(t => t.status === 'completed' || t.status === 'failed');

    const currentTask = processingTasks[0];
    const totalTasks = projectQueue.length;
    const completedCount = completedTasks.length;

    const estimatedTimeRemaining = queuedTasks.reduce((sum, task) => sum + task.estimatedDuration, 0) +
      (currentTask ? Math.max(0, currentTask.estimatedDuration - this.getTaskElapsedTime(currentTask)) : 0);

    return {
      projectId,
      currentTask,
      queuedTasks,
      completedTasks,
      totalTasks,
      completedCount,
      isActive: processingTasks.length > 0 || queuedTasks.length > 0,
      estimatedTimeRemaining,
    };
  }

  async getAllProjectsProgress(userId: string): Promise<Record<string, ProcessingProgress>> {
    const userProjects = await this.getUserProjects(userId);
    const progressMap: Record<string, ProcessingProgress> = {};

    for (const projectId of userProjects) {
      progressMap[projectId] = await this.getProjectProgress(projectId);
    }

    return progressMap;
  }

  async getBatchProjectProgress(projectIds: string[]): Promise<Record<string, ProcessingProgress>> {
    const progressMap: Record<string, ProcessingProgress> = {};

    // Process all projects in parallel for better performance
    await Promise.all(
      projectIds.map(async (projectId) => {
        progressMap[projectId] = await this.getProjectProgress(projectId);
      })
    );

    return progressMap;
  }

  private async startProcessingLoop() {
    if (this.isProcessing) return;
    this.isProcessing = true;

    while (true) {
      if (this.activeTasks.size < this.maxConcurrentTasks) {
        const nextTask = this.getNextTask();
        if (nextTask) {
          this.processTask(nextTask);
        }
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000)); // Check every second
    }
  }

  private getNextTask(): ProcessingTask | null {
    for (const [, queue] of this.projectQueues.entries()) {
      const pendingTask = queue.find(task => 
        task.status === 'pending' && !this.activeTasks.has(task.id)
      );
      if (pendingTask) {
        return pendingTask;
      }
    }
    return null;
  }

  private async processTask(task: ProcessingTask) {
    this.activeTasks.add(task.id);
    task.status = 'processing';
    task.startedAt = new Date();

    await this.updateTaskInDatabase(task);

    try {
      const result = await this.executeTask(task);
      task.result = result;
      task.status = 'completed';
      task.completedAt = new Date();
    } catch (error) {
      task.error = error instanceof Error ? error.message : 'Unknown error';
      task.status = 'failed';
      task.completedAt = new Date();
    } finally {
      this.activeTasks.delete(task.id);
      await this.updateTaskInDatabase(task);
    }
  }

  private async executeTask(task: ProcessingTask): Promise<Record<string, unknown>> {
    const { type, payload } = task;

    switch (type) {
      case 'content_analysis':
        return await this.analyzeContent(payload);
      case 'character_development':
        return await this.analyzeCharacterDevelopment(payload);
      case 'plot_consistency':
        return await this.checkPlotConsistency(payload);
      case 'pacing_analysis':
        return await this.analyzePacing(payload);
      case 'voice_matching':
        return await this.analyzeVoiceMatching(payload);
      case 'world_building':
        return await this.analyzeWorldBuilding(payload);
      case 'timeline_validation':
        return await this.validateTimeline(payload);
      default:
        throw new Error(`Unknown task type: ${type}`);
    }
  }

  private async analyzeContent(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are an expert literary analyst. Analyze the provided text for:
          - Plot holes and inconsistencies
          - Character development issues
          - Pacing problems
          - Dialogue quality
          - Show vs tell balance
          - Emotional resonance
          
          Provide specific, actionable feedback with line references where possible.`
        },
        {
          role: 'user',
          content: `Analyze this content:\n\n${payload.content}`
        }
      ],
      temperature: 0.3,
    });

    const messageContent = response.choices[0]?.message?.content;
    return {
      analysis: messageContent || 'No analysis available',
      suggestions: this.extractSuggestions(messageContent || ''),
      timestamp: new Date(),
    };
  }

  private async analyzeCharacterDevelopment(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are a character development expert. Analyze character arcs, consistency, growth, and relationships. 
          Look for character motivation clarity, believable change, and authentic dialogue.`
        },
        {
          role: 'user',
          content: `Analyze character development in this content:\n\n${payload.content}\n\nContext: ${JSON.stringify(payload.context)}`
        }
      ],
      temperature: 0.3,
    });

    const messageContent = response.choices[0]?.message?.content;
    return {
      characterAnalysis: messageContent || 'No analysis available',
      arcProgress: this.calculateArcProgress(payload),
      timestamp: new Date(),
    };
  }

  private async checkPlotConsistency(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are a plot consistency checker. Identify logical inconsistencies, timeline issues, 
          and continuity errors. Cross-reference with established story elements.`
        },
        {
          role: 'user',
          content: `Check plot consistency:\n\nNew content: ${payload.content}\n\nStory context: ${JSON.stringify(payload.context)}`
        }
      ],
      temperature: 0.2,
    });

    return {
      consistencyReport: response.choices[0]?.message?.content || 'No report available',
      issues: this.extractIssues(response.choices[0]?.message?.content || ''),
      timestamp: new Date(),
    };
  }

  private async analyzePacing(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are a pacing expert. Analyze narrative rhythm, tension curves, 
          scene transitions, and overall story flow. Identify pacing issues and suggest improvements.`
        },
        {
          role: 'user',
          content: `Analyze pacing:\n\n${payload.content}`
        }
      ],
      temperature: 0.3,
    });

    return {
      pacingAnalysis: response.choices[0]?.message?.content || 'No analysis available',
      tensionCurve: this.generateTensionCurve(payload),
      timestamp: new Date(),
    };
  }

  private async analyzeVoiceMatching(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are a voice and style analyst. Compare this text to the established voice pattern 
          and identify any inconsistencies in tone, vocabulary, sentence structure, or narrative style.`
        },
        {
          role: 'user',
          content: `Compare voice consistency:\n\nNew content: ${payload.content}\n\nReference style: ${JSON.stringify((payload.context as Record<string, unknown>)?.voiceProfile || {})}`
        }
      ],
      temperature: 0.3,
    });

    return {
      voiceAnalysis: response.choices[0]?.message?.content || 'No analysis available',
      consistencyScore: this.calculateVoiceConsistency(payload),
      timestamp: new Date(),
    };
  }

  private async analyzeWorldBuilding(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are a world-building expert. Check for internal consistency in the fictional world, 
          including geography, culture, technology, magic systems, and social structures.`
        },
        {
          role: 'user',
          content: `Analyze world-building consistency:\n\n${payload.content}\n\nWorld context: ${JSON.stringify(payload.context)}`
        }
      ],
      temperature: 0.3,
    });

    return {
      worldBuildingAnalysis: response.choices[0]?.message?.content || 'No analysis available',
      consistencyIssues: this.extractWorldBuildingIssues(response.choices[0]?.message?.content || ''),
      timestamp: new Date(),
    };
  }

  private async validateTimeline(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are a timeline validation expert. Check chronological consistency, 
          verify that events happen in logical order, and identify any temporal inconsistencies.`
        },
        {
          role: 'user',
          content: `Validate timeline:\n\n${payload.content}\n\nTimeline context: ${JSON.stringify(payload.context)}`
        }
      ],
      temperature: 0.2,
    });

    return {
      timelineAnalysis: response.choices[0]?.message?.content || 'No analysis available',
      chronologyIssues: this.extractTimelineIssues(response.choices[0]?.message?.content || ''),
      timestamp: new Date(),
    };
  }

  // Helper methods
  private extractSuggestions(analysis: string): string[] {
    const lines = analysis.split('\n');
    return lines.filter(line => 
      line.includes('suggest') || line.includes('recommend') || line.includes('consider')
    );
  }

  private extractIssues(report: string): string[] {
    const lines = report.split('\n');
    return lines.filter(line => 
      line.includes('inconsistent') || line.includes('error') || line.includes('issue')
    );
  }

  private extractWorldBuildingIssues(analysis: string): string[] {
    return this.extractIssues(analysis);
  }

  private extractTimelineIssues(analysis: string): string[] {
    return this.extractIssues(analysis);
  }

  private calculateArcProgress(payload: ProcessingTask['payload']): number {
    // Simple calculation based on content length and context
    return Math.min(100, (payload.content?.length || 0) / 1000 * 10);
  }

  private generateTensionCurve(payload: ProcessingTask['payload']): number[] {
    // Generate a simple tension curve based on content analysis
    const contentLength = payload.content?.length || 0;
    const segments = Math.min(10, Math.max(3, Math.floor(contentLength / 500)));
    return Array.from({ length: segments }, () => Math.random() * 100);
  }

  private calculateVoiceConsistency(_payload: ProcessingTask['payload']): number {
    // Simple scoring based on content comparison
    return Math.floor(Math.random() * 30) + 70; // Mock score between 70-100
  }

  private getTaskElapsedTime(task: ProcessingTask): number {
    if (!task.startedAt) return 0;
    return Math.floor((Date.now() - task.startedAt.getTime()) / 1000);
  }

  private async saveTaskToDatabase(task: ProcessingTask) {
    try {
      await db.processing.createTask({
        id: task.id,
        project_id: task.projectId,
        user_id: task.userId,
        type: task.type,
        status: task.status,
        priority: task.priority,
        payload: task.payload,
        estimated_duration: task.estimatedDuration,
        created_at: task.createdAt.toISOString(),
      });
    } catch (error) {
      console.error('Failed to save task to database:', error);
    }
  }

  private async updateTaskInDatabase(task: ProcessingTask) {
    try {
      await db.processing.updateTask(task.id, {
        status: task.status,
        result: task.result,
        error: task.error,
        started_at: task.startedAt?.toISOString(),
        completed_at: task.completedAt?.toISOString(),
        actual_duration: task.completedAt && task.startedAt ? 
          Math.floor((task.completedAt.getTime() - task.startedAt.getTime()) / 1000) : null,
      });
    } catch (error) {
      console.error('Failed to update task in database:', error);
    }
  }

  private async getUserProjects(userId: string): Promise<string[]> {
    try {
      const projects = await db.projects.getAll(userId);
      return projects.map(p => p.id);
    } catch (error) {
      console.error('Failed to get user projects:', error);
      return [];
    }
  }
}

export const aiProcessingQueue = AIProcessingQueue.getInstance();