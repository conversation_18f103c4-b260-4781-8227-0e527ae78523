import { NextResponse } from 'next/server';
import { db } from '@/lib/db/client';
import { authenticateUser, handleRouteError } from '@/lib/auth';

export async function GET() {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const { user } = authResult;
    const userId = user.id;

    const profiles = await db.selectionProfiles.getUserProfiles(userId);
    return NextResponse.json({ profiles });
  } catch (error) {
    return handleRouteError(error, 'User Profiles GET');
  }
}