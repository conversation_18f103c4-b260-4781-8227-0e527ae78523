// Project Configuration Types based on PRD specifications

export interface ProjectConfig {
  // Project Basics
  name: string;
  description: string;
  targetAudience: string;
  contentRating: string;
  projectScope: string;
  initialConcept: string;

  // Genre & Style Configuration
  primaryGenre: string;
  subgenre: string;
  customGenre?: string;
  narrativeVoice: string;
  tense: string;
  toneOptions: string[];
  writingStyle: string;
  customStyleDescription?: string;

  // Story Structure & Pacing
  structureType: string;
  pacingPreference: string;
  chapterStructure: string;
  timelineComplexity: string;
  customStructureNotes?: string;

  // Character & World Building
  protagonistTypes: string[];
  antagonistTypes: string[];
  characterComplexity: string;
  characterArcTypes: string[];
  customCharacterConcepts?: string;
  timePeriod: string;
  geographicSetting: string;
  worldType: string;
  magicTechLevel: string;
  customSettingDescription?: string;

  // Themes & Content
  majorThemes: string[];
  philosophicalThemes: string[];
  socialThemes: string[];
  customThemes?: string;
  contentWarnings: string[];
  culturalSensitivityNotes?: string;

  // Series & Scope
  seriesType?: string;
  interconnectionLevel?: string;
  customScopeDescription?: string;

  // Technical Specifications
  targetWordCount: number;
  targetChapters: number;
  chapterCountType: string;
  povCharacterCount: number;
  povCharacterType: string;

  // Research & References
  researchNeeds: string[];
  factCheckingLevel: string;
  customResearchNotes?: string;
}

export interface SelectionProfile {
  id: string;
  name: string;
  description: string;
  isPublic: boolean;
  config: Partial<ProjectConfig>;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

// Genre Options
export const GENRES = {
  primary: [
    'Fantasy',
    'Science Fiction',
    'Mystery/Thriller',
    'Romance',
    'Historical Fiction',
    'Literary Fiction',
    'Horror',
    'Adventure',
    'Young Adult',
    'Contemporary Fiction'
  ],
  subgenres: {
    'Fantasy': [
      'Epic Fantasy',
      'Urban Fantasy',
      'Dark Fantasy',
      'High Fantasy',
      'Low Fantasy',
      'Sword & Sorcery',
      'Magical Realism'
    ],
    'Science Fiction': [
      'Space Opera',
      'Cyberpunk',
      'Dystopian',
      'Time Travel',
      'Hard Sci-Fi',
      'Soft Sci-Fi',
      'Steampunk',
      'Biopunk'
    ],
    'Mystery/Thriller': [
      'Cozy Mystery',
      'Police Procedural',
      'Noir',
      'Psychological Thriller',
      'Legal Thriller',
      'Espionage'
    ]
  }
};

// Writing Style & Tone Options
export const WRITING_OPTIONS = {
  narrativeVoice: [
    'First Person',
    'Third Person Limited',
    'Third Person Omniscient',
    'Second Person',
    'Multiple POV'
  ],
  tense: ['Present', 'Past', 'Mixed'],
  toneOptions: [
    'Dark & Gritty',
    'Light & Humorous',
    'Epic & Heroic',
    'Intimate & Personal',
    'Mysterious & Suspenseful',
    'Romantic & Passionate',
    'Philosophical & Contemplative'
  ],
  writingStyle: [
    'Literary',
    'Commercial',
    'Pulp',
    'Experimental',
    'Minimalist',
    'Descriptive',
    'Dialogue-Heavy'
  ]
};

// Story Structure Options
export const STRUCTURE_OPTIONS = {
  structureTypes: [
    'Three-Act Structure',
    'Hero\'s Journey',
    'Save the Cat',
    'Freytag\'s Pyramid',
    'Seven-Point Story Structure',
    'Fichtean Curve'
  ],
  pacingPreferences: [
    'Fast-Paced Action',
    'Slow Burn',
    'Balanced',
    'Character-Driven',
    'Plot-Driven'
  ],
  chapterStructures: [
    'Fixed Length',
    'Variable Length',
    'Scene-Based',
    'Time-Based'
  ],
  timelineComplexity: [
    'Linear',
    'Flashbacks',
    'Multiple Timelines',
    'Non-Linear Narrative'
  ]
};

// Character Options
export const CHARACTER_OPTIONS = {
  protagonistTypes: [
    'The Hero',
    'The Antihero',
    'The Reluctant Hero',
    'The Tragic Hero',
    'The Everyman',
    'The Mentor',
    'The Innocent'
  ],
  antagonistTypes: [
    'The Villain',
    'The Shadow',
    'The Rival',
    'The Skeptic',
    'The Threshold Guardian',
    'The Shapeshifter'
  ],
  characterComplexity: [
    'Simple/Archetypal',
    'Complex/Layered',
    'Morally Ambiguous',
    'Ensemble Cast'
  ],
  characterArcTypes: [
    'Positive Change',
    'Negative Change',
    'Flat Arc',
    'Corruption Arc',
    'Redemption Arc'
  ]
};

// Setting & World Building Options
export const SETTING_OPTIONS = {
  timePeriods: [
    'Contemporary',
    'Historical - Ancient',
    'Historical - Medieval',
    'Historical - Renaissance',
    'Historical - 18th Century',
    'Historical - 19th Century',
    'Historical - Early 20th Century',
    'Historical - Mid 20th Century',
    'Near Future',
    'Far Future',
    'Alternate History',
    'Timeless'
  ],
  geographicSettings: [
    'Urban',
    'Rural',
    'Suburban',
    'Wilderness',
    'Island',
    'Underground',
    'Space',
    'Alternate Dimension'
  ],
  worldTypes: [
    'Real World',
    'Alternate Reality',
    'Fantasy World',
    'Sci-Fi Universe',
    'Post-Apocalyptic',
    'Steampunk',
    'Cyberpunk'
  ],
  magicTechLevels: [
    'No Magic/Current Tech',
    'Low Magic/Near Future Tech',
    'High Magic/Advanced Tech',
    'Magitech Fusion'
  ]
};

// Theme Options
export const THEME_OPTIONS = {
  majorThemes: [
    'Love & Relationships',
    'Good vs Evil',
    'Coming of Age',
    'Redemption',
    'Sacrifice',
    'Power & Corruption',
    'Identity',
    'Family',
    'Survival',
    'Justice'
  ],
  philosophicalThemes: [
    'Existentialism',
    'Morality',
    'Free Will vs Destiny',
    'Nature vs Nurture',
    'Technology vs Humanity'
  ],
  socialThemes: [
    'Class Struggle',
    'Prejudice & Discrimination',
    'War & Peace',
    'Environmental Issues',
    'Political Intrigue'
  ]
};

// Content & Audience Options
export const CONTENT_OPTIONS = {
  targetAudiences: [
    'Children (8-12)',
    'Young Adult (13-17)',
    'New Adult (18-25)',
    'Adult (25+)',
    'All Ages'
  ],
  contentRatings: [
    'G (General)',
    'PG (Mild Content)',
    'PG-13 (Moderate Content)',
    'R (Mature Content)',
    'NC-17 (Explicit Content)'
  ],
  contentWarnings: [
    'Violence',
    'Sexual Content',
    'Strong Language',
    'Substance Use',
    'Mental Health Topics',
    'Death/Grief',
    'Abuse',
    'Discrimination'
  ]
};

// Project Scope Options
export const SCOPE_OPTIONS = {
  projectScopes: [
    'Standalone Novel',
    'Duology',
    'Trilogy',
    'Series (4-7 books)',
    'Epic Series (8+ books)',
    'Anthology'
  ],
  seriesTypes: [
    'Sequential',
    'Parallel Timelines',
    'Different Characters Same World',
    'Generational Saga'
  ],
  interconnectionLevels: [
    'Loose Connection',
    'Moderate Connection',
    'Tight Continuity',
    'Shared Universe'
  ]
};

// Technical Specifications
export const TECHNICAL_OPTIONS = {
  targetWordCounts: [
    { label: 'Short Novel (40k-60k)', value: 50000 },
    { label: 'Standard Novel (70k-100k)', value: 85000 },
    { label: 'Long Novel (100k-150k)', value: 125000 },
    { label: 'Epic Novel (150k-300k)', value: 225000 }
  ],
  chapterCountTypes: [
    'Fixed Number',
    'Flexible Range',
    'Scene-Based Division'
  ],
  povCharacterTypes: [
    'Single POV',
    'Dual POV',
    'Multiple POV (3-5)',
    'Ensemble Cast (6+)'
  ]
};

// Research Options
export const RESEARCH_OPTIONS = {
  researchNeeds: [
    'Historical Accuracy',
    'Scientific Accuracy',
    'Cultural Authenticity',
    'Technical Expertise'
  ],
  factCheckingLevels: [
    'Minimal',
    'Moderate',
    'Extensive',
    'Expert Review Required'
  ]
};