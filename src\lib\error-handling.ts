import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
// Removed config import to avoid initialization issues

/**
 * Standard error response format
 */
export interface ErrorResponse {
  error: string;
  details?: unknown;
  code?: string;
  statusCode: number;
}

/**
 * Custom application error class
 */
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'AppError';
  }
}

/**
 * Common error types with standard messages and codes
 */
export const ErrorTypes = {
  VALIDATION_ERROR: {
    message: 'Validation failed',
    statusCode: 400,
    code: 'VALIDATION_ERROR'
  },
  AUTHENTICATION_ERROR: {
    message: 'Authentication failed',
    statusCode: 401,
    code: 'AUTH_ERROR'
  },
  AUTHORIZATION_ERROR: {
    message: 'Insufficient permissions',
    statusCode: 403,
    code: 'FORBIDDEN'
  },
  NOT_FOUND: {
    message: 'Resource not found',
    statusCode: 404,
    code: 'NOT_FOUND'
  },
  RATE_LIMIT_ERROR: {
    message: 'Too many requests',
    statusCode: 429,
    code: 'RATE_LIMIT_EXCEEDED'
  },
  INTERNAL_ERROR: {
    message: 'Internal server error',
    statusCode: 500,
    code: 'INTERNAL_ERROR'
  },
  SERVICE_UNAVAILABLE: {
    message: 'Service temporarily unavailable',
    statusCode: 503,
    code: 'SERVICE_UNAVAILABLE'
  }
} as const;

/**
 * Handles errors and returns a standardized NextResponse
 */
export function handleApiError(error: unknown): NextResponse<ErrorResponse> {
  console.error('API Error:', error);

  // Handle known error types
  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        details: error.details,
        statusCode: error.statusCode
      },
      { status: error.statusCode }
    );
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return NextResponse.json(
      {
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: error.errors,
        statusCode: 400
      },
      { status: 400 }
    );
  }

  // Handle standard errors
  if (error instanceof Error) {
    // Check for specific error messages
    if (error.message.includes('rate limit')) {
      return NextResponse.json(
        {
          error: ErrorTypes.RATE_LIMIT_ERROR.message,
          code: ErrorTypes.RATE_LIMIT_ERROR.code,
          statusCode: ErrorTypes.RATE_LIMIT_ERROR.statusCode
        },
        { status: ErrorTypes.RATE_LIMIT_ERROR.statusCode }
      );
    }

    if (error.message.includes('not found')) {
      return NextResponse.json(
        {
          error: ErrorTypes.NOT_FOUND.message,
          code: ErrorTypes.NOT_FOUND.code,
          statusCode: ErrorTypes.NOT_FOUND.statusCode
        },
        { status: ErrorTypes.NOT_FOUND.statusCode }
      );
    }

    // Default error response
    return NextResponse.json(
      {
        error: error.message || ErrorTypes.INTERNAL_ERROR.message,
        code: ErrorTypes.INTERNAL_ERROR.code,
        statusCode: ErrorTypes.INTERNAL_ERROR.statusCode
      },
      { status: ErrorTypes.INTERNAL_ERROR.statusCode }
    );
  }

  // Unknown error type
  return NextResponse.json(
    {
      error: ErrorTypes.INTERNAL_ERROR.message,
      code: ErrorTypes.INTERNAL_ERROR.code,
      statusCode: ErrorTypes.INTERNAL_ERROR.statusCode
    },
    { status: ErrorTypes.INTERNAL_ERROR.statusCode }
  );
}

/**
 * Wraps an async API route handler with error handling
 */
export function withErrorHandling<T extends (...args: Parameters<T>) => Promise<NextResponse>>(
  handler: T
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await handler(...args);
    } catch (error) {
      return handleApiError(error);
    }
  }) as T;
}

/**
 * Creates a standardized error response
 */
export function createErrorResponse(
  type: keyof typeof ErrorTypes,
  customMessage?: string,
  details?: unknown
): NextResponse<ErrorResponse> {
  const errorType = ErrorTypes[type];
  return NextResponse.json(
    {
      error: customMessage || errorType.message,
      code: errorType.code,
      details,
      statusCode: errorType.statusCode
    },
    { status: errorType.statusCode }
  );
}

/**
 * Logs errors with context
 */
export function logError(
  error: unknown,
  context: {
    userId?: string;
    projectId?: string;
    action?: string;
    metadata?: Record<string, unknown>;
  }
): void {
  const timestamp = new Date().toISOString();
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  const errorStack = error instanceof Error ? error.stack : undefined;

  console.error(`[${timestamp}] Error in ${context.action || 'unknown action'}:`, {
    message: errorMessage,
    stack: errorStack,
    userId: context.userId,
    projectId: context.projectId,
    metadata: context.metadata,
    error
  });

  // In production, you would send this to an error tracking service
  if (process.env.NODE_ENV === 'production') {
    // TODO: Send to error tracking service (e.g., Sentry)
  }
}

/**
 * Validates required fields and throws AppError if validation fails
 */
export function validateRequired<T extends Record<string, unknown>>(
  data: T,
  requiredFields: (keyof T)[]
): void {
  const missingFields = requiredFields.filter(field => !data[field]);
  
  if (missingFields.length > 0) {
    throw new AppError(
      `Missing required fields: ${missingFields.join(', ')}`,
      400,
      'MISSING_FIELDS',
      { missingFields }
    );
  }
}