import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { orchestratorInstances } from '@/lib/agents/orchestrator-instances';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import type { ProjectSettings } from '@/lib/types/project-settings';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      projectId, 
      projectSelections, 
      storyPrompt, 
      targetWordCount, 
      targetChapters 
    } = body;

    if (!projectId || !projectSelections || !storyPrompt) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Check if orchestration is already running for this project
    if (orchestratorInstances.has(projectId)) {
      return NextResponse.json(
        { error: 'Orchestration already running for this project' },
        { status: 409 }
      );
    }

    // Create new orchestrator instance
    const orchestrator = new AdvancedAgentOrchestrator(3); // 3 concurrent tasks
    orchestratorInstances.set(projectId, orchestrator);

    // Set up event listeners for progress tracking
    orchestrator.on('orchestration:started', (data) => {
      console.log(`=� Orchestration started for project ${projectId}:`, data);
    });

    orchestrator.on('task:started', (data) => {
      console.log(`� Task started: ${data.taskId} (${data.type})`);
    });

    orchestrator.on('task:completed', (data) => {
      console.log(` Task completed: ${data.taskId}`);
    });

    orchestrator.on('task:failed', (data) => {
      console.log(`L Task failed: ${data.taskId} - ${data.error}`);
    });

    orchestrator.on('orchestration:completed', async (data) => {
      console.log(`<� Orchestration completed for project ${projectId}:`, data);
      
      // Clean up the instance after completion
      setTimeout(() => {
        orchestratorInstances.delete(projectId);
      }, 30000); // Keep for 30 seconds for final status checks
    });

    orchestrator.on('orchestration:cancelled', () => {
      console.log(`=� Orchestration cancelled for project ${projectId}`);
      orchestratorInstances.delete(projectId);
    });

    // Start orchestration asynchronously
    const orchestrationPromise = orchestrator.orchestrateProject(
      projectId,
      projectSelections as ProjectSettings,
      storyPrompt,
      targetWordCount || 80000,
      targetChapters || 20
    );

    // Don't await the orchestration - let it run in background
    orchestrationPromise.then(async (result) => {
      if (result.success && result.data) {
        // Store the completed book context in the database
        try {
          const supabase = await createServerSupabaseClient();
          const { error: updateError } = await supabase
            .from('projects')
            .update({
              book_context: result.data,
              status: 'outlined',
              updated_at: new Date().toISOString()
            })
            .eq('id', projectId);

          if (updateError) {
            console.error('Failed to update project with book context:', updateError);
          } else {
            console.log(`=� Book context saved for project ${projectId}`);
          }
        } catch (error) {
          console.error('Error saving book context:', error);
        }
      }
    }).catch((error) => {
      console.error(`Orchestration failed for project ${projectId}:`, error);
    });

    return NextResponse.json({
      success: true,
      message: 'Orchestration started',
      projectId,
      estimatedDuration: '10-15 minutes',
      progressEndpoint: `/api/orchestration/progress?projectId=${projectId}`
    });

  } catch (error) {
    console.error('Error starting orchestration:', error);
    return NextResponse.json(
      { error: 'Failed to start orchestration' },
      { status: 500 }
    );
  }
}