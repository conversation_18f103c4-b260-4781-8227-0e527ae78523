'use client'

import { usePathname } from 'next/navigation'
import { useMemo } from 'react'
import { BreadcrumbItem } from '@/components/ui/breadcrumb'

interface RouteConfig {
  [key: string]: {
    label: string
    icon?: React.ReactNode
  }
}

const routeConfig: RouteConfig = {
  dashboard: { label: 'Dashboard' },
  projects: { label: 'Projects' },
  new: { label: 'New' },
  templates: { label: 'Templates' },
  samples: { label: 'Samples' },
  profile: { label: 'Profile' },
  settings: { label: 'Settings' },
  write: { label: 'Write' },
  edit: { label: 'Edit' },
  chapters: { label: 'Chapters' },
  'story-bible': { label: 'Story Bible' },
  characters: { label: 'Characters' },
  timeline: { label: 'Timeline' },
  analytics: { label: 'Analytics' },
  'version-history': { label: 'Version History' },
  collaborators: { label: 'Collaborators' },
  export: { label: 'Export' },
}

export function useBreadcrumbs(customItems?: BreadcrumbItem[]): BreadcrumbItem[] {
  const pathname = usePathname()
  
  return useMemo(() => {
    if (customItems) return customItems
    
    const segments = pathname.split('/').filter(Boolean)
    const items: BreadcrumbItem[] = []
    
    segments.forEach((segment, index) => {
      // Skip UUID segments
      if (isUUID(segment)) {
        // For project IDs, we could fetch the project name
        // For now, we'll skip them
        return
      }
      
      const config = routeConfig[segment]
      const label = config?.label || formatSegment(segment)
      
      // Build the href for all but the last segment
      const href = index < segments.length - 1 
        ? `/${segments.slice(0, index + 1).join('/')}` 
        : undefined
      
      items.push({
        label,
        href,
        icon: config?.icon,
      })
    })
    
    return items
  }, [pathname, customItems])
}

// Helper function to check if a string is a UUID
function isUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(str)
}

// Helper function to format segment names
function formatSegment(segment: string): string {
  return segment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}