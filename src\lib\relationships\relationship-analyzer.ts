import { openai } from '../openai';

export interface Relationship {
  id: string;
  fromCharacter: string;
  toCharacter: string;
  type: RelationshipType;
  intensity: number; // 0-1 scale
  sentiment: 'positive' | 'negative' | 'neutral' | 'complex';
  status: 'developing' | 'established' | 'changing' | 'ended';
  firstMentioned: number; // chapter number
  lastUpdated: number; // chapter number
  evolution: RelationshipEvolution[];
  tags: string[];
  description: string;
  evidence: RelationshipEvidence[];
  mutual: boolean; // if both characters feel the same way
}

export interface RelationshipType {
  primary: 'romantic' | 'family' | 'friendship' | 'professional' | 'rivalry' | 'mentorship' | 'alliance' | 'conflict';
  secondary?: string; // more specific types like 'siblings', 'boss-employee', etc.
  subtype?: string; // very specific like 'unrequited love', 'bitter rivals', etc.
}

export interface RelationshipEvolution {
  chapter: number;
  event: string;
  previousState: string;
  newState: string;
  trigger: string;
  intensity: number;
  significance: 'minor' | 'major' | 'pivotal';
}

export interface RelationshipEvidence {
  chapter: number;
  quote: string;
  context: string;
  type: 'dialogue' | 'action' | 'thought' | 'description';
  confidence: number; // 0-1 how confident we are this shows the relationship
}

export interface RelationshipGraph {
  nodes: RelationshipNode[];
  edges: RelationshipEdge[];
  clusters: RelationshipCluster[];
  metrics: GraphMetrics;
}

export interface RelationshipNode {
  id: string;
  character: string;
  importance: number;
  centrality: number;
  connectionCount: number;
  clusterIds: string[];
  position?: { x: number; y: number };
}

export interface RelationshipEdge {
  id: string;
  source: string;
  target: string;
  relationship: Relationship;
  weight: number;
  bidirectional: boolean;
  tension: number; // for conflict visualization
}

export interface RelationshipCluster {
  id: string;
  name: string;
  members: string[];
  type: 'family' | 'faction' | 'team' | 'social_circle' | 'workplace' | 'other';
  strength: number; // how tightly connected the cluster is
  stability: number; // how stable the cluster is over time
}

export interface GraphMetrics {
  density: number; // how connected the graph is
  avgPathLength: number;
  clusteringCoefficient: number;
  mostCentral: string[];
  isolatedNodes: string[];
  strongestBonds: string[];
  volatileRelationships: string[];
}

export interface RelationshipAnalysis {
  totalRelationships: number;
  byType: Record<string, number>;
  byIntensity: Record<string, number>;
  evolutionTrends: { chapter: number; totalChanges: number }[];
  conflicts: string[];
  alliances: string[];
  romanticArcs: string[];
  keyMoments: { chapter: number; event: string; impact: string }[];
}

export class RelationshipAnalyzer {
  private relationships = new Map<string, Relationship>();
  private characters = new Set<string>();

  constructor(_projectId: string) {
  }

  async analyzeChapterRelationships(
    chapterContent: string, 
    chapterNumber: number,
    knownCharacters: string[]
  ): Promise<Relationship[]> {
    // Add known characters to our set
    knownCharacters.forEach(char => this.characters.add(char));

    // Extract relationships using AI
    const extractedRelationships = await this.extractRelationshipsFromText(
      chapterContent, 
      chapterNumber, 
      knownCharacters
    );

    // Process and update existing relationships
    const updatedRelationships: Relationship[] = [];
    
    for (const newRel of extractedRelationships) {
      const existingId = this.findExistingRelationship(newRel.fromCharacter!, newRel.toCharacter!);
      
      if (existingId) {
        const updated = await this.updateRelationship(existingId, newRel, chapterNumber);
        updatedRelationships.push(updated);
      } else {
        const created = await this.createRelationship(newRel, chapterNumber);
        updatedRelationships.push(created);
      }
    }

    return updatedRelationships;
  }

  async generateRelationshipGraph(chapterRange?: { start: number; end: number }): Promise<RelationshipGraph> {
    const allRelationships = Array.from(this.relationships.values());
    
    // Filter by chapter range if specified
    const filteredRelationships = chapterRange 
      ? allRelationships.filter(rel => 
          rel.lastUpdated >= chapterRange.start && rel.firstMentioned <= chapterRange.end
        )
      : allRelationships;

    // Build nodes
    const characterSet = new Set<string>();
    filteredRelationships.forEach(rel => {
      characterSet.add(rel.fromCharacter);
      characterSet.add(rel.toCharacter);
    });

    const nodes: RelationshipNode[] = Array.from(characterSet).map(character => ({
      id: character,
      character,
      importance: this.calculateCharacterImportance(character, filteredRelationships),
      centrality: this.calculateCentrality(character, filteredRelationships),
      connectionCount: this.getConnectionCount(character, filteredRelationships),
      clusterIds: []
    }));

    // Build edges
    const edges: RelationshipEdge[] = filteredRelationships.map(rel => ({
      id: rel.id,
      source: rel.fromCharacter,
      target: rel.toCharacter,
      relationship: rel,
      weight: rel.intensity,
      bidirectional: rel.mutual,
      tension: this.calculateTension(rel)
    }));

    // Detect clusters
    const clusters = await this.detectClusters(nodes, edges);
    
    // Update node cluster assignments
    nodes.forEach(node => {
      node.clusterIds = clusters
        .filter(cluster => cluster.members.includes(node.character))
        .map(cluster => cluster.id);
    });

    // Calculate metrics
    const metrics = this.calculateGraphMetrics(nodes, edges);

    return {
      nodes,
      edges,
      clusters,
      metrics
    };
  }

  private async extractRelationshipsFromText(
    text: string, 
    _chapterNumber: number, 
    knownCharacters: string[]
  ): Promise<Partial<Relationship>[]> {
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `Analyze this text for character relationships. For each relationship you identify, provide:
1. The characters involved
2. The type of relationship (romantic, family, friendship, professional, rivalry, etc.)
3. The intensity (0.0-1.0)
4. The sentiment (positive, negative, neutral, complex)
5. Evidence from the text
6. Whether it appears mutual

Focus on relationships that are:
- Explicitly mentioned or demonstrated
- Show interaction or connection
- Have emotional significance
- Advance character or plot development

Known characters: ${knownCharacters.join(', ')}

Return a JSON array of relationships.`
          },
          {
            role: 'user',
            content: text.substring(0, 4000) // Limit for token efficiency
          }
        ],
        temperature: 0.2
      });

      const content = response.choices[0]?.message?.content;
      if (!content) return [];

      const extracted = JSON.parse(content);
      return Array.isArray(extracted) ? extracted : [];
    } catch (error) {
      console.error('Error extracting relationships:', error);
      return [];
    }
  }

  private findExistingRelationship(charA: string, charB: string): string | null {
    for (const [id, rel] of this.relationships) {
      if ((rel.fromCharacter === charA && rel.toCharacter === charB) ||
          (rel.fromCharacter === charB && rel.toCharacter === charA)) {
        return id;
      }
    }
    return null;
  }

  private async updateRelationship(
    relationshipId: string, 
    newData: Partial<Relationship>, 
    chapterNumber: number
  ): Promise<Relationship> {
    const existing = this.relationships.get(relationshipId)!;
    
    // Track evolution if significant change
    const hasSignificantChange = 
      Math.abs((newData.intensity || existing.intensity) - existing.intensity) > 0.2 ||
      newData.sentiment !== existing.sentiment ||
      newData.status !== existing.status;

    if (hasSignificantChange) {
      const evolution: RelationshipEvolution = {
        chapter: chapterNumber,
        event: `Relationship development in Chapter ${chapterNumber}`,
        previousState: `${existing.sentiment} ${existing.type.primary} (${existing.intensity})`,
        newState: `${newData.sentiment || existing.sentiment} ${newData.type?.primary || existing.type.primary} (${newData.intensity || existing.intensity})`,
        trigger: 'Chapter content',
        intensity: newData.intensity || existing.intensity,
        significance: Math.abs((newData.intensity || existing.intensity) - existing.intensity) > 0.4 ? 'pivotal' : 'major'
      };

      existing.evolution.push(evolution);
    }

    // Update fields
    const updated: Relationship = {
      ...existing,
      ...newData,
      lastUpdated: chapterNumber,
      evidence: [
        ...existing.evidence,
        ...(newData.evidence || [])
      ]
    };

    this.relationships.set(relationshipId, updated);
    return updated;
  }

  private async createRelationship(
    relationshipData: Partial<Relationship>, 
    chapterNumber: number
  ): Promise<Relationship> {
    const id = `rel_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    const relationship: Relationship = {
      id,
      fromCharacter: relationshipData.fromCharacter || '',
      toCharacter: relationshipData.toCharacter || '',
      type: relationshipData.type || { primary: 'friendship' },
      intensity: relationshipData.intensity || 0.5,
      sentiment: relationshipData.sentiment || 'neutral',
      status: relationshipData.status || 'developing',
      firstMentioned: chapterNumber,
      lastUpdated: chapterNumber,
      evolution: [],
      tags: relationshipData.tags || [],
      description: relationshipData.description || '',
      evidence: relationshipData.evidence || [],
      mutual: relationshipData.mutual || false
    };

    this.relationships.set(id, relationship);
    return relationship;
  }

  private calculateCharacterImportance(character: string, relationships: Relationship[]): number {
    const charRelationships = relationships.filter(rel => 
      rel.fromCharacter === character || rel.toCharacter === character
    );

    const totalIntensity = charRelationships.reduce((sum, rel) => sum + rel.intensity, 0);
    const avgIntensity = charRelationships.length > 0 ? totalIntensity / charRelationships.length : 0;
    
    // Weight by number of relationships and average intensity
    return Math.min((charRelationships.length * 0.1) + (avgIntensity * 0.7), 1.0);
  }

  private calculateCentrality(character: string, relationships: Relationship[]): number {
    const connections = relationships.filter(rel => 
      rel.fromCharacter === character || rel.toCharacter === character
    );

    const totalCharacters = this.characters.size;
    return totalCharacters > 1 ? connections.length / (totalCharacters - 1) : 0;
  }

  private getConnectionCount(character: string, relationships: Relationship[]): number {
    return relationships.filter(rel => 
      rel.fromCharacter === character || rel.toCharacter === character
    ).length;
  }

  private calculateTension(relationship: Relationship): number {
    let tension = 0;

    // Base tension on relationship type and sentiment
    if (relationship.type.primary === 'rivalry' || relationship.type.primary === 'conflict') {
      tension += 0.7;
    }
    
    if (relationship.sentiment === 'negative') {
      tension += 0.5;
    } else if (relationship.sentiment === 'complex') {
      tension += 0.3;
    }

    // Add volatility based on evolution
    const recentChanges = relationship.evolution.filter(ev => ev.significance !== 'minor').length;
    tension += Math.min(recentChanges * 0.1, 0.3);

    return Math.min(tension, 1.0);
  }

  private async detectClusters(nodes: RelationshipNode[], edges: RelationshipEdge[]): Promise<RelationshipCluster[]> {
    // Simple clustering algorithm based on connection strength
    const clusters: RelationshipCluster[] = [];
    const visited = new Set<string>();

    for (const node of nodes) {
      if (visited.has(node.character)) continue;

      const cluster = this.expandCluster(node.character, nodes, edges, visited);
      if (cluster.length > 1) {
        clusters.push({
          id: `cluster_${clusters.length}`,
          name: `Group ${clusters.length + 1}`,
          members: cluster,
          type: 'social_circle',
          strength: this.calculateClusterStrength(cluster, edges),
          stability: 0.7 // Would calculate based on evolution
        });
      }
    }

    return clusters;
  }

  private expandCluster(
    startCharacter: string, 
    nodes: RelationshipNode[], 
    edges: RelationshipEdge[], 
    visited: Set<string>
  ): string[] {
    const cluster = [startCharacter];
    visited.add(startCharacter);

    const connectedEdges = edges.filter(edge => 
      (edge.source === startCharacter || edge.target === startCharacter) &&
      edge.weight > 0.5 // Strong connections only
    );

    for (const edge of connectedEdges) {
      const connected = edge.source === startCharacter ? edge.target : edge.source;
      if (!visited.has(connected)) {
        cluster.push(...this.expandCluster(connected, nodes, edges, visited));
      }
    }

    return cluster;
  }

  private calculateClusterStrength(members: string[], edges: RelationshipEdge[]): number {
    const clusterEdges = edges.filter(edge => 
      members.includes(edge.source) && members.includes(edge.target)
    );
    
    const totalPossibleEdges = (members.length * (members.length - 1)) / 2;
    return totalPossibleEdges > 0 ? clusterEdges.length / totalPossibleEdges : 0;
  }

  private calculateGraphMetrics(nodes: RelationshipNode[], edges: RelationshipEdge[]): GraphMetrics {
    const totalPossibleEdges = (nodes.length * (nodes.length - 1)) / 2;
    const density = totalPossibleEdges > 0 ? edges.length / totalPossibleEdges : 0;

    const mostCentral = nodes
      .sort((a, b) => b.centrality - a.centrality)
      .slice(0, 3)
      .map(n => n.character);

    const isolatedNodes = nodes
      .filter(n => n.connectionCount === 0)
      .map(n => n.character);

    const strongestBonds = edges
      .sort((a, b) => b.weight - a.weight)
      .slice(0, 3)
      .map(e => `${e.source} - ${e.target}`);

    const volatileRelationships = edges
      .filter(e => e.tension > 0.6)
      .map(e => `${e.source} - ${e.target}`);

    return {
      density,
      avgPathLength: 0, // Would implement graph traversal algorithm
      clusteringCoefficient: 0, // Would calculate actual clustering coefficient
      mostCentral,
      isolatedNodes,
      strongestBonds,
      volatileRelationships
    };
  }

  // Missing methods for API compatibility
  async getRelationshipAnalysis(_projectId: string): Promise<{
    relationships: Relationship[];
    graph: RelationshipGraph;
    insights: string[];
  }> {
    const relationships = Array.from(this.relationships.values());
    const graph = await this.generateRelationshipGraph();
    
    const insights = [
      `Found ${relationships.length} relationships between ${this.characters.size} characters`,
      `Average relationship intensity: ${this.calculateAverageIntensity(relationships).toFixed(2)}`,
      `Most connected character: ${graph.metrics.mostCentral[0] || 'None'}`,
      `${graph.metrics.volatileRelationships.length} volatile relationships detected`
    ];

    return { relationships, graph, insights };
  }

  async detectRelationshipConflicts(_projectId: string): Promise<{
    conflicts: Array<{
      type: string;
      description: string;
      involvedCharacters: string[];
      severity: 'low' | 'medium' | 'high';
      suggestions: string[];
    }>;
  }> {
    const relationships = Array.from(this.relationships.values());
    const conflicts = [];

    // Detect love triangles
    for (const char of this.characters) {
      const romanticRels = relationships.filter(rel => 
        (rel.fromCharacter === char || rel.toCharacter === char) &&
        rel.type.primary === 'romantic'
      );

      if (romanticRels.length >= 2) {
        conflicts.push({
          type: 'love_triangle',
          description: `${char} has multiple romantic relationships`,
          involvedCharacters: [char, ...romanticRels.map(r => 
            r.fromCharacter === char ? r.toCharacter : r.fromCharacter
          )],
          severity: 'medium' as const,
          suggestions: [
            'Consider clarifying relationship timeline',
            'Add conflict resolution scenes',
            'Develop character motivations for choices'
          ]
        });
      }
    }

    // Detect contradictory relationships
    const contradictions = this.findContradictoryRelationships(relationships);
    conflicts.push(...contradictions);

    return { conflicts };
  }

  private calculateAverageIntensity(relationships: Relationship[]): number {
    if (relationships.length === 0) return 0;
    const total = relationships.reduce((sum, rel) => sum + rel.intensity, 0);
    return total / relationships.length;
  }

  private findContradictoryRelationships(relationships: Relationship[]) {
    const conflicts: Array<{
      type: string;
      description: string;
      involvedCharacters: string[];
      severity: 'low' | 'medium' | 'high';
      suggestions: string[];
    }> = [];
    
    // Check for conflicting relationship types between same characters
    const characterPairs = new Map<string, Relationship[]>();
    
    relationships.forEach(rel => {
      const key = [rel.fromCharacter, rel.toCharacter].sort().join('-');
      if (!characterPairs.has(key)) {
        characterPairs.set(key, []);
      }
      characterPairs.get(key)!.push(rel);
    });

    characterPairs.forEach((rels, pair) => {
      if (rels.length > 1) {
        const types = rels.map(r => r.type.primary);
        const uniqueTypes = new Set(types);
        
        if (uniqueTypes.size > 1) {
          const [char1, char2] = pair.split('-');
          conflicts.push({
            type: 'contradictory_relationship',
            description: `${char1} and ${char2} have conflicting relationship types`,
            involvedCharacters: [char1 || '', char2 || ''],
            severity: 'high' as const,
            suggestions: [
              'Clarify the primary relationship type',
              'Show relationship evolution over time',
              'Remove contradictory elements'
            ]
          });
        }
      }
    });

    return conflicts;
  }
}