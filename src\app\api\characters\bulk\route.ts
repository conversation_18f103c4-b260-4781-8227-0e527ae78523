import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { bulkCharacterOperationSchema, createCharacterSchema, updateCharacterSchema } from '@/lib/validation/schemas'
import { z } from 'zod'

type SupabaseClient = Awaited<ReturnType<typeof createClient>>

interface BulkCharacterResult {
  index: number;
  character?: unknown;
  operation: 'create' | 'update' | 'delete';
  message?: string;
}

interface BulkCharacterError {
  index: number;
  character: unknown;
  error: string;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate input
    const validatedData = bulkCharacterOperationSchema.parse(body)
    const { operation, characters } = validatedData

    const results = {
      successful: [] as BulkCharacterResult[],
      failed: [] as BulkCharacterError[],
      summary: {
        total: characters.length,
        successful: 0,
        failed: 0
      }
    }

    // Pre-validate all project IDs and character data to avoid N+1 queries
    const projectIds = [...new Set(characters.map(c => 'project_id' in c ? c.project_id : null).filter(Boolean) as string[])]
    
    // Batch validate project ownership
    const { data: validProjects } = await supabase
      .from('projects')
      .select('id')
      .in('id', projectIds)
      .eq('user_id', user.id)
    
    const validProjectIds = new Set(validProjects?.map(p => p.id) || [])
    
    // Batch check existing characters for creates/updates
    const existingCharactersByProject: Map<string, Set<string>> = new Map()
    const ownedCharacters: Map<string, { project_id: string, character_id: string }> = new Map()
    
    if (operation === 'create' || operation === 'update') {
      const { data: existingChars } = await supabase
        .from('characters')
        .select('project_id, id, character_id')
        .in('project_id', projectIds)
      
      existingChars?.forEach(char => {
        if (!existingCharactersByProject.has(char.project_id)) {
          existingCharactersByProject.set(char.project_id, new Set())
        }
        existingCharactersByProject.get(char.project_id)!.add(char.character_id)
        ownedCharacters.set(char.id, { project_id: char.project_id, character_id: char.character_id })
      })
    }
    
    // For updates/deletes, batch check character ownership
    if (operation === 'update' || operation === 'delete') {
      const characterIdsToCheck = characters
        .map(c => ('id' in c ? c.id : null))
        .filter((id): id is string => Boolean(id))
      
      if (characterIdsToCheck.length > 0) {
        const { data: ownedChars } = await supabase
          .from('characters')
          .select(`
            id,
            project_id,
            character_id,
            projects!inner (
              user_id
            )
          `)
          .in('id', characterIdsToCheck)
          .eq('projects.user_id', user.id)
        
        ownedChars?.forEach(char => {
          ownedCharacters.set(char.id, { 
            project_id: char.project_id, 
            character_id: char.character_id 
          })
        })
      }
    }

    // Process each character based on operation type
    for (const [index, characterData] of characters.entries()) {
      try {
        let result
        
        switch (operation) {
          case 'create':
            result = await handleCreateCharacter(
              supabase, 
              user.id, 
              characterData, 
              index, 
              validProjectIds, 
              existingCharactersByProject
            )
            break
          case 'update':
            result = await handleUpdateCharacter(
              supabase, 
              user.id, 
              characterData, 
              index, 
              validProjectIds, 
              existingCharactersByProject,
              ownedCharacters
            )
            break
          case 'delete':
            result = await handleDeleteCharacter(
              supabase, 
              user.id, 
              characterData, 
              index, 
              validProjectIds,
              ownedCharacters
            )
            break
          default:
            throw new Error('Invalid operation')
        }
        
        results.successful.push(result)
        results.summary.successful++
        
      } catch (error) {
        console.error(`Error in bulk ${operation} for character ${index}:`, error)
        results.failed.push({
          index,
          character: characterData,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        results.summary.failed++
      }
    }

    const statusCode = results.summary.failed === 0 ? 200 : 207 // Multi-status if some failed

    return NextResponse.json({
      operation,
      results,
      message: `Bulk ${operation} completed: ${results.summary.successful} successful, ${results.summary.failed} failed`
    }, { status: statusCode })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid bulk operation data',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error in characters bulk operation:', error)
    return NextResponse.json(
      { error: 'Failed to execute bulk operation' },
      { status: 500 }
    )
  }
}

async function handleCreateCharacter(
  supabase: SupabaseClient, 
  _userId: string, 
  characterData: unknown, 
  index: number,
  validProjectIds: Set<string>,
  existingCharactersByProject: Map<string, Set<string>>
): Promise<BulkCharacterResult> {
  // Validate character data
  const validatedCharacter = createCharacterSchema.parse(characterData)

  // Verify project ownership using pre-validated data
  if (!validProjectIds.has(validatedCharacter.project_id)) {
    throw new Error(`Project not found for character at index ${index}`)
  }

  // Check if character_id already exists in this project using pre-fetched data
  const existingCharsInProject = existingCharactersByProject.get(validatedCharacter.project_id)
  if (existingCharsInProject?.has(validatedCharacter.character_id)) {
    throw new Error(`Character with ID "${validatedCharacter.character_id}" already exists in project`)
  }

  // Create character
  const { data: character, error } = await supabase
    .from('characters')
    .insert({
      ...validatedCharacter,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) {
    throw new Error(error.message)
  }

  return { index, character, operation: 'create' }
}

async function handleUpdateCharacter(
  supabase: SupabaseClient, 
  _userId: string, 
  characterData: unknown, 
  index: number,
  _validProjectIds: Set<string>,
  existingCharactersByProject: Map<string, Set<string>>,
  ownedCharacters: Map<string, { project_id: string, character_id: string }>
): Promise<BulkCharacterResult> {
  // Type guard to ensure we have an object with an id property
  if (typeof characterData !== 'object' || characterData === null || !('id' in characterData)) {
    throw new Error(`Character ID required for update at index ${index}`)
  }

  const data = characterData as { id: string; [key: string]: unknown }
  const { id, ...updateData } = data
  const validatedUpdate = updateCharacterSchema.parse(updateData)

  // Verify ownership using pre-validated data
  const characterInfo = ownedCharacters.get(id)
  if (!characterInfo) {
    throw new Error(`Character not found or access denied for ID ${id}`)
  }

  // If character_id is being updated, check for conflicts
  if (validatedUpdate.character_id && validatedUpdate.character_id !== characterInfo.character_id) {
    const existingCharsInProject = existingCharactersByProject.get(characterInfo.project_id)
    if (existingCharsInProject?.has(validatedUpdate.character_id)) {
      throw new Error(`Character with ID "${validatedUpdate.character_id}" already exists in project`)
    }
  }

  // Update character
  const { data: character, error } = await supabase
    .from('characters')
    .update({
      ...validatedUpdate,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    throw new Error(error.message)
  }

  return { index, character, operation: 'update' }
}

async function handleDeleteCharacter(
  supabase: SupabaseClient, 
  _userId: string, 
  characterData: unknown, 
  index: number,
  _validProjectIds: Set<string>,
  ownedCharacters: Map<string, { project_id: string, character_id: string }>
): Promise<BulkCharacterResult> {
  // Type guard to ensure we have an object with an id property
  if (typeof characterData !== 'object' || characterData === null || !('id' in characterData)) {
    throw new Error(`Character ID required for delete at index ${index}`)
  }

  const data = characterData as { id: string }
  const { id } = data

  // Verify ownership using pre-validated data
  const characterInfo = ownedCharacters.get(id)
  if (!characterInfo) {
    throw new Error(`Character not found or access denied for ID ${id}`)
  }

  // Delete character
  const { error } = await supabase
    .from('characters')
    .delete()
    .eq('id', id)

  if (error) {
    throw new Error(error.message)
  }

  return { 
    index, 
    character: { id, character_id: characterInfo.character_id }, 
    operation: 'delete',
    message: `Character "${characterInfo.character_id}" deleted successfully`
  }
}