import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { getRelationshipAnalyzer } from '@/lib/relationships/relationship-instances';

export async function POST(request: NextRequest) {
  try {
    const { projectId, chapterContent, chapterNumber, knownCharacters } = await request.json();
    
    if (!projectId || !chapterContent || !chapterNumber) {
      return NextResponse.json(
        { error: 'Project ID, chapter content, and chapter number are required' },
        { status: 400 }
      );
    }

    const analyzer = getRelationshipAnalyzer(projectId);

    // Analyze relationships in the chapter
    const relationships = await analyzer.analyzeChapterRelationships(
      chapterContent,
      chapterNumber,
      knownCharacters || []
    );

    // Get overall analysis
    const analysis = await analyzer.getRelationshipAnalysis(projectId);

    // Check for conflicts
    const conflicts = await analyzer.detectRelationshipConflicts(projectId);
    
    return NextResponse.json({
      success: true,
      relationships,
      analysis,
      conflicts
    });

  } catch (error) {
    console.error('Error analyzing relationships:', error);
    return NextResponse.json(
      { error: 'Failed to analyze relationships' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    const analyzer = getRelationshipAnalyzer(projectId);

    const analysis = await analyzer.getRelationshipAnalysis(projectId);
    const conflicts = await analyzer.detectRelationshipConflicts(projectId);
    
    return NextResponse.json({
      success: true,
      analysis,
      conflicts
    });

  } catch (error) {
    console.error('Error getting relationship analysis:', error);
    return NextResponse.json(
      { error: 'Failed to get relationship analysis' },
      { status: 500 }
    );
  }
}