import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';

interface ProjectAnalytics {
  id: string;
  user_id: string;
  project_id: string;
  event_type: 'project_completed' | 'project_abandoned';
  selection_data?: {
    genre?: string;
    structureType?: string;
    pacingPreference?: string;
    narrativeVoice?: string;
    toneOptions?: string[];
    [key: string]: unknown;
  };
  projects?: {
    id: string;
    name: string;
    genre?: string;
    status: string;
    word_count?: number;
    target_word_count?: number;
    created_at: string;
    completed_at?: string;
  };
}

interface PatternData {
  completed: number;
  abandoned: number;
  successRate?: number;
  total?: number;
}

interface SuccessPatterns {
  genreSuccess: Record<string, PatternData>;
  structureSuccess: Record<string, PatternData>;
  paceSuccess: Record<string, PatternData>;
  targetWordCountSuccess: Record<string, PatternData>;
  povSuccess: Record<string, PatternData>;
  toneSuccess: Record<string, PatternData>;
}

interface ProfileStats {
  profile: {
    name: string;
    description?: string;
    category?: string;
    usage_count?: number;
  };
  completed: number;
  abandoned: number;
  total?: number;
  successRate?: number;
}


export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || 'month'; // week, month, quarter, year
    const genre = searchParams.get('genre');

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeframe) {
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(endDate.getMonth() - 1);
    }

    // Get completed projects with their selection data
    let completedProjectsQuery = supabase
      .from('selection_analytics')
      .select(`
        *,
        projects!inner(
          id,
          name,
          genre,
          status,
          word_count,
          target_word_count,
          created_at,
          completed_at
        )
      `)
      .eq('event_type', 'project_completed')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (genre) {
      completedProjectsQuery = completedProjectsQuery.eq('projects.genre', genre);
    }

    const { data: completedProjects, error: completedError } = await completedProjectsQuery;

    if (completedError) throw completedError;

    // Get abandoned projects for comparison
    let abandonedProjectsQuery = supabase
      .from('selection_analytics')
      .select(`
        *,
        projects!inner(
          id,
          name,
          genre,
          status,
          word_count,
          target_word_count,
          created_at
        )
      `)
      .eq('event_type', 'project_abandoned')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (genre) {
      abandonedProjectsQuery = abandonedProjectsQuery.eq('projects.genre', genre);
    }

    const { data: abandonedProjects, error: abandonedError } = await abandonedProjectsQuery;

    if (abandonedError) throw abandonedError;

    // Calculate success patterns
    const patterns = calculateSuccessPatterns(completedProjects || [], abandonedProjects || []);

    // Get top performing selection profiles
    const topProfiles = await getTopPerformingProfiles(timeframe, genre);

    // Calculate metrics
    const totalCompleted = completedProjects?.length || 0;
    const totalAbandoned = abandonedProjects?.length || 0;
    const successRate = totalCompleted + totalAbandoned > 0 
      ? (totalCompleted / (totalCompleted + totalAbandoned)) * 100 
      : 0;

    return NextResponse.json({
      successRate: Math.round(successRate * 100) / 100,
      totalProjects: totalCompleted + totalAbandoned,
      completedProjects: totalCompleted,
      abandonedProjects: totalAbandoned,
      timeframe,
      patterns,
      topProfiles,
      recommendations: generateRecommendations(patterns)
    });
  } catch (error) {
    console.error('Success patterns API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function calculateSuccessPatterns(completed: ProjectAnalytics[], abandoned: ProjectAnalytics[]): SuccessPatterns {
  const patterns = {
    genreSuccess: {},
    structureSuccess: {},
    paceSuccess: {},
    targetWordCountSuccess: {},
    povSuccess: {},
    toneSuccess: {}
  };

  // Process completed projects
  completed.forEach(project => {
    const selections = project.selection_data;
    if (!selections) return;

    incrementPattern(patterns.genreSuccess, selections.genre || 'unknown', 'completed');
    incrementPattern(patterns.structureSuccess, selections.structureType || 'unknown', 'completed');
    incrementPattern(patterns.paceSuccess, selections.pacingPreference || 'unknown', 'completed');
    incrementPattern(patterns.povSuccess, selections.narrativeVoice || 'unknown', 'completed');
    
    if (selections.toneOptions && Array.isArray(selections.toneOptions)) {
      selections.toneOptions.forEach((tone: string) => {
        incrementPattern(patterns.toneSuccess, tone, 'completed');
      });
    }

    // Word count ranges
    const wordCountRange = getWordCountRange(project.projects?.target_word_count);
    incrementPattern(patterns.targetWordCountSuccess, wordCountRange, 'completed');
  });

  // Process abandoned projects
  abandoned.forEach(project => {
    const selections = project.selection_data;
    if (!selections) return;

    incrementPattern(patterns.genreSuccess, selections.genre || 'unknown', 'abandoned');
    incrementPattern(patterns.structureSuccess, selections.structureType || 'unknown', 'abandoned');
    incrementPattern(patterns.paceSuccess, selections.pacingPreference || 'unknown', 'abandoned');
    incrementPattern(patterns.povSuccess, selections.narrativeVoice || 'unknown', 'abandoned');
    
    if (selections.toneOptions && Array.isArray(selections.toneOptions)) {
      selections.toneOptions.forEach((tone: string) => {
        incrementPattern(patterns.toneSuccess, tone, 'abandoned');
      });
    }

    const wordCountRange = getWordCountRange(project.projects?.target_word_count);
    incrementPattern(patterns.targetWordCountSuccess, wordCountRange, 'abandoned');
  });

  // Calculate success rates for each pattern
  Object.keys(patterns).forEach(patternType => {
    const pattern = patterns[patternType as keyof typeof patterns] as Record<string, PatternData>;
    Object.keys(pattern).forEach(key => {
      const data = pattern[key];
      if (data) {
        const total = data.completed + data.abandoned;
        data.successRate = total > 0 ? Math.round((data.completed / total) * 10000) / 100 : 0;
        data.total = total;
      }
    });
  });

  return patterns;
}

function incrementPattern(pattern: Record<string, PatternData>, key: string, type: 'completed' | 'abandoned') {
  if (!key) return;
  
  if (!pattern[key]) {
    pattern[key] = { completed: 0, abandoned: 0 };
  }
  pattern[key][type]++;
}

function getWordCountRange(wordCount: number | null | undefined): string {
  if (!wordCount) return 'unknown';
  
  if (wordCount < 50000) return 'novella';
  if (wordCount < 80000) return 'short-novel';
  if (wordCount < 120000) return 'standard-novel';
  if (wordCount < 200000) return 'long-novel';
  return 'epic-novel';
}

async function getTopPerformingProfiles(_timeframe: string, genre?: string | null) {
  try {
    let query = supabase
      .from('selection_analytics')
      .select(`
        selection_profile_id,
        selection_profiles!inner(name, description, category, usage_count),
        event_type
      `)
      .in('event_type', ['project_completed', 'project_abandoned'])
      .not('selection_profile_id', 'is', null);

    if (genre) {
      query = query.eq('selection_profiles.category', genre);
    }

    const { data, error } = await query;

    if (error) throw error;

    // Group by profile and calculate success rates
    const profileStats: Record<string, ProfileStats> = {};
    
    data?.forEach(entry => {
      const profileId = entry.selection_profile_id;
      if (!profileStats[profileId]) {
        const profileData = Array.isArray(entry.selection_profiles) && entry.selection_profiles.length > 0
          ? entry.selection_profiles[0] 
          : Array.isArray(entry.selection_profiles) 
            ? null
            : entry.selection_profiles;
        
        profileStats[profileId] = {
          profile: {
            name: profileData?.name || 'Unknown Profile',
            description: profileData?.description || '',
            category: profileData?.category || '',
            usage_count: profileData?.usage_count || 0
          },
          completed: 0,
          abandoned: 0
        };
      }
      profileStats[profileId][entry.event_type === 'project_completed' ? 'completed' : 'abandoned']++;
    });

    // Calculate success rates and sort
    const profiles = Object.values(profileStats)
      .map((stats: ProfileStats) => ({
        profile: stats.profile,
        completed: stats.completed,
        abandoned: stats.abandoned,
        total: stats.completed + stats.abandoned,
        successRate: stats.completed + stats.abandoned > 0 
          ? Math.round((stats.completed / (stats.completed + stats.abandoned)) * 10000) / 100 
          : 0
      }))
      .filter(p => p.total >= 3) // Minimum projects for statistical relevance
      .sort((a, b) => b.successRate - a.successRate)
      .slice(0, 10);

    return profiles;
  } catch (error) {
    console.error('Error getting top profiles:', error);
    return [];
  }
}

interface Recommendation {
  type: string;
  recommendation: string;
  successRate: number;
  sampleSize?: number;
  message: string;
}

function generateRecommendations(patterns: SuccessPatterns) {
  const recommendations: Recommendation[] = [];

  // Find top performing patterns
  Object.keys(patterns).forEach(patternType => {
    const pattern = patterns[patternType as keyof SuccessPatterns] as Record<string, PatternData>;
    const sorted = Object.entries(pattern)
      .filter(([, data]: [string, PatternData]) => data.total && data.total >= 5)
      .sort(([, a]: [string, PatternData], [, b]: [string, PatternData]) => (b.successRate || 0) - (a.successRate || 0));

    if (sorted.length > 0) {
      const [bestKey, bestData] = sorted[0] as [string, PatternData];
      if (bestData.successRate && bestData.successRate > 70) {
        recommendations.push({
          type: patternType,
          recommendation: bestKey,
          successRate: bestData.successRate,
          sampleSize: bestData.total,
          message: `Projects with ${formatPatternType(patternType)} "${bestKey}" have a ${bestData.successRate}% completion rate`
        });
      }
    }
  });

  return recommendations.slice(0, 5); // Top 5 recommendations
}

function formatPatternType(type: string): string {
  const formatMap: { [key: string]: string } = {
    genreSuccess: 'genre',
    structureSuccess: 'story structure',
    paceSuccess: 'pacing preference',
    targetWordCountSuccess: 'target word count',
    povSuccess: 'point of view',
    toneSuccess: 'tone'
  };
  return formatMap[type] || type;
}