import { formatPrice } from '../stripe'

describe('Stripe utilities', () => {
  describe('formatPrice', () => {
    it('formats USD prices correctly', () => {
      expect(formatPrice(2500)).toBe('$25.00')
      expect(formatPrice(100)).toBe('$1.00')
      expect(formatPrice(50)).toBe('$0.50')
      expect(formatPrice(0)).toBe('$0.00')
    })

    it('formats different currencies correctly', () => {
      expect(formatPrice(2500, 'eur')).toBe('€25.00')
      expect(formatPrice(2500, 'gbp')).toBe('£25.00')
    })

    it('handles large amounts correctly', () => {
      expect(formatPrice(999999)).toBe('$9,999.99')
      expect(formatPrice(1000000)).toBe('$10,000.00')
    })

    it('handles fractional cents', () => {
      expect(formatPrice(1)).toBe('$0.01')
      expect(formatPrice(99)).toBe('$0.99')
    })
  })
})