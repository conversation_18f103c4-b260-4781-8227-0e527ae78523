import { TimelineValidator, TimelineEvent, ValidationR<PERSON>ult, TimelineConflict } from './timeline-validator';

// Optional imports for runtime dependencies
interface ContextManagerInterface {
  memoryManager: {
    addMemoryChunk: (chunk: MemoryChunk) => Promise<void>;
  };
}

interface MemoryChunk {
  type: string;
  content: string;
  importance: number;
  chapters: number[];
  keywords: string[];
  dependencies: unknown[];
}

interface BookContextInterface {
  storyStructure?: {
    conflicts?: Array<{ type: string; description: string }>;
    timeline?: Array<{ event: string; chapter?: number }>;
  };
  characters: Array<{ name: string; role: string }>;
  chapterOutlines: Array<{
    title: string;
    summary: string;
    chapterNumber?: number;
    scenes?: Array<{ characters: string[] }>;
  }>;
}

interface ChapterContentInterface {
  content: string;
  chapterNumber: number;
  characterVoices?: Record<string, unknown>;
}

// These are optional dependencies that may not be available in all contexts
// They should be injected if needed rather than imported directly

export interface TimelineIntegrationConfig {
  projectId: string;
  enableAutoValidation: boolean;
  enableAutoExtraction: boolean;
  validationThreshold: number; // 0-1, confidence threshold for warnings
  historyLength: number; // How many chapters to consider for context
}

export interface TimelineUpdate {
  newEvents: TimelineEvent[];
  conflicts: TimelineConflict[];
  suggestions: string[];
  confidence: number;
}

export class TimelineIntegration {
  private validator: TimelineValidator;
  private contextManager?: ContextManagerInterface;
  private config: TimelineIntegrationConfig;

  constructor(config: TimelineIntegrationConfig, contextManager?: ContextManagerInterface) {
    this.config = config;
    this.contextManager = contextManager;
    this.validator = new TimelineValidator(config.projectId);
  }

  async initializeFromBookContext(bookContext: BookContextInterface): Promise<void> {
    // Extract initial timeline events from book context
    await this.extractEventsFromStoryStructure(bookContext);
    await this.extractEventsFromCharacters(bookContext);
    await this.extractEventsFromChapterOutlines(bookContext);
  }

  async processNewChapter(chapterContent: ChapterContentInterface): Promise<TimelineUpdate> {
    const result: TimelineUpdate = {
      newEvents: [],
      conflicts: [],
      suggestions: [],
      confidence: 1.0
    };

    if (!this.config.enableAutoExtraction) {
      return result;
    }

    try {
      // Extract timeline events from the new chapter
      const extractedEvents = await this.validator.extractTimelineFromChapter(
        chapterContent.content,
        chapterContent.chapterNumber
      );

      // Add events to timeline
      for (const event of extractedEvents) {
        const eventId = await this.validator.addEvent(event);
        result.newEvents.push({ ...event, id: eventId });
      }

      // Validate timeline if auto-validation is enabled
      if (this.config.enableAutoValidation) {
        const validation = await this.validator.validateTimeline();
        result.conflicts = validation.conflicts;
        result.suggestions = validation.suggestions;
        result.confidence = validation.confidence;

        // Update memory system with timeline conflicts if available
        if (this.contextManager && validation.conflicts.length > 0) {
          await this.updateMemoryWithConflicts(validation, chapterContent.chapterNumber);
        }
      }

      // Provide writing suggestions based on timeline
      if (result.confidence < this.config.validationThreshold) {
        result.suggestions.push(...await this.generateWritingSuggestions(chapterContent));
      }

    } catch (error) {
      console.error('Error processing chapter for timeline:', error);
      result.suggestions.push('Unable to process timeline for this chapter. Manual review recommended.');
    }

    return result;
  }

  async validateChapterBeforeWriting(chapterNumber: number, chapterOutline: Record<string, unknown>): Promise<{
    warnings: string[];
    suggestions: string[];
    timelineContext: string;
  }> {
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Get timeline context for this chapter
    const timelineContext = await this.generateTimelineContext(chapterNumber);

    // Check for character conflicts
    const characterEvents = this.getRecentCharacterEvents(chapterNumber);
    const characters = (chapterOutline.characters as string[]) || [];
    for (const character of characters) {
      const recentEvents = characterEvents.get(character) || [];
      if (recentEvents.length > 0) {
        const lastEvent = recentEvents[recentEvents.length - 1];
        if (lastEvent.location && chapterOutline.location && 
            lastEvent.location !== chapterOutline.location) {
          warnings.push(`${character} was last seen in ${lastEvent.location}, now in ${chapterOutline.location}`);
          suggestions.push(`Consider adding transition for ${character} moving from ${lastEvent.location} to ${chapterOutline.location}`);
        }
      }
    }

    // Check for timeline gaps
    if (chapterNumber > 1) {
      const timeGap = this.calculateTimeGap(chapterNumber);
      if (timeGap && timeGap.amount > 30 && timeGap.unit === 'days') {
        warnings.push(`Large time gap detected (${timeGap.amount} ${timeGap.unit})`);
        suggestions.push('Consider explaining what happened during this time period');
      }
    }

    return {
      warnings,
      suggestions,
      timelineContext
    };
  }

  async getTimelineForRange(startChapter: number, endChapter: number): Promise<TimelineEvent[]> {
    const allEvents = Array.from(this.validator['events'].values());
    return allEvents.filter(event => 
      event.chapter >= startChapter && event.chapter <= endChapter
    ).sort((a, b) => a.chapter - b.chapter);
  }

  async getCharacterTimeline(characterName: string): Promise<TimelineEvent[]> {
    return this.validator.getEventsByCharacter(characterName);
  }

  async checkConsistency(newContent: string, chapterNumber: number): Promise<string[]> {
    const inconsistencies: string[] = [];

    // Extract timeline information from new content
    const newEvents = await this.validator.extractTimelineFromChapter(newContent, chapterNumber);

    // Check each new event against existing timeline
    for (const newEvent of newEvents) {
      // Temporarily add event to check for conflicts
      const tempId = await this.validator.addEvent(newEvent);
      const conflicts = await this.validator.validateEvent(tempId);
      
      for (const conflict of conflicts) {
        inconsistencies.push(`Timeline conflict: ${conflict.description}`);
      }

      // Remove temporary event
      this.validator['events'].delete(tempId);
    }

    return inconsistencies;
  }

  getTimelineStats(): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    conflictCount: number;
    confidenceScore: number;
    timeSpanDays: number;
  } {
    const analysis = this.validator.getTimelineAnalysis();
    const validation = this.validator['validateTimeline'] ? 
      this.validator['validateTimeline']() : 
      { confidence: 1, conflicts: [] };

    return {
      totalEvents: analysis.totalEvents,
      eventsByType: Object.keys(analysis.characterAges).reduce((acc, type) => {
        acc[type] = 1; // Simplified
        return acc;
      }, {} as Record<string, number>),
      conflictCount: analysis.conflicts.length,
      confidenceScore: validation instanceof Promise ? 1 : validation.confidence || 1,
      timeSpanDays: analysis.timeSpan.unit === 'days' ? analysis.timeSpan.amount : 
                   analysis.timeSpan.unit === 'weeks' ? analysis.timeSpan.amount * 7 :
                   analysis.timeSpan.unit === 'months' ? analysis.timeSpan.amount * 30 :
                   analysis.timeSpan.amount * 365
    };
  }

  private async extractEventsFromStoryStructure(bookContext: BookContextInterface): Promise<void> {
    if (!bookContext.storyStructure) return;

    // Extract major plot points from story structure
    for (const conflict of bookContext.storyStructure.conflicts || []) {
      await this.validator.addEvent({
        type: 'plot',
        title: `Major Conflict: ${conflict.type}`,
        description: conflict.description,
        timestamp: {
          type: 'vague',
          value: 'Throughout story',
          uncertainty: 0.8
        },
        chapter: 1,
        characters: [],
        importance: 0.9,
        dependencies: [],
        consequences: []
      });
    }

    // Extract timeline events
    for (const timelineEvent of bookContext.storyStructure.timeline || []) {
      await this.validator.addEvent({
        type: 'reference',
        title: timelineEvent.event,
        description: timelineEvent.event,
        timestamp: {
          type: 'vague',
          value: `Chapter ${timelineEvent.chapter || 1}`,
          uncertainty: 0.6
        },
        chapter: timelineEvent.chapter || 1,
        characters: [],
        importance: 0.7,
        dependencies: [],
        consequences: []
      });
    }
  }

  private async extractEventsFromCharacters(bookContext: BookContextInterface): Promise<void> {
    for (const character of bookContext.characters) {
      // Add character introduction event
      await this.validator.addEvent({
        type: 'character',
        title: `${character.name} Introduction`,
        description: `Character ${character.name} (${character.role}) enters the story`,
        timestamp: {
          type: 'vague',
          value: 'Early in story',
          uncertainty: 0.7
        },
        chapter: 1,
        characters: [character.name],
        importance: character.role === 'protagonist' ? 0.9 : 0.6,
        dependencies: [],
        consequences: []
      });
    }
  }

  private async extractEventsFromChapterOutlines(bookContext: BookContextInterface): Promise<void> {
    for (const outline of bookContext.chapterOutlines) {
      await this.validator.addEvent({
        type: 'plot',
        title: outline.title,
        description: outline.summary,
        timestamp: {
          type: 'relative',
          value: `Chapter ${(outline as any).chapterNumber || 1}`,
          uncertainty: 0.3
        },
        chapter: outline.chapterNumber || 1,
        characters: (outline.scenes?.flatMap(scene => scene.characters)) || [],
        importance: 0.8,
        dependencies: [],
        consequences: []
      });
    }
  }

  private async updateMemoryWithConflicts(validation: ValidationResult, chapterNumber: number): Promise<void> {
    if (!this.contextManager) return;

    for (const conflict of validation.conflicts) {
      await this.contextManager.memoryManager.addMemoryChunk({
        type: 'event',
        content: `Timeline Conflict (Chapter ${chapterNumber}): ${conflict.description}`,
        importance: conflict.severity === 'critical' ? 0.9 : 
                   conflict.severity === 'high' ? 0.7 : 0.5,
        chapters: [chapterNumber],
        keywords: ['timeline', 'conflict', conflict.type],
        dependencies: []
      });
    }
  }

  private async generateWritingSuggestions(chapterContent: ChapterContentInterface): Promise<string[]> {
    const suggestions: string[] = [];
    
    // Get recent timeline events for context
    const recentEvents = await this.getTimelineForRange(
      Math.max(1, chapterContent.chapterNumber - this.config.historyLength),
      chapterContent.chapterNumber - 1
    );

    // Suggest timeline anchors
    if (recentEvents.length === 0) {
      suggestions.push('Consider adding specific time references to establish chronology');
    }

    // Check for missing character locations
    const characterLocations = new Map<string, string>();
    recentEvents.forEach(event => {
      if (event.location) {
        event.characters.forEach(char => characterLocations.set(char, event.location!));
      }
    });

    if (chapterContent.characterVoices) {
      Object.keys(chapterContent.characterVoices).forEach(char => {
        if (!characterLocations.has(char)) {
          suggestions.push(`Consider establishing ${char}'s current location`);
        }
      });
    }

    return suggestions;
  }

  private async generateTimelineContext(chapterNumber: number): Promise<string> {
    const recentEvents = await this.getTimelineForRange(
      Math.max(1, chapterNumber - 3),
      chapterNumber - 1
    );

    if (recentEvents.length === 0) {
      return 'No recent timeline events to consider.';
    }

    return `Recent Timeline Context:
${recentEvents.map(event => 
  `- Chapter ${event.chapter}: ${event.title} (${event.timestamp.value})`
).join('\n')}`;
  }

  private getRecentCharacterEvents(chapterNumber: number): Map<string, TimelineEvent[]> {
    const characterEvents = new Map<string, TimelineEvent[]>();
    const allEvents = Array.from(this.validator['events'].values());
    
    const recentEvents = allEvents.filter(event => 
      event.chapter < chapterNumber && event.chapter >= chapterNumber - 3
    );

    recentEvents.forEach(event => {
      event.characters.forEach(character => {
        if (!characterEvents.has(character)) {
          characterEvents.set(character, []);
        }
        const events = characterEvents.get(character);
        if (events) {
          events.push(event);
        }
      });
    });

    return characterEvents;
  }

  private calculateTimeGap(chapterNumber: number): { amount: number; unit: string } | null {
    const allEvents = Array.from(this.validator['events'].values());
    const currentEvents = allEvents.filter(e => e.chapter === chapterNumber);
    const previousEvents = allEvents.filter(e => e.chapter === chapterNumber - 1);

    if (currentEvents.length === 0 || previousEvents.length === 0) return null;

    const currentEvent = currentEvents.find(e => e.timestamp.parsedDate);
    const previousEvent = previousEvents.find(e => e.timestamp.parsedDate);

    if (!currentEvent?.timestamp.parsedDate || !previousEvent?.timestamp.parsedDate) {
      return null;
    }

    const diffMs = currentEvent.timestamp.parsedDate.getTime() - previousEvent.timestamp.parsedDate.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays < 1) return { amount: Math.floor(diffMs / (1000 * 60 * 60)), unit: 'hours' };
    if (diffDays < 30) return { amount: diffDays, unit: 'days' };
    if (diffDays < 365) return { amount: Math.floor(diffDays / 30), unit: 'months' };
    return { amount: Math.floor(diffDays / 365), unit: 'years' };
  }
}