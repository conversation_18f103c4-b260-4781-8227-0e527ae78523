import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { db } from '@/lib/db/client';
import { authenticateUser, handleRouteError } from '@/lib/auth';
import { z } from 'zod';
import { ErrorResponses, handleApiError } from '@/lib/api/error-response';

export async function GET() {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const user = authResult.user;
    const userId = user.id;

    const profiles = await db.selectionProfiles.getUserProfiles(userId);
    return NextResponse.json({ profiles });
  } catch (error) {
    return handleRouteError(error, 'Profiles GET');
  }
}

// Validation schema for profile creation
const createProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be 100 characters or less'),
  description: z.string().max(500, 'Description must be 500 characters or less').optional(),
  category: z.enum(['custom', 'template', 'shared']).default('custom'),
  isPublic: z.boolean().default(false),
  tags: z.array(z.string()).max(10, 'Maximum 10 tags allowed').default([]),
  settings: z.record(z.unknown()).refine(
    (data) => Object.keys(data).length > 0,
    { message: 'Settings cannot be empty' }
  )
});

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const user = authResult.user;
    
    const body = await request.json();
    
    // Validate request body
    const validationResult = createProfileSchema.safeParse(body);
    if (!validationResult.success) {
      return ErrorResponses.validationError(validationResult.error);
    }
    
    const { name, description, category, isPublic, tags, settings } = validationResult.data;
    
    const userId = user.id;

    const profile = await db.selectionProfiles.create({
      user_id: userId,
      name,
      description: description || '',
      category,
      is_public: isPublic,
      is_featured: false,
      settings,
      tags,
      usage_count: 0,
    });

    return NextResponse.json({ profile });
  } catch (error) {
    return handleApiError(error, 'Profiles POST');
  }
}