'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { useWizardStore } from '@/stores/wizard-store'

export function StepThemesContent() {
  const { selections, updateSelections } = useWizardStore()
  
  const majorThemes = [
    'Love & Relationships',
    'Good vs Evil',
    'Coming of Age',
    'Redemption',
    'Sacrifice',
    'Power & Corruption',
    'Identity',
    'Family',
    'Survival',
    'Justice'
  ]
  
  const philosophicalThemes = [
    'Existentialism',
    'Morality',
    'Free Will vs Destiny',
    'Nature vs Nurture',
    'Technology vs Humanity',
    'Life & Death',
    'Truth & Reality',
    'Hope & Despair'
  ]
  
  const socialThemes = [
    'Class Struggle',
    'Prejudice & Discrimination',
    'War & Peace',
    'Environmental Issues',
    'Political Intrigue',
    'Social Justice',
    'Cultural Identity',
    'Generational Conflict'
  ]
  
  const contentWarnings = [
    'Violence',
    'Sexual Content',
    'Strong Language',
    'Substance Use',
    'Mental Health Issues',
    'Death/Suicide',
    'Abuse',
    'Trauma',
    'Religious Content',
    'Political Content'
  ]
  
  const handleArrayToggle = (array: string[], item: string, key: keyof typeof selections) => {
    const currentArray = array || []
    const newArray = currentArray.includes(item)
      ? currentArray.filter(i => i !== item)
      : [...currentArray, item]
    updateSelections({ [key]: newArray })
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Themes & Messages</CardTitle>
          <CardDescription>
            Choose the themes and messages you want to explore in your story.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Major Themes (select all that apply)</Label>
            <div className="grid grid-cols-2 gap-2">
              {majorThemes.map((theme) => (
                <div key={theme} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`major-${theme}`}
                    checked={selections.majorThemes?.includes(theme) || false}
                    onCheckedChange={() => 
                      handleArrayToggle(selections.majorThemes || [], theme, 'majorThemes')
                    }
                  />
                  <Label htmlFor={`major-${theme}`} className="text-sm font-normal">
                    {theme}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Philosophical Themes (select all that apply)</Label>
            <div className="grid grid-cols-2 gap-2">
              {philosophicalThemes.map((theme) => (
                <div key={theme} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`philosophical-${theme}`}
                    checked={selections.philosophicalThemes?.includes(theme) || false}
                    onCheckedChange={() => 
                      handleArrayToggle(selections.philosophicalThemes || [], theme, 'philosophicalThemes')
                    }
                  />
                  <Label htmlFor={`philosophical-${theme}`} className="text-sm font-normal">
                    {theme}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Social Themes (select all that apply)</Label>
            <div className="grid grid-cols-2 gap-2">
              {socialThemes.map((theme) => (
                <div key={theme} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`social-${theme}`}
                    checked={selections.socialThemes?.includes(theme) || false}
                    onCheckedChange={() => 
                      handleArrayToggle(selections.socialThemes || [], theme, 'socialThemes')
                    }
                  />
                  <Label htmlFor={`social-${theme}`} className="text-sm font-normal">
                    {theme}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customThemes">Custom Themes (Optional)</Label>
            <Textarea
              id="customThemes"
              placeholder="Describe any unique themes or messages you want to explore..."
              value={selections.customThemes || ''}
              onChange={(e) => updateSelections({ customThemes: e.target.value })}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Content Guidelines</CardTitle>
          <CardDescription>
            Set content boundaries and audience considerations for your story.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Content Warnings (select all that apply)</Label>
            <p className="text-xs text-muted-foreground mb-2">
              Select topics that will be present in your story to ensure appropriate handling.
            </p>
            <div className="grid grid-cols-2 gap-2">
              {contentWarnings.map((warning) => (
                <div key={warning} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`warning-${warning}`}
                    checked={selections.contentWarnings?.includes(warning) || false}
                    onCheckedChange={() => 
                      handleArrayToggle(selections.contentWarnings || [], warning, 'contentWarnings')
                    }
                  />
                  <Label htmlFor={`warning-${warning}`} className="text-sm font-normal">
                    {warning}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="culturalSensitivityNotes">Cultural Sensitivity Notes (Optional)</Label>
            <Textarea
              id="culturalSensitivityNotes"
              placeholder="Note any cultural elements that require sensitivity or research, representation goals, or authenticity requirements..."
              value={selections.culturalSensitivityNotes || ''}
              onChange={(e) => updateSelections({ culturalSensitivityNotes: e.target.value })}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Thematic Preview</CardTitle>
          <CardDescription>
            Based on your selections, here&apos;s how your themes will be integrated.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border p-4 bg-muted/50">
            <div className="space-y-2 text-sm">
              <div>
                <strong>Major Themes:</strong> {selections.majorThemes?.join(', ') || 'None selected'}
              </div>
              <div>
                <strong>Philosophical Focus:</strong> {selections.philosophicalThemes?.join(', ') || 'None selected'}
              </div>
              <div>
                <strong>Social Elements:</strong> {selections.socialThemes?.join(', ') || 'None selected'}
              </div>
              <div>
                <strong>Content Rating:</strong> {selections.contentRating || 'Not specified'}
              </div>
              <div>
                <strong>Target Audience:</strong> {selections.targetAudience || 'Not specified'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}