'use client'

import React from 'react'
import { APIErrorBoundary } from '@/components/error/api-error-boundary'
import { ComponentErrorBoundary } from '@/components/error/component-error-boundary'
import { ProjectCardSkeleton } from '@/components/loading/skeleton-loader'
import { ProjectLoader } from '@/components/loading/loading-spinner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertTriangle, RefreshCw, Home, Plus } from 'lucide-react'
import Link from 'next/link'
import { config } from '@/lib/config'

interface ProjectPageWrapperProps {
  children: React.ReactNode
  projectId?: string
  loadingVariant?: 'spinner' | 'skeleton'
  _showProjectActions?: boolean
}

function ProjectNotFoundFallback({ projectId }: { projectId?: string }) {
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/dashboard">
            <Button variant="ghost">← Back to Dashboard</Button>
          </Link>
          <h1 className="text-2xl font-bold">Project Not Found</h1>
        </div>
      </header>
      
      <main className="container py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-amber-100 rounded-full w-fit">
              <AlertTriangle className="h-6 w-6 text-amber-600" />
            </div>
            <CardTitle>Project Not Found</CardTitle>
            <CardDescription>
              {projectId 
                ? `Project "${projectId}" doesn't exist or you don't have access to it.`
                : "The requested project could not be found."
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Link href="/dashboard" className="flex-1">
                <Button variant="outline" className="w-full">
                  <Home className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
              </Link>
              <Link href="/projects/new" className="flex-1">
                <Button className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  New Project
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

interface ErrorWithStatus extends Error {
  status?: number;
}

function ProjectErrorFallback({ 
  error, 
  projectId, 
  retry 
}: { 
  error: Error
  projectId?: string
  retry: () => void 
}) {
  const errorWithStatus = error as ErrorWithStatus;
  const is404 = errorWithStatus?.status === 404 || error?.message?.includes('not found')
  
  if (is404) {
    return <ProjectNotFoundFallback projectId={projectId} />
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/dashboard">
            <Button variant="ghost">← Back to Dashboard</Button>
          </Link>
          <h1 className="text-2xl font-bold">Project Error</h1>
        </div>
      </header>
      
      <main className="container py-8">
        <Card className="max-w-lg mx-auto">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle>Failed to Load Project</CardTitle>
            <CardDescription>
              There was an error loading this project. Please try again.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error?.message && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error Details</AlertTitle>
                <AlertDescription>{error.message}</AlertDescription>
              </Alert>
            )}
            
            <div className="flex gap-2">
              <Button onClick={retry} variant="outline" className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Link href="/dashboard" className="flex-1">
                <Button className="w-full">
                  <Home className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

function ProjectLoadingFallback({ 
  variant = 'skeleton' 
}: { 
  projectId?: string
  variant?: 'spinner' | 'skeleton' 
}) {
  if (variant === 'spinner') {
    return (
      <div className="min-h-screen bg-background">
        <header className="border-b">
          <div className="container flex h-16 items-center justify-between">
            <Link href="/dashboard">
              <Button variant="ghost">← Back to Dashboard</Button>
            </Link>
            <div className="h-8 w-32 bg-muted animate-pulse rounded" />
          </div>
        </header>
        
        <main className="container py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <ProjectLoader text="Loading project..." size="lg" />
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/dashboard">
            <Button variant="ghost">← Back to Dashboard</Button>
          </Link>
          <div className="h-8 w-32 bg-muted animate-pulse rounded" />
        </div>
      </header>
      
      <main className="container py-8">
        <div className="grid gap-8 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <ProjectCardSkeleton />
            <ProjectCardSkeleton />
          </div>
          <div className="space-y-6">
            <ProjectCardSkeleton />
            <ProjectCardSkeleton />
          </div>
        </div>
      </main>
    </div>
  )
}

export function ProjectPageWrapper({
  children,
  projectId,
  loadingVariant = 'skeleton'
}: ProjectPageWrapperProps) {
  const handleRetry = React.useCallback(() => {
    window.location.reload()
  }, [])

  return (
    <APIErrorBoundary
      fallbackComponent={({ error, retry }) => (
        <ProjectErrorFallback 
          error={error} 
          projectId={projectId} 
          retry={retry} 
        />
      )}
      onRetry={handleRetry}
      retryable={true}
      showErrorDetails={config.isDevelopment}
    >
      <React.Suspense
        fallback={
          <ProjectLoadingFallback 
            projectId={projectId} 
            variant={loadingVariant} 
          />
        }
      >
        {children}
      </React.Suspense>
    </APIErrorBoundary>
  )
}

// HOC for wrapping project pages
export function withProjectPageWrapper<T extends object>(
  Component: React.ComponentType<T>,
  options?: Omit<ProjectPageWrapperProps, 'children'>
) {
  const WrappedComponent = (props: T & { params?: { id: string } }) => {
    const propsWithParams = props as T & { params?: { id: string } };
    const projectId = propsWithParams.params?.id

    return (
      <ProjectPageWrapper projectId={projectId} {...options}>
        <Component {...props} />
      </ProjectPageWrapper>
    )
  }

  WrappedComponent.displayName = `withProjectPageWrapper(${Component.displayName || Component.name})`
  return WrappedComponent
}

// Specialized wrapper for project sections
export function ProjectSectionWrapper({
  children,
  title,
  description,
  actions
}: {
  children: React.ReactNode
  title?: string
  description?: string
  actions?: React.ReactNode
}) {
  return (
    <APIErrorBoundary
      retryable={true}
      showErrorDetails={false}
    >
      <Card>
        {(title || description || actions) && (
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                {title && <CardTitle>{title}</CardTitle>}
                {description && <CardDescription>{description}</CardDescription>}
              </div>
              {actions && <div className="flex gap-2">{actions}</div>}
            </div>
          </CardHeader>
        )}
        <CardContent>
          <React.Suspense fallback={<ProjectCardSkeleton />}>
            {children}
          </React.Suspense>
        </CardContent>
      </Card>
    </APIErrorBoundary>
  )
}