'use client';

import { useEffect, useRef, useState } from 'react';
import Editor from '@monaco-editor/react';
import type { Monaco } from '@monaco-editor/react';
import { editor } from 'monaco-editor';
import { useEditorStore } from '@/stores/editor-store';
import { SelectionMenu } from './selection-menu';
import { FormattingToolbar } from './formatting-toolbar';
import { AISuggestions } from './ai-suggestions';
import { WritingStats } from './writing-stats';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, CheckCircle, X } from 'lucide-react';
import { ComponentErrorBoundary } from '@/components/error/component-error-boundary';

type MonacoEditor = editor.IStandaloneCodeEditor;
type CursorSelectionChangedEvent = editor.ICursorSelectionChangedEvent;
type CursorPositionChangedEvent = editor.ICursorPositionChangedEvent;

interface Suggestion {
  id: string;
  type: 'grammar' | 'style' | 'character' | 'plot' | 'pacing' | 'dialogue';
  severity: 'error' | 'warning' | 'suggestion';
  message: string;
  range: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  replacement?: string;
  explanation?: string;
}

interface UnifiedMonacoEditorProps {
  // Basic props
  value?: string;
  initialContent?: string;
  onChange?: (value: string) => void;
  onContentChange?: (content: string) => void;
  onSave?: () => void;
  readOnly?: boolean;
  height?: string;
  
  // Feature toggles
  showToolbar?: boolean;
  showStats?: boolean;
  showAISuggestions?: boolean;
  showSelectionMenu?: boolean;
  enableRealTimeAnalysis?: boolean;
  
  // Advanced features
  projectId?: string;
  chapterNumber?: number;
  
  // Mode selection
  mode?: 'basic' | 'advanced';
}

export function UnifiedMonacoEditor({ 
  value,
  initialContent = '', 
  onChange,
  onContentChange,
  onSave,
  readOnly = false,
  height = '600px',
  showToolbar = true,
  showStats = false,
  showAISuggestions = true,
  showSelectionMenu = true,
  enableRealTimeAnalysis = false,
  projectId,
  chapterNumber,
  mode = 'basic'
}: UnifiedMonacoEditorProps) {
  const { 
    content, 
    setContent, 
    setSelection, 
    clearSelection,
    selectedText 
  } = useEditorStore();
  
  const editorRef = useRef<MonacoEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const [showSelectionMenuState, setShowSelectionMenuState] = useState(false);
  const [selectionPosition, setSelectionPosition] = useState({ x: 0, y: 0 });
  const [showAISuggestionsPanel, setShowAISuggestionsPanel] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [, setUserVoiceProfile] = useState<Record<string, unknown> | null>(null);

  const editorValue = value !== undefined ? value : content;

  useEffect(() => {
    if (initialContent && content !== initialContent) {
      setContent(initialContent);
    }
  }, [initialContent, content, setContent]);

  const handleEditorDidMount = (editor: MonacoEditor, monaco: Monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;

    // Configure editor for prose writing
    editor.updateOptions({
      wordWrap: 'bounded',
      wordWrapColumn: 80,
      lineNumbers: 'off',
      minimap: { enabled: false },
      renderLineHighlight: 'none',
      scrollBeyondLastLine: false,
      padding: { top: 16, bottom: 16 },
      fontFamily: '"Merriweather", "Georgia", serif',
      fontSize: 16,
      lineHeight: 24,
      letterSpacing: 0.3,
      glyphMargin: false,
      folding: false,
      lineDecorationsWidth: 0,
      lineNumbersMinChars: 0,
      overviewRulerLanes: 0,
      hideCursorInOverviewRuler: true,
      scrollbar: {
        vertical: 'visible',
        horizontal: 'hidden',
        verticalSliderSize: 8,
      },
      quickSuggestions: false,
      suggestOnTriggerCharacters: false,
      acceptSuggestionOnCommitCharacter: false,
      tabCompletion: 'off',
      wordBasedSuggestions: 'off',
      renderWhitespace: 'none',
      renderControlCharacters: false,
      guides: {
        indentation: false,
      },
    });

    // Save on Ctrl+S
    editor.addAction({
      id: 'save-content',
      label: 'Save',
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
      run: () => {
        onSave?.();
      }
    });

    if (showSelectionMenu) {
      // Handle text selection
      editor.onDidChangeCursorSelection((e: CursorSelectionChangedEvent) => {
        const selection = e.selection;
        const text = editor.getModel()?.getValueInRange(selection) || '';
        
        if (text.trim().length > 0) {
          const model = editor.getModel();
          if (model) {
            const startOffset = model.getOffsetAt(selection.getStartPosition());
            const endOffset = model.getOffsetAt(selection.getEndPosition());
            setSelection(text, startOffset, endOffset);
          }
          
          const position = editor.getScrolledVisiblePosition(selection.getEndPosition());
          if (position) {
            setSelectionPosition({
              x: position.left,
              y: position.top + position.height
            });
            setShowSelectionMenuState(true);
          }
        } else {
          clearSelection();
          setShowSelectionMenuState(false);
        }
      });
    }

    // Track cursor position for AI suggestions
    editor.onDidChangeCursorPosition((e: CursorPositionChangedEvent) => {
      const position = e.position;
      const offset = editor.getModel()?.getOffsetAt(position) || 0;
      setCursorPosition(offset);
    });

    // Set up real-time analysis if in advanced mode
    if (mode === 'advanced' && enableRealTimeAnalysis) {
      let analysisTimeout: NodeJS.Timeout;
      
      editor.onDidChangeModelContent(() => {
        clearTimeout(analysisTimeout);
        analysisTimeout = setTimeout(() => {
          analyzeContent();
        }, 2000);
      });

      // Initial analysis
      analyzeContent();
    }
  };

  const handleEditorChange = (value: string | undefined) => {
    const newContent = value || '';
    setContent(newContent);
    onContentChange?.(newContent);
    onChange?.(newContent);
  };

  const analyzeContent = async () => {
    if (!projectId || !editorRef.current) return;
    
    setIsAnalyzing(true);
    
    try {
      const model = editorRef.current.getModel();
      const content = model?.getValue() || '';
      
      const response = await fetch('/api/analysis/content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          projectId,
          chapterNumber,
          analysisTypes: ['grammar', 'style', 'character', 'plot', 'pacing', 'dialogue'],
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
        setUserVoiceProfile(data.voiceProfile || null);
        
        // Apply decorations
        if (monacoRef.current && model) {
          const decorations = suggestions.map(suggestion => ({
            range: suggestion.range,
            options: {
              isWholeLine: false,
              className: `suggestion-${suggestion.severity}`,
              glyphMarginClassName: `glyph-${suggestion.severity}`,
              hoverMessage: { value: suggestion.message },
            },
          }));
          
          editorRef.current.deltaDecorations([], decorations);
        }
      }
    } catch (error) {
      console.error('Error analyzing content:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const applySuggestion = (suggestion: Suggestion) => {
    if (!editorRef.current || !suggestion.replacement) return;
    
    const model = editorRef.current.getModel();
    if (!model) return;
    
    const edit = {
      range: suggestion.range,
      text: suggestion.replacement,
    };
    
    editorRef.current.executeEdits('apply-suggestion', [edit]);
    
    // Remove the suggestion
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
  };

  const dismissSuggestion = (suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  };

  const handleFormat = (command: string, value?: string) => {
    if (!editorRef.current) return;
    
    const editor = editorRef.current;
    const selection = editor.getSelection();
    if (!selection) return;
    
    const selectedText = editor.getModel()?.getValueInRange(selection) || '';
    let formattedText = selectedText;
    
    // Apply formatting based on command
    switch (command) {
      case 'bold':
        formattedText = selectedText ? `**${selectedText}**` : '**bold text**';
        break;
      case 'italic':
        formattedText = selectedText ? `*${selectedText}*` : '*italic text*';
        break;
      case 'underline':
        formattedText = selectedText ? `<u>${selectedText}</u>` : '<u>underlined text</u>';
        break;
      case 'strikethrough':
        formattedText = selectedText ? `~~${selectedText}~~` : '~~strikethrough text~~';
        break;
      case 'heading1':
        formattedText = `# ${selectedText || 'Heading 1'}`;
        break;
      case 'heading2':
        formattedText = `## ${selectedText || 'Heading 2'}`;
        break;
      case 'heading3':
        formattedText = `### ${selectedText || 'Heading 3'}`;
        break;
      case 'bulletList':
        formattedText = `- ${selectedText || 'List item'}`;
        break;
      case 'orderedList':
        formattedText = `1. ${selectedText || 'List item'}`;
        break;
      case 'blockquote':
        formattedText = `> ${selectedText || 'Quote'}`;
        break;
      case 'code':
        formattedText = selectedText ? `\`${selectedText}\`` : '`code`';
        break;
      case 'link':
        formattedText = `[${selectedText || 'link text'}](${value || 'https://example.com'})`;
        break;
      case 'image':
        formattedText = `![${selectedText || 'alt text'}](${value || 'image-url.jpg'})`;
        break;
      default:
        return;
    }
    
    // Replace selection with formatted text
    editor.executeEdits('format-text', [{
      range: selection,
      text: formattedText
    }]);
    
    // Adjust cursor position for empty selections
    if (!selectedText) {
      const position = selection.getStartPosition();
      let newColumn = position.column;
      
      // Position cursor inside the formatting marks
      switch (command) {
        case 'bold':
          newColumn += 2;
          break;
        case 'italic':
          newColumn += 1;
          break;
        case 'underline':
          newColumn += 3;
          break;
        case 'strikethrough':
          newColumn += 2;
          break;
      }
      
      editor.setPosition({ lineNumber: position.lineNumber, column: newColumn });
    }
  };

  return (
    <div className="relative h-full">
      {showToolbar && (
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <FormattingToolbar onFormat={handleFormat} />
        </div>
      )}
      
      <div className="relative" style={{ height: showToolbar ? `calc(${height} - 48px)` : height }}>
        <Editor
          defaultLanguage="markdown"
          value={editorValue}
          onChange={handleEditorChange}
          onMount={handleEditorDidMount}
          theme="vs-dark"
          options={{
            readOnly,
            automaticLayout: true,
          }}
        />
        
        {showSelectionMenuState && showSelectionMenu && selectedText && (
          <SelectionMenu 
            selectedText={selectedText}
            position={selectionPosition}
            onClose={() => setShowSelectionMenuState(false)}
            onApplyEdit={(newText) => {
              if (!editorRef.current) return;
              const selection = editorRef.current.getSelection();
              if (!selection) return;
              editorRef.current.executeEdits('apply-edit', [{
                range: selection,
                text: newText
              }]);
              setShowSelectionMenuState(false);
            }}
          />
        )}
        
        {showAISuggestionsPanel && showAISuggestions && (
          <div className="absolute top-0 right-0 w-80 h-full bg-background border-l">
            <AISuggestions 
              content={content}
              cursorPosition={cursorPosition}
              onInsertSuggestion={(text) => {
                // Insert suggestion at cursor position
                const editor = editorRef.current;
                if (editor) {
                  const position = editor.getPosition();
                  if (position) {
                    editor.executeEdits('ai-suggestion', [{
                      range: {
                        startLineNumber: position.lineNumber,
                        startColumn: position.column,
                        endLineNumber: position.lineNumber,
                        endColumn: position.column
                      },
                      text: text
                    }]);
                  }
                }
              }}
              onClose={() => setShowAISuggestionsPanel(false)}
              visible={showAISuggestionsPanel}
            />
          </div>
        )}
        
        {mode === 'advanced' && suggestions.length > 0 && (
          <Card className="absolute top-4 right-4 w-80 max-h-96 overflow-y-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <Lightbulb className="h-4 w-4" />
                  Writing Suggestions
                </h3>
                <Badge variant="outline">{suggestions.length}</Badge>
              </div>
              
              <div className="space-y-2">
                {suggestions.map((suggestion) => (
                  <div
                    key={suggestion.id}
                    className={`p-3 rounded-lg border ${
                      suggestion.severity === 'error'
                        ? 'border-destructive bg-destructive/10'
                        : suggestion.severity === 'warning'
                        ? 'border-yellow-500 bg-yellow-500/10'
                        : 'border-blue-500 bg-blue-500/10'
                    }`}
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1">
                        <Badge
                          variant={suggestion.severity === 'error' ? 'destructive' : 'secondary'}
                          className="mb-1"
                        >
                          {suggestion.type}
                        </Badge>
                        <p className="text-sm">{suggestion.message}</p>
                        {suggestion.explanation && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {suggestion.explanation}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-1">
                        {suggestion.replacement && (
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-6 w-6"
                            onClick={() => applySuggestion(suggestion)}
                          >
                            <CheckCircle className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6"
                          onClick={() => dismissSuggestion(suggestion.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}
      </div>
      
      {showStats && (
        <div className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <WritingStats content={editorValue} />
        </div>
      )}
      
      {mode === 'advanced' && isAnalyzing && (
        <div className="absolute bottom-4 left-4">
          <Badge variant="secondary" className="animate-pulse">
            Analyzing content...
          </Badge>
        </div>
      )}
    </div>
  );
}

// Export wrapped component with error boundary
export function UnifiedMonacoEditorWithErrorBoundary(props: UnifiedMonacoEditorProps) {
  return (
    <ComponentErrorBoundary componentName="Monaco Editor">
      <UnifiedMonacoEditor {...props} />
    </ComponentErrorBoundary>
  );
}