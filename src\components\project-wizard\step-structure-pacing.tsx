'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useWizardStore } from '@/stores/wizard-store'
import { STRUCTURE_TYPES, PACING_PREFERENCES } from '@/lib/types/project'

export function StepStructurePacing() {
  const { selections, updateSelections } = useWizardStore()
  
  const chapterStructures = [
    { value: 'fixed_length', label: 'Fixed Length Chapters' },
    { value: 'variable_length', label: 'Variable Length Chapters' },
    { value: 'scene_based', label: 'Scene-Based Chapters' },
    { value: 'time_based', label: 'Time-Based Chapters' }
  ]
  
  const timelineComplexities = [
    { value: 'linear', label: 'Linear Timeline' },
    { value: 'flashbacks', label: 'With Flashbacks' },
    { value: 'multiple_timelines', label: 'Multiple Timelines' },
    { value: 'non_linear', label: 'Non-Linear Narrative' }
  ]
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Story Structure</CardTitle>
          <CardDescription>
            Choose the narrative framework that will guide your story&apos;s development.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Structure Type</Label>
            <Select 
              value={selections.structureType} 
              onValueChange={(value) => updateSelections({ structureType: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select story structure" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(STRUCTURE_TYPES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Timeline Complexity</Label>
            <Select 
              value={selections.timelineComplexity} 
              onValueChange={(value) => updateSelections({ timelineComplexity: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select timeline complexity" />
              </SelectTrigger>
              <SelectContent>
                {timelineComplexities.map(({ value, label }) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customStructure">Custom Structure Notes (Optional)</Label>
            <Textarea
              id="customStructure"
              placeholder="Describe any specific structural requirements or unique narrative approaches..."
              value={selections.customStructureNotes || ''}
              onChange={(e) => updateSelections({ customStructureNotes: e.target.value })}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Pacing & Chapter Structure</CardTitle>
          <CardDescription>
            Define how your story will unfold and how chapters will be organized.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Pacing Preference</Label>
            <Select 
              value={selections.pacingPreference} 
              onValueChange={(value) => updateSelections({ pacingPreference: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select pacing style" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(PACING_PREFERENCES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Chapter Structure</Label>
            <Select 
              value={selections.chapterStructure} 
              onValueChange={(value) => updateSelections({ chapterStructure: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select chapter structure" />
              </SelectTrigger>
              <SelectContent>
                {chapterStructures.map(({ value, label }) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Structure Preview</CardTitle>
          <CardDescription>
            Based on your selections, here&apos;s how your story structure will be organized.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border p-4 bg-muted/50">
            <div className="space-y-2 text-sm">
              <div>
                <strong>Framework:</strong> {selections.structureType || 'Not selected'}
              </div>
              <div>
                <strong>Pacing:</strong> {selections.pacingPreference || 'Not selected'}
              </div>
              <div>
                <strong>Timeline:</strong> {selections.timelineComplexity || 'Not selected'}
              </div>
              <div>
                <strong>Chapters:</strong> {selections.chapterStructure || 'Not selected'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}