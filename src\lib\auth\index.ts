// Server-side authentication utilities
export {
  authenticateUser,
  createErrorResponse,
  createSuccessResponse,
  validateEnvironment,
  handleRouteError
} from './server'

// Admin authentication utilities
export {
  authenticateAdmin,
  requireAdmin
} from './admin'

// Ownership validation utilities
export {
  validateUserOwnership,
  validateUserOwnershipViaJoin,
  validateBulkUserOwnership,
  validateProjectOwnership,
  validateChapterOwnership
} from './validation'

// Types and constants
export type {
  AuthResult,
  OwnershipResult,
  AuthError,
  AuthErrorType
} from './types'

export type { AdminAuthResult } from './admin'

export { AUTH_ERRORS } from './types'