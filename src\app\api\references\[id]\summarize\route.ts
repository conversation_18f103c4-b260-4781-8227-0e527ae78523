import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { authenticateUser } from '@/lib/auth';
import OpenAI from 'openai';
import { config } from '@/lib/config';

const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }
    const { user, supabase } = authResult;
    
    if (!user || !supabase) {
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 })
    }

    const { id: materialId } = await params;

    // Get the material
    const { data: material, error: fetchError } = await supabase
      .from('reference_materials')
      .select('*')
      .eq('id', materialId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Material not found' }, { status: 404 });
      }
      throw fetchError;
    }

    // Check if already has AI summary (stored in description for now)
    if (material.description && material.description.startsWith('[AI Summary]')) {
      return NextResponse.json({ 
        summary: material.description.replace('[AI Summary] ', ''),
        message: 'Summary already exists' 
      });
    }

    let contentToSummarize = '';

    // Determine content source based on material type
    if (material.file_type === 'url' && material.file_url) {
      // For URLs, we might need to fetch content (basic implementation)
      try {
        const response = await fetch(material.file_url);
        const html = await response.text();
        // Extract text content (simplified - in production, use proper HTML parsing)
        contentToSummarize = html.replace(/<[^>]*>/g, ' ').substring(0, 10000);
      } catch {
        contentToSummarize = `URL: ${material.file_url}\nTitle: ${material.name}\nDescription: ${material.description || 'No description available'}`;
      }
    } else if (material.file_type === 'document' && material.file_url) {
      // For documents, we'd need to extract text (placeholder)
      contentToSummarize = `Document: ${material.name}\nDescription: ${material.description || 'No description available'}\nFile Type: ${material.mime_type || 'Unknown'}`;
    } else {
      contentToSummarize = `${material.name}\n${material.description || 'No additional content available'}`;
    }

    if (!contentToSummarize.trim()) {
      return NextResponse.json({ error: 'No content available to summarize' }, { status: 400 });
    }

    // Generate AI summary
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: `You are an AI assistant that creates concise, helpful summaries of reference materials for fiction writers. 

Your task is to analyze the provided content and create a summary that will be useful for writers referencing this material in their creative work.

Focus on:
- Key themes, concepts, or information
- How this material might be useful for story development
- Important details that a writer should remember
- Any inspiration or ideas this material might spark

Keep the summary concise (2-4 sentences) but informative. Write in a way that helps writers quickly understand the value and content of this reference material.`
        },
        {
          role: 'user',
          content: `Please summarize this reference material:\n\nTitle: ${material.name}\nType: ${material.file_type}\nDescription: ${material.description || 'None provided'}\n\nContent:\n${contentToSummarize.substring(0, 8000)}`
        }
      ],
      max_tokens: 300,
      temperature: 0.7,
    });

    const summary = completion.choices[0]?.message?.content?.trim();

    if (!summary) {
      return NextResponse.json({ error: 'Failed to generate summary' }, { status: 500 });
    }

    // Update the material with the AI summary (store in description with prefix)
    const updatedDescription = `[AI Summary] ${summary}`;
    const { data: updatedMaterial, error: updateError } = await supabase
      .from('reference_materials')
      .update({ 
        description: updatedDescription,
        updated_at: new Date().toISOString()
      })
      .eq('id', materialId)
      .select()
      .single();

    if (updateError) throw updateError;

    return NextResponse.json({ 
      summary,
      material: {
        id: updatedMaterial.id,
        projectId: updatedMaterial.project_id,
        name: updatedMaterial.name,
        description: updatedMaterial.description,
        fileType: updatedMaterial.file_type,
        fileUrl: updatedMaterial.file_url,
        fileSize: updatedMaterial.file_size,
        mimeType: updatedMaterial.mime_type,
        tags: updatedMaterial.tags || [],
        createdAt: new Date(updatedMaterial.created_at),
        updatedAt: new Date(updatedMaterial.updated_at),
      }
    });
  } catch (error) {
    console.error('Summarization API error:', error);
    
    // Handle OpenAI API errors specifically
    if (error instanceof Error && error.message.includes('API key')) {
      return NextResponse.json({ error: 'AI service configuration error' }, { status: 503 });
    }
    
    return NextResponse.json({ error: 'Failed to generate summary' }, { status: 500 });
  }
}