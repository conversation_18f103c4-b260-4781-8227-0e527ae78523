'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AlertCircle, RefreshCw } from 'lucide-react'

export default function DashboardError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Dashboard error:', error)
  }, [error])

  return (
    <div className="flex min-h-[400px] flex-col items-center justify-center p-8">
      <div className="text-center">
        <AlertCircle className="mx-auto h-10 w-10 text-destructive mb-4" />
        <h2 className="mb-2 text-xl font-semibold">Dashboard Error</h2>
        <p className="mb-4 text-sm text-muted-foreground max-w-md">
          We encountered an error loading your dashboard. This might be a temporary issue.
        </p>
        <Button onClick={reset} variant="default" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Retry
        </Button>
      </div>
    </div>
  )
}