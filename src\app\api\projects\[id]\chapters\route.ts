import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { chapterQuerySchema, createChapterSchema } from '@/lib/validation/schemas'
import { z } from 'zod'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = {
      project_id: projectId, // Use the project ID from the URL
      status: searchParams.get('status'),
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined
    }

    const validatedQuery = chapterQuerySchema.parse(queryParams)

    // Verify project ownership first
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Build query for chapters
    let query = supabase
      .from('chapters')
      .select('*', { count: 'exact' })
      .eq('project_id', projectId)

    // Apply filters
    if (validatedQuery.status) {
      query = query.eq('status', validatedQuery.status)
    }

    // Apply pagination
    if (validatedQuery.limit) {
      query = query.limit(validatedQuery.limit)
    }
    if (validatedQuery.offset) {
      query = query.range(validatedQuery.offset, (validatedQuery.offset + (validatedQuery.limit || 50)) - 1)
    }

    // Order by chapter number
    query = query.order('chapter_number', { ascending: true })

    const { data: chapters, error, count } = await query

    if (error) {
      console.error('Error fetching chapters:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Calculate summary statistics
    const stats = chapters?.reduce((acc, chapter) => {
      acc.totalWordCount += chapter.actual_word_count || 0
      acc.targetWordCount += chapter.target_word_count || 0
      
      switch (chapter.status) {
        case 'planned':
          acc.statusCounts.planned++
          break
        case 'writing':
          acc.statusCounts.writing++
          break
        case 'review':
          acc.statusCounts.review++
          break
        case 'complete':
          acc.statusCounts.complete++
          break
      }
      
      return acc
    }, {
      totalWordCount: 0,
      targetWordCount: 0,
      statusCounts: {
        planned: 0,
        writing: 0,
        review: 0,
        complete: 0
      }
    })

    return NextResponse.json({ 
      chapters: chapters || [],
      stats: stats || {
        totalWordCount: 0,
        targetWordCount: 0,
        statusCounts: { planned: 0, writing: 0, review: 0, complete: 0 }
      },
      pagination: {
        total: count,
        limit: validatedQuery.limit || 50,
        offset: validatedQuery.offset || 0
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error in project chapters GET:', error)
    return NextResponse.json(
      { error: 'Failed to fetch chapters' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Add project_id from URL to the body
    const chapterData = {
      ...body,
      project_id: projectId
    }
    
    // Validate input
    const validatedData = createChapterSchema.parse(chapterData)

    // Verify project ownership
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Check if chapter number already exists in this project
    const { data: existingChapter } = await supabase
      .from('chapters')
      .select('id')
      .eq('project_id', projectId)
      .eq('chapter_number', validatedData.chapter_number)
      .single()

    if (existingChapter) {
      return NextResponse.json({ 
        error: 'Chapter with this number already exists in the project' 
      }, { status: 409 })
    }

    // Create chapter
    const { data: chapter, error } = await supabase
      .from('chapters')
      .insert({
        ...validatedData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating chapter:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Update project word count if content was provided
    if (validatedData.content && validatedData.actual_word_count) {
      const { data: allChapters } = await supabase
        .from('chapters')
        .select('actual_word_count')
        .eq('project_id', projectId)

      const totalWordCount = allChapters?.reduce(
        (sum, ch) => sum + (ch.actual_word_count || 0), 
        0
      ) || 0

      await supabase
        .from('projects')
        .update({ 
          current_word_count: totalWordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId)
    }

    return NextResponse.json({ chapter }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid chapter data',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error in project chapters POST:', error)
    return NextResponse.json(
      { error: 'Failed to create chapter' },
      { status: 500 }
    )
  }
}