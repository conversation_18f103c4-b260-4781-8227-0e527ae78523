import { MetadataRoute } from 'next'
import { config } from '@/lib/config'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = config.app.url
  
  // Static pages
  const staticPages = [
    '',
    '/login',
    '/signup',
    '/features',
    '/pricing',
    '/about',
    '/contact',
    '/privacy',
    '/terms',
  ].map(route => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: route === '' ? 1 : 0.8,
  }))

  // You can add dynamic pages here by fetching from database
  // const dynamicPages = await fetchDynamicPages()

  return [
    ...staticPages,
    // ...dynamicPages,
  ]
}