import { db } from '../db/client';
import { generateEmbedding } from '../openai';

// Mock the OpenAI function
jest.mock('../openai', () => ({
  generateEmbedding: jest.fn(),
}));

const mockGenerateEmbedding = generateEmbedding as jest.MockedFunction<typeof generateEmbedding>;

describe('Content Embeddings', () => {
  const mockEmbeddingArray = new Array(1536).fill(0.1);

  beforeEach(() => {
    jest.clearAllMocks();
    mockGenerateEmbedding.mockResolvedValue(mockEmbeddingArray);
  });

  describe('create', () => {
    it('should create a new embedding', async () => {
      // Note: In a real test, you'd mock the supabase client
      // For now, this test demonstrates the expected interface
      expect(db.embeddings.create).toBeDefined();
      expect(typeof db.embeddings.create).toBe('function');
    });
  });

  describe('search', () => {
    it('should search embeddings by natural language query', async () => {
      expect(db.embeddings.search).toBeDefined();
      expect(typeof db.embeddings.search).toBe('function');
      
      // Test the function signature
      const searchFunction = db.embeddings.search;
      expect(searchFunction).toEqual(expect.any(Function));
    });
  });

  describe('getByProject', () => {
    it('should retrieve embeddings for a project', async () => {
      expect(db.embeddings.getByProject).toBeDefined();
      expect(typeof db.embeddings.getByProject).toBe('function');
    });
  });

  describe('createBatch', () => {
    it('should create multiple embeddings efficiently', async () => {
      expect(db.embeddings.createBatch).toBeDefined();
      expect(typeof db.embeddings.createBatch).toBe('function');
    });
  });
});