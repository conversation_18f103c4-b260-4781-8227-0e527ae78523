import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { authenticateUser, handleRouteError } from '@/lib/auth';
import { createClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }

    const supabase = await createClient();
    
    // Get all series for the authenticated user
    const { data: series, error } = await supabase
      .from('series')
      .select(`
        *,
        series_books(
          book_number,
          project_id,
          book_role,
          projects(id, title, status)
        )
      `)
      .eq('user_id', authResult.user.id)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return NextResponse.json({ series: series || [] });
  } catch (error) {
    return handleRouteError(error, 'Series GET');
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    
    const body = await request.json();
    const { 
      title, 
      description, 
      genre,
      target_audience,
      overall_arc_description,
      planned_book_count 
    } = body;

    if (!title) {
      return NextResponse.json({ error: 'Series title is required' }, { status: 400 });
    }

    const supabase = await createClient();

    // Create new series
    const { data: series, error } = await supabase
      .from('series')
      .insert([{
        user_id: authResult.user.id,
        title,
        description,
        genre,
        target_audience,
        overall_arc_description,
        planned_book_count: planned_book_count || 1
      }])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({ series }, { status: 201 });
  } catch (error) {
    return handleRouteError(error, 'Series POST');
  }
}