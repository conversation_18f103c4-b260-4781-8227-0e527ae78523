import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'AI-Powered Novel Writing Platform - BookScribe AI',
  description: 'Transform your ideas into epic novels with AI assistance. BookScribe helps you write up to 300,000 words with perfect consistency, character development, and plot tracking.',
  keywords: [
    'AI novel writing',
    'book writing software',
    'AI author assistant',
    'novel generator',
    'creative writing AI',
    'story development tool',
    'character creation AI',
    'plot generator',
    'writing productivity',
    'AI writing assistant'
  ],
  openGraph: {
    title: 'Write Your Next Bestseller with BookScribe AI',
    description: 'Join thousands of authors using AI to write compelling novels. Start your free trial today.',
    type: 'website',
    images: [
      {
        url: '/og-home.jpg',
        width: 1200,
        height: 630,
        alt: 'BookScribe AI - Transform Ideas into Epic Novels',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Write Your Next Bestseller with BookScribe AI',
    description: 'Join thousands of authors using AI to write compelling novels. Start your free trial today.',
    images: ['/twitter-home.jpg'],
  },
  alternates: {
    canonical: 'https://bookscribe.ai',
  },
}