-- Collaboration Sessions Table
CREATE TABLE IF NOT EXISTS collaboration_sessions (
  id TEXT PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  owner_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  document_content TEXT DEFAULT '',
  document_version INTEGER DEFAULT 1,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for collaboration_sessions
CREATE INDEX idx_collaboration_sessions_project_id ON collaboration_sessions(project_id);
CREATE INDEX idx_collaboration_sessions_owner_id ON collaboration_sessions(owner_id);

-- Collaboration Participants Table
CREATE TABLE IF NOT EXISTS collaboration_participants (
  session_id TEXT NOT NULL REFERENCES collaboration_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('owner', 'editor', 'viewer', 'commenter')),
  status TEXT NOT NULL DEFAULT 'offline' CHECK (status IN ('online', 'offline')),
  cursor_position JSONB,
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  last_seen TIMESTAMPTZ DEFAULT NOW(),
  
  PRIMARY KEY (session_id, user_id)
);

-- Indexes for collaboration_participants
CREATE INDEX idx_collaboration_participants_user_id ON collaboration_participants(user_id);
CREATE INDEX idx_collaboration_participants_status ON collaboration_participants(status);

-- Collaboration Changes Table (for history)
CREATE TABLE IF NOT EXISTS collaboration_changes (
  id TEXT PRIMARY KEY,
  session_id TEXT NOT NULL REFERENCES collaboration_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('insert', 'delete', 'format', 'comment')),
  position JSONB NOT NULL,
  content TEXT,
  length INTEGER,
  data JSONB,
  version INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for collaboration_changes
CREATE INDEX idx_collaboration_changes_session_id ON collaboration_changes(session_id);
CREATE INDEX idx_collaboration_changes_created_at ON collaboration_changes(created_at DESC);

-- Collaboration Locks Table
CREATE TABLE IF NOT EXISTS collaboration_locks (
  session_id TEXT NOT NULL REFERENCES collaboration_sessions(id) ON DELETE CASCADE,
  section TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  PRIMARY KEY (session_id, section)
);

-- Indexes for collaboration_locks
CREATE INDEX idx_collaboration_locks_user_id ON collaboration_locks(user_id);

-- Enable Row Level Security
ALTER TABLE collaboration_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_changes ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_locks ENABLE ROW LEVEL SECURITY;

-- RLS Policies for collaboration_sessions
CREATE POLICY "Users can view sessions they participate in"
  ON collaboration_sessions FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM collaboration_participants
      WHERE collaboration_participants.session_id = collaboration_sessions.id
      AND collaboration_participants.user_id = auth.uid()
    )
  );

CREATE POLICY "Owners can update their sessions"
  ON collaboration_sessions FOR UPDATE
  USING (owner_id = auth.uid());

CREATE POLICY "Users can create sessions"
  ON collaboration_sessions FOR INSERT
  WITH CHECK (owner_id = auth.uid());

-- RLS Policies for collaboration_participants
CREATE POLICY "Participants can view session participants"
  ON collaboration_participants FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM collaboration_participants cp
      WHERE cp.session_id = collaboration_participants.session_id
      AND cp.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can join sessions"
  ON collaboration_participants FOR INSERT
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own participation"
  ON collaboration_participants FOR UPDATE
  USING (user_id = auth.uid());

-- RLS Policies for collaboration_changes
CREATE POLICY "Participants can view session changes"
  ON collaboration_changes FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM collaboration_participants
      WHERE collaboration_participants.session_id = collaboration_changes.session_id
      AND collaboration_participants.user_id = auth.uid()
    )
  );

CREATE POLICY "Participants can create changes"
  ON collaboration_changes FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM collaboration_participants
      WHERE collaboration_participants.session_id = collaboration_changes.session_id
      AND collaboration_participants.user_id = auth.uid()
      AND collaboration_participants.role IN ('owner', 'editor', 'commenter')
    )
  );

-- RLS Policies for collaboration_locks
CREATE POLICY "Participants can view locks"
  ON collaboration_locks FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM collaboration_participants
      WHERE collaboration_participants.session_id = collaboration_locks.session_id
      AND collaboration_participants.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create their own locks"
  ON collaboration_locks FOR INSERT
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own locks"
  ON collaboration_locks FOR DELETE
  USING (user_id = auth.uid());

-- Function to clean up expired locks
CREATE OR REPLACE FUNCTION cleanup_expired_locks()
RETURNS void AS $$
BEGIN
  DELETE FROM collaboration_locks
  WHERE created_at < NOW() - INTERVAL '5 minutes';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update last_seen timestamp
CREATE OR REPLACE FUNCTION update_participant_last_seen()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_seen = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update last_seen
CREATE TRIGGER update_participant_last_seen_trigger
  BEFORE UPDATE ON collaboration_participants
  FOR EACH ROW
  EXECUTE FUNCTION update_participant_last_seen();

-- Enable Realtime for collaboration tables
ALTER PUBLICATION supabase_realtime ADD TABLE collaboration_participants;
ALTER PUBLICATION supabase_realtime ADD TABLE collaboration_changes;
ALTER PUBLICATION supabase_realtime ADD TABLE collaboration_locks;