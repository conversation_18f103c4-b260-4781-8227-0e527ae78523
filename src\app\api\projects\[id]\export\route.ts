import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { exportService } from '@/lib/export/export-service';
import type { ExportOptions } from '@/lib/export/export-service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params;
    const options: ExportOptions = await request.json();

    // Validate required fields
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    if (!options.format) {
      return NextResponse.json({ error: 'Export format is required' }, { status: 400 });
    }

    // Export the project
    const blob = await exportService.exportProject(projectId, options);

    // Determine content type and filename extension
    let contentType: string;
    let fileExtension: string;

    switch (options.format) {
      case 'pdf':
        contentType = 'application/pdf';
        fileExtension = 'pdf';
        break;
      case 'docx':
        contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        fileExtension = 'docx';
        break;
      case 'epub':
        contentType = 'application/epub+zip';
        fileExtension = 'epub';
        break;
      case 'txt':
        contentType = 'text/plain';
        fileExtension = 'txt';
        break;
      case 'markdown':
        contentType = 'text/markdown';
        fileExtension = 'md';
        break;
      default:
        return NextResponse.json({ error: 'Unsupported format' }, { status: 400 });
    }

    // Convert blob to buffer for response
    const buffer = await blob.arrayBuffer();

    // Return the file
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="export.${fileExtension}"`,
        'Content-Length': buffer.byteLength.toString(),
      },
    });

  } catch (error) {
    console.error('Export error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json({ 
        error: 'Export failed', 
        details: error.message 
      }, { status: 500 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}