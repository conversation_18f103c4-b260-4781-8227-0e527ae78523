'use client'

import { useState, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'

export interface ChapterGenerationState {
  isGenerating: boolean
  isReviewing: boolean
  isSubmittingChanges: boolean
  generatedContent: string | null
  originalContent: string | null
  error: string | null
  currentStep: 'idle' | 'generating' | 'reviewing' | 'adjusting' | 'complete'
}

export interface GeneratedChapterData {
  content: string
  wordCount: number
  scenes: Array<{
    id: string
    content: string
    wordCount: number
    characters: string[]
    setting: string
    purpose: string
  }>
  characterVoices: Record<string, string>
  plotProgression: string[]
  continuityNotes: string[]
  qualityScore: number
  generationMetadata: {
    model: string
    tokensUsed: number
    generationTime: number
    revisionsCount: number
  }
}

export interface UserChanges {
  chapterId: string
  changes: Array<{
    type: 'plot' | 'character' | 'pacing' | 'style' | 'dialogue' | 'setting' | 'other'
    description: string
    impact: 'low' | 'medium' | 'high'
    affectedElements: string[]
    textChange: {
      from: string
      to: string
      startIndex: number
      endIndex: number
    }
  }>
  userNotes: string
  significantChanges: string[]
  requestsPlanUpdate: boolean
}

export function useChapterGeneration(projectId: string, chapterId: string) {
  const [state, setState] = useState<ChapterGenerationState>({
    isGenerating: false,
    isReviewing: false,
    isSubmittingChanges: false,
    generatedContent: null,
    originalContent: null,
    error: null,
    currentStep: 'idle'
  })

  const supabase = createClient()

  const generateChapter = useCallback(async (chapterNumber: number): Promise<GeneratedChapterData | null> => {
    setState(prev => ({
      ...prev,
      isGenerating: true,
      error: null,
      currentStep: 'generating'
    }))

    try {
      // Call the enhanced generation API
      const response = await fetch('/api/agents/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          action: 'generate_chapter_enhanced',
          chapterNumber
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate chapter')
      }

      const generatedData: GeneratedChapterData = result.data

      setState(prev => ({
        ...prev,
        isGenerating: false,
        generatedContent: generatedData.content,
        originalContent: generatedData.content,
        currentStep: 'reviewing'
      }))

      return generatedData

    } catch (error) {
      setState(prev => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Generation failed',
        currentStep: 'idle'
      }))
      return null
    }
  }, [projectId])

  const startReview = useCallback((originalContent: string, editedContent: string) => {
    setState(prev => ({
      ...prev,
      isReviewing: true,
      originalContent,
      generatedContent: editedContent,
      currentStep: 'reviewing'
    }))
  }, [])

  const updateChapterContent = useCallback(async (chapterId: string, changes: UserChanges): Promise<void> => {
    // In a real implementation, this would reconstruct the content from changes
    // For now, we'll assume the edited content is available in the component
    const editedContent = state.generatedContent || ''
    const wordCount = editedContent.trim().split(/\s+/).filter(word => word.length > 0).length

    const { error } = await supabase
      .from('chapters')
      .update({
        content: editedContent,
        actual_word_count: wordCount,
        status: 'complete',
        ai_notes: {
          userChanges: changes,
          adjustmentTimestamp: new Date().toISOString()
        },
        updated_at: new Date().toISOString()
      })
      .eq('id', chapterId)

    if (error) {
      throw new Error('Failed to update chapter content')
    }

    // Also create a version history entry
    await supabase
      .from('chapter_versions')
      .insert({
        chapter_id: chapterId,
        content: editedContent,
        word_count: wordCount,
        version_type: 'user_edited',
        metadata: {
          userChanges: changes,
          source: 'chapter_generation_workflow'
        }
      })
  }, [state.generatedContent, supabase])

  const submitChanges = useCallback(async (changes: UserChanges): Promise<boolean> => {
    setState(prev => ({
      ...prev,
      isSubmittingChanges: true,
      error: null,
      currentStep: 'adjusting'
    }))

    try {
      // Submit changes for analysis and plan adjustment
      const response = await fetch('/api/agents/adjust-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          chapterId,
          userChanges: changes
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit changes')
      }

      // Update the chapter content with user edits
      await updateChapterContent(chapterId, changes)

      setState(prev => ({
        ...prev,
        isSubmittingChanges: false,
        isReviewing: false,
        currentStep: 'complete'
      }))

      return true

    } catch (error) {
      setState(prev => ({
        ...prev,
        isSubmittingChanges: false,
        error: error instanceof Error ? error.message : 'Failed to submit changes',
        currentStep: 'reviewing'
      }))
      return false
    }
  }, [projectId, chapterId, updateChapterContent])

  const approveGeneration = useCallback(async (): Promise<boolean> => {
    setState(prev => ({
      ...prev,
      isSubmittingChanges: true,
      error: null
    }))

    try {
      // Simply approve the generated content without changes
      if (state.generatedContent) {
        const wordCount = state.generatedContent.trim().split(/\s+/).filter(word => word.length > 0).length

        const { error } = await supabase
          .from('chapters')
          .update({
            content: state.generatedContent,
            actual_word_count: wordCount,
            status: 'complete',
            updated_at: new Date().toISOString()
          })
          .eq('id', chapterId)

        if (error) {
          throw new Error('Failed to save approved content')
        }
      }

      setState(prev => ({
        ...prev,
        isSubmittingChanges: false,
        isReviewing: false,
        currentStep: 'complete'
      }))

      return true

    } catch (error) {
      setState(prev => ({
        ...prev,
        isSubmittingChanges: false,
        error: error instanceof Error ? error.message : 'Failed to approve generation'
      }))
      return false
    }
  }, [chapterId, state.generatedContent, supabase])

  const resetState = useCallback(() => {
    setState({
      isGenerating: false,
      isReviewing: false,
      isSubmittingChanges: false,
      generatedContent: null,
      originalContent: null,
      error: null,
      currentStep: 'idle'
    })
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  return {
    state,
    generateChapter,
    startReview,
    submitChanges,
    approveGeneration,
    resetState,
    clearError
  }
}