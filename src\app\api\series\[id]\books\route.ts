import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const body = await request.json();
    const { projectId, order } = body;

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    // Verify series exists
    const { data: series, error: seriesError } = await supabase
      .from('book_series')
      .select('id, name')
      .eq('id', seriesId)
      .single();

    if (seriesError) {
      if (seriesError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Series not found' }, { status: 404 });
      }
      throw seriesError;
    }

    // Verify project exists and isn't already in a series
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name, series_id')
      .eq('id', projectId)
      .single();

    if (projectError) {
      if (projectError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 });
      }
      throw projectError;
    }

    if (project.series_id) {
      return NextResponse.json({ 
        error: 'Project is already part of a series' 
      }, { status: 400 });
    }

    // Determine order if not provided
    let bookOrder = order;
    if (!bookOrder) {
      const { data: existingBooks } = await supabase
        .from('projects')
        .select('series_order')
        .eq('series_id', seriesId)
        .order('series_order', { ascending: false })
        .limit(1);

      bookOrder = existingBooks && existingBooks.length > 0 
        ? ((existingBooks[0]?.series_order || 0) + 1)
        : 1;
    }

    // Add project to series
    const { data: updatedProject, error: updateError } = await supabase
      .from('projects')
      .update({
        series_id: seriesId,
        series_order: bookOrder
      })
      .eq('id', projectId)
      .select()
      .single();

    if (updateError) throw updateError;

    // Track analytics
    try {
      await supabase
        .from('selection_analytics')
        .insert({
          user_id: updatedProject.user_id,
          project_id: projectId,
          event_type: 'book_added_to_series',
          selection_data: {
            seriesId,
            seriesName: series.name,
            projectName: project.name,
            order: bookOrder
          }
        });
    } catch (analyticsError) {
      console.warn('Failed to track book addition analytics:', analyticsError);
    }

    return NextResponse.json({ 
      success: true,
      project: {
        id: updatedProject.id,
        name: updatedProject.name,
        seriesId: updatedProject.series_id,
        order: updatedProject.series_order
      },
      series: {
        id: series.id,
        name: series.name
      }
    });
  } catch (error) {
    console.error('Add book to series API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    // Verify project is in this series
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name, series_id, series_order')
      .eq('id', projectId)
      .single();

    if (projectError) {
      if (projectError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 });
      }
      throw projectError;
    }

    if (project.series_id !== seriesId) {
      return NextResponse.json({ 
        error: 'Project is not part of this series' 
      }, { status: 400 });
    }

    // Remove project from series
    const { data: updatedProject, error: updateError } = await supabase
      .from('projects')
      .update({
        series_id: null,
        series_order: null
      })
      .eq('id', projectId)
      .select()
      .single();

    if (updateError) throw updateError;

    // Reorder remaining books in series
    const { data: remainingBooks } = await supabase
      .from('projects')
      .select('id, series_order')
      .eq('series_id', seriesId)
      .order('series_order', { ascending: true });

    if (remainingBooks && remainingBooks.length > 0) {
      const reorderPromises = remainingBooks.map((book, index) => 
        supabase
          .from('projects')
          .update({ series_order: index + 1 })
          .eq('id', book.id)
      );

      await Promise.all(reorderPromises);
    }

    // Track analytics
    try {
      await supabase
        .from('selection_analytics')
        .insert({
          user_id: updatedProject.user_id,
          project_id: projectId,
          event_type: 'book_removed_from_series',
          selection_data: {
            seriesId,
            projectName: project.name,
            previousOrder: project.series_order
          }
        });
    } catch (analyticsError) {
      console.warn('Failed to track book removal analytics:', analyticsError);
    }

    return NextResponse.json({ 
      success: true,
      project: {
        id: updatedProject.id,
        name: updatedProject.name,
        removedFromSeries: seriesId
      }
    });
  } catch (error) {
    console.error('Remove book from series API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}