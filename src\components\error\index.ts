// Error Boundary Components
export { ErrorBoundary, withErrorBoundary } from './error-boundary'
export { APIErrorBoundary, withAPIErrorBoundary, useAPIErrorHandler } from './api-error-boundary'

// Error Reporting Components
export {
  ErrorReportingDialog,
  QuickErrorReport,
  ErrorStatusIndicator,
  GlobalErrorProvider,
  useGlobalErrors
} from './error-reporting'

// Types
export type { ErrorReport } from '@/hooks/use-error-handling'