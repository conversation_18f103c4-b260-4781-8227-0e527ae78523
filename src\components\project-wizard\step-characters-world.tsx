'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useWizardStore } from '@/stores/wizard-store'
import { CHARACTER_COMPLEXITIES } from '@/lib/types/project'

export function StepCharactersWorld() {
  const { selections, updateSelections } = useWizardStore()
  
  const protagonistTypes = [
    'The Hero',
    'The Antihero', 
    'The Reluctant Hero',
    'The Tragic Hero',
    'The Everyman',
    'The Mentor',
    'The Innocent'
  ]
  
  const antagonistTypes = [
    'The Villain',
    'The Shadow',
    'The Rival',
    'The Skeptic',
    'The Threshold Guardian',
    'The Shapeshifter'
  ]
  
  const characterArcTypes = [
    'Positive Change',
    'Negative Change', 
    'Flat Arc',
    'Corruption Arc',
    'Redemption Arc'
  ]
  
  const timePeriods = [
    'Contemporary',
    'Historical - Ancient',
    'Historical - Medieval',
    'Historical - Renaissance',
    'Historical - Industrial',
    'Historical - Modern (20th Century)',
    'Near Future',
    'Far Future',
    'Alternate History',
    'Timeless'
  ]
  
  const geographicSettings = [
    'Urban',
    'Rural',
    'Suburban',
    'Wilderness',
    'Island',
    'Underground',
    'Space',
    'Alternate Dimension',
    'Floating City',
    'Desert',
    'Mountains',
    'Ocean'
  ]
  
  const worldTypes = [
    'Real World',
    'Alternate Reality',
    'Fantasy World',
    'Sci-Fi Universe',
    'Post-Apocalyptic',
    'Steampunk',
    'Cyberpunk',
    'Magical Realism'
  ]
  
  const magicTechLevels = [
    'No Magic/Current Tech',
    'Low Magic/Near Future Tech',
    'High Magic/Advanced Tech',
    'Magitech Fusion'
  ]
  
  const handleArrayToggle = (array: string[], item: string, key: keyof typeof selections) => {
    const currentArray = array || []
    const newArray = currentArray.includes(item)
      ? currentArray.filter(i => i !== item)
      : [...currentArray, item]
    updateSelections({ [key]: newArray })
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Character Development</CardTitle>
          <CardDescription>
            Define the types of characters and their development patterns for your story.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Character Complexity</Label>
            <Select 
              value={selections.characterComplexity} 
              onValueChange={(value) => updateSelections({ characterComplexity: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select character complexity" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(CHARACTER_COMPLEXITIES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Protagonist Types (select all that apply)</Label>
            <div className="grid grid-cols-2 gap-2">
              {protagonistTypes.map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`protagonist-${type}`}
                    checked={selections.protagonistTypes?.includes(type) || false}
                    onCheckedChange={() => 
                      handleArrayToggle(selections.protagonistTypes || [], type, 'protagonistTypes')
                    }
                  />
                  <Label htmlFor={`protagonist-${type}`} className="text-sm font-normal">
                    {type}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Antagonist Types (select all that apply)</Label>
            <div className="grid grid-cols-2 gap-2">
              {antagonistTypes.map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`antagonist-${type}`}
                    checked={selections.antagonistTypes?.includes(type) || false}
                    onCheckedChange={() => 
                      handleArrayToggle(selections.antagonistTypes || [], type, 'antagonistTypes')
                    }
                  />
                  <Label htmlFor={`antagonist-${type}`} className="text-sm font-normal">
                    {type}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Character Arc Types (select all that apply)</Label>
            <div className="grid grid-cols-2 gap-2">
              {characterArcTypes.map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`arc-${type}`}
                    checked={selections.characterArcTypes?.includes(type) || false}
                    onCheckedChange={() => 
                      handleArrayToggle(selections.characterArcTypes || [], type, 'characterArcTypes')
                    }
                  />
                  <Label htmlFor={`arc-${type}`} className="text-sm font-normal">
                    {type}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customCharacterConcepts">Custom Character Concepts (Optional)</Label>
            <Textarea
              id="customCharacterConcepts"
              placeholder="Describe any specific character ideas, unique archetypes, or special character requirements..."
              value={selections.customCharacterConcepts || ''}
              onChange={(e) => updateSelections({ customCharacterConcepts: e.target.value })}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>World Building & Setting</CardTitle>
          <CardDescription>
            Configure the world where your story takes place.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Time Period</Label>
            <Select 
              value={selections.timePeriod} 
              onValueChange={(value) => updateSelections({ timePeriod: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select time period" />
              </SelectTrigger>
              <SelectContent>
                {timePeriods.map((period) => (
                  <SelectItem key={period} value={period}>{period}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Geographic Setting</Label>
            <Select 
              value={selections.geographicSetting} 
              onValueChange={(value) => updateSelections({ geographicSetting: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select geographic setting" />
              </SelectTrigger>
              <SelectContent>
                {geographicSettings.map((setting) => (
                  <SelectItem key={setting} value={setting}>{setting}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>World Type</Label>
            <Select 
              value={selections.worldType} 
              onValueChange={(value) => updateSelections({ worldType: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select world type" />
              </SelectTrigger>
              <SelectContent>
                {worldTypes.map((type) => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Magic/Technology Level</Label>
            <Select 
              value={selections.magicTechLevel} 
              onValueChange={(value) => updateSelections({ magicTechLevel: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select magic/technology level" />
              </SelectTrigger>
              <SelectContent>
                {magicTechLevels.map((level) => (
                  <SelectItem key={level} value={level}>{level}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customSettingDescription">Custom Setting Description (Optional)</Label>
            <Textarea
              id="customSettingDescription"
              placeholder="Describe any unique world-building elements, special locations, or setting requirements..."
              value={selections.customSettingDescription || ''}
              onChange={(e) => updateSelections({ customSettingDescription: e.target.value })}
              rows={4}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}