import { NextResponse } from 'next/server'
import { adminOperations } from '@/lib/supabase/admin'
import { authenticateAdmin } from '@/lib/auth'
import { config } from '@/lib/config'

/**
 * Test admin operations with service role key
 * This endpoint tests that the service role key is working properly
 */
export async function GET() {
  try {
    // Authenticate admin user
    const adminAuth = await authenticateAdmin()
    if (!adminAuth.success) {
      return adminAuth.response!
    }
    // Test admin connection
    const connectionTest = await adminOperations.testConnection()
    
    if (!connectionTest.success) {
      return NextResponse.json({
        success: false,
        error: 'Admin connection failed',
        details: connectionTest.error,
        message: connectionTest.details
      }, { status: 500 })
    }

    // Test getting all users (admin operation)
    const { data: usersData, error: usersError } = await adminOperations.getAllUsers()
    
    // Test getting all projects (bypassing RLS)
    const { data: projectsData, error: projectsError } = await adminOperations.getAllProjects()

    return NextResponse.json({
      success: true,
      message: 'Service role key is working properly',
      tests: {
        connection: connectionTest,
        adminAuth: {
          success: !usersError,
          userCount: usersData?.users?.length || 0,
          error: usersError?.message || null
        },
        databaseBypass: {
          success: !projectsError,
          projectCount: projectsData?.length || 0,
          error: projectsError?.message || null
        }
      },
      environment: {
        hasServiceRoleKey: !!config.supabase.serviceRoleKey,
        hasSupabaseUrl: !!config.supabase.url,
        serviceRoleKeyLength: config.supabase.serviceRoleKey?.length || 0
      }
    })

  } catch (error) {
    console.error('Admin test failed:', error)
    return NextResponse.json({
      success: false,
      error: 'Admin test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
