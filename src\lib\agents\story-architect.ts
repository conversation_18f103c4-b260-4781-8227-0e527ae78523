import { openai, defaultConfig } from '../openai'
import { 
  StoryArchitectInput, 
  StoryStructure, 
  AgentResponse 
} from '../types/agents'

export class StoryArchitectAgent {
  private config = {
    ...defaultConfig,
    temperature: 0.8,
    maxTokens: 6000
  }

  async generateStoryStructure(input: StoryArchitectInput): Promise<AgentResponse<StoryStructure>> {
    const startTime = Date.now()
    
    try {
      const systemPrompt = this.buildSystemPrompt(input)
      const userPrompt = this.buildUserPrompt(input)

      const response = await openai.chat.completions.create({
        model: this.config.model,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        response_format: { type: 'json_object' }
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response content received')
      }

      const storyStructure = JSON.parse(content) as StoryStructure
      
      return {
        success: true,
        data: storyStructure,
        executionTime: Date.now() - startTime,
        tokensUsed: response.usage?.total_tokens
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      }
    }
  }

  private buildSystemPrompt(input: StoryArchitectInput): string {
    return `You are an expert Story Architect AI agent specializing in creating comprehensive story structures for novels. Your task is to analyze the provided project selections and story concept to generate a detailed, coherent story structure.

GUIDELINES:
- Create a structure that aligns with the selected genre (${input.projectSelections.primaryGenre})
- Follow the chosen narrative structure (${input.projectSelections.structureType})
- Match the selected tone and style (${input.projectSelections.toneOptions?.join(', ')})
- Ensure the story supports the target word count (${input.targetWordCount} words)
- Plan for ${input.targetChapters} chapters
- Include compelling conflicts and character arcs
- Maintain genre conventions while adding unique elements

STRUCTURE REQUIREMENTS:
- Create 3-5 acts based on the selected structure type
- Define major conflicts (internal, external, relational)
- Establish clear themes that align with project selections
- Plan key story events and their placement
- Consider pacing based on preferences (${input.projectSelections.pacingPreference})

RESPONSE FORMAT:
Return a valid JSON object with the StoryStructure interface, including:
- title: Creative title based on the concept
- genre: Primary genre
- themes: Array of 2-4 major themes
- acts: Array of act objects with number, title, description, keyEvents, and estimated wordCount
- conflicts: Array of conflict objects with type and description
- timeline: Array of major events with chapter placement

Ensure all content is original, engaging, and appropriate for the target audience (${input.projectSelections.targetAudience}).`
  }

  private buildUserPrompt(input: StoryArchitectInput): string {
    return `Please create a comprehensive story structure for this novel project:

STORY CONCEPT:
${input.storyPrompt}

PROJECT DETAILS:
- Genre: ${input.projectSelections.primaryGenre}${input.projectSelections.subgenre ? ` (${input.projectSelections.subgenre})` : ''}
- Target Word Count: ${input.targetWordCount.toLocaleString()} words
- Target Chapters: ${input.targetChapters}
- Structure Type: ${input.projectSelections.structureType}
- Narrative Voice: ${input.projectSelections.narrativeVoice}
- Tense: ${input.projectSelections.tense}
- Tone: ${input.projectSelections.toneOptions?.join(', ') || 'Not specified'}
- Target Audience: ${input.projectSelections.targetAudience}
- Pacing: ${input.projectSelections.pacingPreference}

ADDITIONAL CONTEXT:
${input.projectSelections.customGenre ? `Custom Genre Elements: ${input.projectSelections.customGenre}` : ''}
${input.projectSelections.customStyleDescription ? `Style Notes: ${input.projectSelections.customStyleDescription}` : ''}
${input.projectSelections.customStructureNotes ? `Structure Notes: ${input.projectSelections.customStructureNotes}` : ''}

Generate a story structure that transforms this concept into a compelling, well-structured novel that meets all the specified requirements.`
  }
}