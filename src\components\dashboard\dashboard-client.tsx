'use client'

import { WelcomeTour } from '@/components/onboarding/welcome-tour'

interface DashboardClientProps {
  hasProjects: boolean
  isNewUser: boolean
}

export function DashboardClient({ hasProjects, isNewUser }: DashboardClientProps) {
  return (
    <WelcomeTour
      isNewUser={isNewUser}
      hasProjects={hasProjects}
      onComplete={() => {
        // Tour completion is handled internally by WelcomeTour component
      }}
    />
  )
}