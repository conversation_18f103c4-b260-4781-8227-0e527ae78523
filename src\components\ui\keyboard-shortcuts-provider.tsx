'use client'

import { useEffect, useState } from 'react'
import { KeyboardShortcutsModal } from './keyboard-shortcuts-modal'
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts'

export function KeyboardShortcutsProvider({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false)
  
  // Initialize keyboard shortcuts
  useKeyboardShortcuts()

  useEffect(() => {
    const handleShowShortcuts = () => setOpen(true)
    window.addEventListener('show-keyboard-shortcuts', handleShowShortcuts)
    return () => window.removeEventListener('show-keyboard-shortcuts', handleShowShortcuts)
  }, [])

  return (
    <>
      {children}
      <KeyboardShortcutsModal open={open} onOpenChange={setOpen} />
    </>
  )
}