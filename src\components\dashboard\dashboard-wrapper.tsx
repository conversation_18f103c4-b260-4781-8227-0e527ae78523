'use client'

import React from 'react'
import { APIErrorBoundary } from '@/components/error/api-error-boundary'
import { DashboardSkeleton, ProjectCardSkeleton } from '@/components/loading/skeleton-loader'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  AlertTriangle, 
  RefreshCw, 
  Plus, 
  BookOpen, 
  TrendingUp,
  Clock,
  Home
} from 'lucide-react'
import Link from 'next/link'
import { config } from '@/lib/config'

interface DashboardWrapperProps {
  children: React.ReactNode
}

interface DashboardSectionWrapperProps {
  children: React.ReactNode
  title: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  actions?: React.ReactNode
  loadingRows?: number
  collapsible?: boolean
}

interface DashboardError {
  status?: number
  message?: string
}

function DashboardErrorFallback({ 
  error, 
  retry 
}: { 
  error: DashboardError | Error
  retry: () => void
}) {
  const isAuthError = ('status' in error && error.status === 401) || (error && 'message' in error && error.message?.includes('auth'))
  const isNetworkError = ('status' in error && error.status === 0) || (error && 'message' in error && error.message?.includes('fetch'))

  if (isAuthError) {
    return (
      <div className="min-h-screen bg-background">
        <header className="border-b">
          <div className="container flex h-16 items-center justify-between">
            <h1 className="text-2xl font-bold">BookScribe</h1>
            <Button asChild>
              <Link href="/login">Sign In</Link>
            </Button>
          </div>
        </header>
        
        <main className="container py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-amber-100 rounded-full w-fit">
                <AlertTriangle className="h-6 w-6 text-amber-600" />
              </div>
              <CardTitle>Sign In Required</CardTitle>
              <CardDescription>
                Please sign in to access your dashboard.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/login">Sign In to Continue</Link>
              </Button>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <Button onClick={retry} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </header>
      
      <main className="container py-8">
        <Card className="max-w-lg mx-auto">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle>
              {isNetworkError ? 'Connection Error' : 'Dashboard Error'}
            </CardTitle>
            <CardDescription>
              {isNetworkError 
                ? 'Unable to connect to BookScribe. Please check your internet connection.'
                : 'Failed to load your dashboard. This might be a temporary issue.'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && 'message' in error && error.message && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error Details</AlertTitle>
                <AlertDescription>{error.message}</AlertDescription>
              </Alert>
            )}
            
            <div className="flex gap-2">
              <Button onClick={retry} variant="outline" className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button 
                onClick={() => window.location.href = '/'}
                className="flex-1"
              >
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

function DashboardLoadingFallback() {
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <div className="h-8 w-32 bg-muted animate-pulse rounded" />
          <div className="h-10 w-24 bg-muted animate-pulse rounded" />
        </div>
      </header>
      
      <main className="container py-8">
        <DashboardSkeleton />
      </main>
    </div>
  )
}


export function DashboardWrapper({
  children
}: DashboardWrapperProps) {

  const handleRetry = React.useCallback(() => {
    window.location.reload()
  }, [])

  return (
    <APIErrorBoundary
      fallbackComponent={({ error, retry }) => (
        <DashboardErrorFallback 
          error={error} 
          retry={retry} 
        />
      )}
      onRetry={handleRetry}
      retryable={true}
      showErrorDetails={config.isDevelopment}
    >
      <React.Suspense fallback={<DashboardLoadingFallback />}>
        {children}
      </React.Suspense>
    </APIErrorBoundary>
  )
}

export function DashboardSectionWrapper({
  children,
  title,
  description,
  icon: Icon,
  actions,
  loadingRows = 3,
  collapsible = false
}: DashboardSectionWrapperProps) {
  const [isCollapsed, setIsCollapsed] = React.useState(false)

  return (
    <APIErrorBoundary
      fallbackComponent={({ error, retry }) => (
        <Card className="border-destructive">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {Icon && <Icon className="h-5 w-5 text-destructive" />}
                <div>
                  <CardTitle className="text-destructive">{title} - Error</CardTitle>
                  <CardDescription>
                    Failed to load this section: {error && 'message' in error ? error.message : 'Unknown error'}
                  </CardDescription>
                </div>
              </div>
              <Button onClick={retry} variant="outline" size="sm">
                <RefreshCw className="h-3 w-3 mr-2" />
                Retry
              </Button>
            </div>
          </CardHeader>
        </Card>
      )}
      retryable={true}
    >
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {Icon && <Icon className="h-5 w-5" />}
              <div>
                <CardTitle className="flex items-center gap-2">
                  {title}
                  {collapsible && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsCollapsed(!isCollapsed)}
                    >
                      {isCollapsed ? '+' : '−'}
                    </Button>
                  )}
                </CardTitle>
                {description && <CardDescription>{description}</CardDescription>}
              </div>
            </div>
            {actions && <div className="flex gap-2">{actions}</div>}
          </div>
        </CardHeader>
        
        {!isCollapsed && (
          <CardContent>
            <React.Suspense 
              fallback={
                <div className="space-y-3">
                  {Array.from({ length: loadingRows }).map((_, i) => (
                    <ProjectCardSkeleton key={i} />
                  ))}
                </div>
              }
            >
              {children}
            </React.Suspense>
          </CardContent>
        )}
      </Card>
    </APIErrorBoundary>
  )
}

// Project Grid Wrapper
export function ProjectGridWrapper({
  children
}: {
  children: React.ReactNode
  hasProjects?: boolean
}) {
  return (
    <DashboardSectionWrapper
      title="Your Projects"
      description="Continue working on your stories"
      icon={BookOpen}
      loadingRows={6}
      actions={
        <Button asChild size="sm">
          <Link href="/projects/new">
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Link>
        </Button>
      }
    >
      {children}
    </DashboardSectionWrapper>
  )
}

// Stats Overview Wrapper
export function StatsOverviewWrapper({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <DashboardSectionWrapper
      title="Writing Statistics"
      description="Your progress at a glance"
      icon={TrendingUp}
      loadingRows={1}
    >
      {children}
    </DashboardSectionWrapper>
  )
}

// Recent Activity Wrapper
export function RecentActivityWrapper({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <DashboardSectionWrapper
      title="Recent Activity"
      description="Your latest writing sessions"
      icon={Clock}
      loadingRows={5}
      collapsible={true}
    >
      {children}
    </DashboardSectionWrapper>
  )
}

// Template Browser Wrapper
export function TemplateBrowserWrapper({
  children
}: {
  children: React.ReactNode
}) {

  return (
    <APIErrorBoundary
      fallbackComponent={({ error, retry }) => (
        <Card className="border-destructive">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
              <BookOpen className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle>Template Error</CardTitle>
            <CardDescription>
              Failed to load project templates.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error && 'message' in error && error.message ? error.message : 'Unknown error occurred'}</AlertDescription>
            </Alert>
            
            <div className="flex gap-2">
              <Button onClick={retry} variant="outline" className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button asChild className="flex-1">
                <Link href="/projects/new">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Blank Project
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      retryable={true}
    >
      <React.Suspense fallback={
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <ProjectCardSkeleton key={i} />
          ))}
        </div>
      }>
        {children}
      </React.Suspense>
    </APIErrorBoundary>
  )
}