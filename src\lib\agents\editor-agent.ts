import { BaseAgent } from './base-agent';
import { EditorialReview, ChapterContent } from './types';

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class EditorAgent extends BaseAgent {
  // Override base execute method
  async execute(): Promise<EditorialReview> {
    throw new Error('Editor Agent requires chapter content. Use executeReview instead.');
  }

  async executeReview(chapterContent: ChapterContent): Promise<EditorialReview> {
    if (!this.context.storyStructure || !this.context.characters) {
      throw new Error('Editor Agent requires complete story context');
    }

    const systemPrompt = this.createSystemPrompt(
      'Editor Agent',
      `Your role is to provide comprehensive editorial review and quality assurance for chapter content.

RESPONSIBILITIES:
1. Review chapters for consistency with story bible and character profiles
2. Check plot continuity and character development alignment
3. Assess writing quality across multiple dimensions
4. Identify potential improvements and corrections
5. Ensure adherence to project settings and style guidelines

REVIEW CRITERIA:
- Character voice consistency
- Plot continuity and logic
- World-building consistency
- Timeline accuracy
- Relationship dynamics
- Prose quality and readability
- Pacing and tension
- Theme development

OUTPUT REQUIREMENTS:
- Provide detailed consistency checks
- Score various quality aspects
- Suggest specific improvements
- Identify any critical issues requiring revision`
    );

    const storyBibleContext = this.buildStoryBibleContext();

    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      {
        role: 'user',
        content: `Review Chapter ${chapterContent.chapterNumber}: "${chapterContent.title}"

CHAPTER CONTENT:
${chapterContent.content}

STORY BIBLE CONTEXT:
${storyBibleContext}

Please provide a comprehensive editorial review that includes:
1. Consistency checks against established story elements
2. Quality assessment across multiple dimensions
3. Specific suggestions for improvement
4. Identification of any plot holes or character inconsistencies
5. Overall scoring and recommendations`
      }
    ];

    const tools = [
      {
        type: 'function' as const,
        function: {
          name: 'create_editorial_review',
          description: 'Create comprehensive editorial review',
          parameters: {
            type: 'object',
            properties: {
              chapterNumber: { type: 'number' },
              overallScore: { type: 'number', minimum: 0, maximum: 100 },
              consistency: {
                type: 'object',
                properties: {
                  characterVoices: { type: 'boolean' },
                  plotContinuity: { type: 'boolean' },
                  worldBuilding: { type: 'boolean' },
                  timeline: { type: 'boolean' },
                  relationships: { type: 'boolean' },
                  issues: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        type: { type: 'string' },
                        description: { type: 'string' },
                        severity: { type: 'string' },
                        suggestion: { type: 'string' }
                      }
                    }
                  }
                }
              },
              quality: {
                type: 'object',
                properties: {
                  prose: { type: 'number', minimum: 0, maximum: 100 },
                  dialogue: { type: 'number', minimum: 0, maximum: 100 },
                  pacing: { type: 'number', minimum: 0, maximum: 100 },
                  description: { type: 'number', minimum: 0, maximum: 100 },
                  characterization: { type: 'number', minimum: 0, maximum: 100 },
                  overallEngagement: { type: 'number', minimum: 0, maximum: 100 }
                }
              },
              suggestions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    type: { type: 'string' },
                    description: { type: 'string' },
                    location: { type: 'string' },
                    priority: { type: 'string' }
                  }
                }
              },
              revisions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    original: { type: 'string' },
                    revised: { type: 'string' },
                    reason: { type: 'string' },
                    approved: { type: 'boolean' }
                  }
                }
              }
            },
            required: ['chapterNumber', 'overallScore', 'consistency', 'quality', 'suggestions']
          }
        }
      }
    ];

    const completion = await this.createCompletion(messages, tools, 'gpt-4');
    
    if (completion.choices[0]?.message.tool_calls) {
      const toolCall = completion.choices[0].message.tool_calls[0];
      if (toolCall) {
        const result = JSON.parse(toolCall.function.arguments);
        return result;
      }
    }
    
    throw new Error('Editor Agent failed to generate editorial review');
  }

  private buildStoryBibleContext(): string {
    const characters = this.context.characters!;
    const structure = this.context.storyStructure!;
    
    return `
ESTABLISHED CHARACTERS:
${[...characters.protagonists, ...characters.antagonists, ...characters.supporting].map(char => `
${char.name}:
- Role: ${char.role}
- Voice: ${char.voice.speakingStyle}
- Key Traits: ${char.personality.traits.join(', ')}
- Motivation: ${char.motivation}
- Arc Type: ${char.arc.type}
`).join('\n')}

STORY STRUCTURE:
- Genre: ${structure.genre}
- Themes: ${structure.themes.join(', ')}
- World: ${structure.worldBuilding.setting.culture}

ESTABLISHED RULES:
${structure.worldBuilding.rules.join('\n- ')}

CHARACTER RELATIONSHIPS:
${characters.relationships.map(rel => `
${rel.character1Id} & ${rel.character2Id}: ${rel.type} - ${rel.description}
`).join('\n')}
    `.trim();
  }
}