import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { getMemoryManager } from '@/lib/memory/memory-instances';
import { authenticateUser } from '@/lib/auth';
import { db } from '@/lib/db/client';

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const { user } = authResult;
    
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Verify user owns the project
    const project = await db.projects.getById(projectId);
    if (!project || project.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 403 }
      );
    }

    const memoryManager = getMemoryManager(projectId);
    const stats = memoryManager.getMemoryStats();
    
    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Error getting memory stats:', error);
    return NextResponse.json(
      { error: 'Failed to get memory stats' },
      { status: 500 }
    );
  }
}

