import { BaseService } from './base-service';
import { ServiceResponse } from './types';
import { createClient } from '@/lib/supabase/server';
import type { RealtimeChannel } from '@supabase/supabase-js';

interface CollaborationParticipant {
  userId: string;
  role: 'owner' | 'editor' | 'viewer' | 'commenter';
  status: 'online' | 'offline';
  cursor?: { line: number; column: number };
  lastSeen?: number;
}


interface CollaborationChange {
  id: string;
  sessionId: string;
  userId: string;
  type: 'insert' | 'delete' | 'format' | 'comment';
  position: { line: number; column: number };
  content?: string;
  length?: number;
  data?: Record<string, unknown>;
  timestamp: number;
  version: number;
}

/**
 * Serverless-compatible CollaborationHub using Supabase
 * 
 * This implementation uses:
 * 1. Supabase Database for persistent storage
 * 2. Supabase Realtime for WebSocket-like functionality
 * 3. Supabase Presence for online user tracking
 * 4. Database-backed locking mechanism
 * 
 * No in-memory state, works perfectly with serverless!
 */
export class CollaborationHubServerless extends BaseService {
  constructor() {
    super({
      name: 'collaboration-hub',
      version: '2.0.0',
      status: 'inactive',
      endpoints: ['/api/collaboration/sessions', '/api/collaboration/changes'],
      dependencies: [],
      healthCheck: '/api/collaboration/health'
    });
  }

  async initialize(): Promise<void> {
    // No persistent state needed - everything is in Supabase
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    try {
      const supabase = await createClient();
      
      // Check if we can query the collaboration tables
      const { error } = await supabase
        .from('collaboration_sessions')
        .select('count')
        .limit(1);

      if (error) {
        return this.createResponse(false, {
          status: 'Database connection failed',
          uptime: 0
        }, 'Database connection failed');
      }

      return this.createResponse(true, {
        status: 'Collaboration service operational (serverless mode)',
        uptime: Date.now(),
      });
    } catch (_error) {
      return this.createResponse(false, {
        status: 'Health check failed',
        uptime: 0
      }, 'Health check failed');
    }
  }

  async shutdown(): Promise<void> {
    // No cleanup needed in serverless
    this.setStatus('inactive');
  }

  async createSession(projectId: string, ownerId: string): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const supabase = await createClient();
      const sessionId = `collab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Create session in database
      const { error: sessionError } = await supabase
        .from('collaboration_sessions')
        .insert({
          id: sessionId,
          project_id: projectId,
          owner_id: ownerId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          document_content: '',
          document_version: 1
        });

      if (sessionError) throw sessionError;

      // Add owner as participant
      const { error: participantError } = await supabase
        .from('collaboration_participants')
        .insert({
          session_id: sessionId,
          user_id: ownerId,
          role: 'owner',
          status: 'online',
          joined_at: new Date().toISOString()
        });

      if (participantError) throw participantError;

      return sessionId;
    });
  }

  async joinSession(sessionId: string, userId: string, role: 'editor' | 'viewer' | 'commenter' = 'viewer'): Promise<ServiceResponse<{
    sessionId: string;
    participants: CollaborationParticipant[];
    documentContent: string;
    documentVersion: number;
  }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createClient();
      
      // Check if session exists
      const { data: session, error: sessionError } = await supabase
        .from('collaboration_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (sessionError || !session) {
        throw new Error('Session not found');
      }

      // Upsert participant
      const { error: participantError } = await supabase
        .from('collaboration_participants')
        .upsert({
          session_id: sessionId,
          user_id: userId,
          role,
          status: 'online',
          joined_at: new Date().toISOString(),
          last_seen: new Date().toISOString()
        }, {
          onConflict: 'session_id,user_id'
        });

      if (participantError) throw participantError;

      // Get all participants
      const { data: participants, error: participantsError } = await supabase
        .from('collaboration_participants')
        .select('*')
        .eq('session_id', sessionId);

      if (participantsError) throw participantsError;

      return {
        sessionId,
        participants: participants.map(p => ({
          userId: p.user_id,
          role: p.role,
          status: p.status,
          cursor: p.cursor_position,
          lastSeen: p.last_seen
        })),
        documentContent: session.document_content,
        documentVersion: session.document_version
      };
    });
  }

  async leaveSession(sessionId: string, userId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createClient();
      
      // Update participant status
      const { error: updateError } = await supabase
        .from('collaboration_participants')
        .update({ 
          status: 'offline',
          last_seen: new Date().toISOString()
        })
        .eq('session_id', sessionId)
        .eq('user_id', userId);

      if (updateError) throw updateError;

      // Release any locks held by this user
      const { error: unlockError } = await supabase
        .from('collaboration_locks')
        .delete()
        .eq('session_id', sessionId)
        .eq('user_id', userId);

      if (unlockError) throw unlockError;

      return true;
    });
  }

  async applyChange(
    sessionId: string, 
    userId: string, 
    change: {
      type: 'insert' | 'delete' | 'format' | 'comment';
      position: { line: number; column: number };
      content?: string;
      length?: number;
      data?: Record<string, unknown>;
    }
  ): Promise<ServiceResponse<{ version: number; changeId: string }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createClient();
      
      // Check permissions
      const { data: participant, error: participantError } = await supabase
        .from('collaboration_participants')
        .select('role')
        .eq('session_id', sessionId)
        .eq('user_id', userId)
        .single();

      if (participantError || !participant) {
        throw new Error('User not in session');
      }

      if (!this.hasEditPermission(participant.role, change.type)) {
        throw new Error('Insufficient permissions');
      }

      // Start a transaction-like operation
      const changeId = `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Get current document state
      const { data: session, error: sessionError } = await supabase
        .from('collaboration_sessions')
        .select('document_content, document_version')
        .eq('id', sessionId)
        .single();

      if (sessionError || !session) throw new Error('Session not found');

      // Apply change to content
      let newContent = session.document_content;
      if (change.type === 'insert' && change.content) {
        newContent = this.insertContent(newContent, change.position, change.content);
      } else if (change.type === 'delete' && change.length) {
        newContent = this.deleteContent(newContent, change.position, change.length);
      }

      const newVersion = session.document_version + 1;

      // Update document
      const { error: updateError } = await supabase
        .from('collaboration_sessions')
        .update({
          document_content: newContent,
          document_version: newVersion,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)
        .eq('document_version', session.document_version); // Optimistic locking

      if (updateError) {
        throw new Error('Concurrent modification detected. Please refresh.');
      }

      // Record the change
      const { error: changeError } = await supabase
        .from('collaboration_changes')
        .insert({
          id: changeId,
          session_id: sessionId,
          user_id: userId,
          type: change.type,
          position: change.position,
          content: change.content,
          length: change.length,
          data: change.data,
          version: newVersion,
          created_at: new Date().toISOString()
        });

      if (changeError) throw changeError;

      return { version: newVersion, changeId };
    });
  }

  async updateCursor(sessionId: string, userId: string, position: { line: number; column: number }): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('collaboration_participants')
        .update({ 
          cursor_position: position,
          last_seen: new Date().toISOString()
        })
        .eq('session_id', sessionId)
        .eq('user_id', userId);

      if (error) throw error;

      return true;
    });
  }

  async lockSection(sessionId: string, userId: string, section: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createClient();
      
      // Check if section is already locked
      const { data: existingLock, error: checkError } = await supabase
        .from('collaboration_locks')
        .select('*')
        .eq('session_id', sessionId)
        .eq('section', section)
        .single();

      if (checkError && checkError.code !== 'PGRST116') throw checkError; // PGRST116 = not found

      if (existingLock) {
        // Check if lock is expired (5 minutes)
        const lockAge = Date.now() - new Date(existingLock.created_at).getTime();
        if (lockAge < 300000 && existingLock.user_id !== userId) {
          throw new Error('Section is locked by another user');
        }
      }

      // Create or update lock
      const { error } = await supabase
        .from('collaboration_locks')
        .upsert({
          session_id: sessionId,
          user_id: userId,
          section,
          created_at: new Date().toISOString()
        }, {
          onConflict: 'session_id,section'
        });

      if (error) throw error;

      return true;
    });
  }

  async unlockSection(sessionId: string, userId: string, section: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('collaboration_locks')
        .delete()
        .eq('session_id', sessionId)
        .eq('user_id', userId)
        .eq('section', section);

      if (error) throw error;

      return true;
    });
  }

  async getSessionHistory(sessionId: string, limit = 100): Promise<ServiceResponse<CollaborationChange[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createClient();
      
      const { data, error } = await supabase
        .from('collaboration_changes')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data.map(change => ({
        id: change.id,
        sessionId: change.session_id,
        userId: change.user_id,
        type: change.type,
        position: change.position,
        content: change.content,
        length: change.length,
        data: change.data,
        timestamp: new Date(change.created_at).getTime(),
        version: change.version
      }));
    });
  }

  /**
   * Subscribe to real-time updates for a session
   * This should be called from client-side code
   */
  subscribeToSession(_sessionId: string, _handlers: {
    onParticipantJoined?: (participant: CollaborationParticipant) => void;
    onParticipantLeft?: (userId: string) => void;
    onCursorMoved?: (userId: string, position: { line: number; column: number }) => void;
    onDocumentChanged?: (change: CollaborationChange) => void;
    onSectionLocked?: (userId: string, section: string) => void;
    onSectionUnlocked?: (userId: string, section: string) => void;
  }): RealtimeChannel {
    // This would be used client-side with the browser Supabase client
    // Server-side, we just return a mock channel
    throw new Error('subscribeToSession should be called from client-side code');
  }

  private hasEditPermission(role: string, changeType: string): boolean {
    switch (role) {
      case 'owner':
      case 'editor':
        return true;
      case 'commenter':
        return changeType === 'comment';
      case 'viewer':
        return false;
      default:
        return false;
    }
  }

  private insertContent(document: string, position: { line: number; column: number }, content: string): string {
    const lines = document.split('\n');
    
    if (position.line > lines.length) {
      while (lines.length < position.line) {
        lines.push('');
      }
    }

    const line = lines[position.line - 1] || '';
    const before = line.substring(0, position.column - 1);
    const after = line.substring(position.column - 1);
    
    lines[position.line - 1] = before + content + after;
    
    return lines.join('\n');
  }

  private deleteContent(document: string, position: { line: number; column: number }, length: number): string {
    const lines = document.split('\n');
    
    if (position.line > lines.length) return document;
    
    const line = lines[position.line - 1] || '';
    const before = line.substring(0, position.column - 1);
    const after = line.substring(position.column - 1 + length);
    
    lines[position.line - 1] = before + after;
    
    return lines.join('\n');
  }
}