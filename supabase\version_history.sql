-- Chapter Version History
CREATE TABLE chapter_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  version_number INTEGER NOT NULL,
  title TEXT,
  content TEXT,
  word_count INTEGER DEFAULT 0,
  outline TEXT,
  ai_notes JSONB,
  change_summary TEXT, -- Brief description of what changed
  is_auto_save BOOLEAN DEFAULT false, -- Distinguishes auto-saves from manual saves
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_chapter_versions_chapter_id ON chapter_versions(chapter_id);
CREATE INDEX idx_chapter_versions_user_id ON chapter_versions(user_id);
CREATE INDEX idx_chapter_versions_created_at ON chapter_versions(created_at);
CREATE INDEX idx_chapter_versions_version_number ON chapter_versions(chapter_id, version_number);

-- Enable RLS
ALTER TABLE chapter_versions ENABLE ROW LEVEL SECURITY;

-- R<PERSON> Policies
CREATE POLICY "Users can view own chapter versions" ON chapter_versions
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own chapter versions" ON chapter_versions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to automatically create version when chapter content changes
CREATE OR REPLACE FUNCTION create_chapter_version()
RETURNS TRIGGER AS $$
DECLARE
  next_version_number INTEGER;
BEGIN
  -- Only create version if content actually changed
  IF OLD.content IS DISTINCT FROM NEW.content OR OLD.title IS DISTINCT FROM NEW.title THEN
    -- Get next version number
    SELECT COALESCE(MAX(version_number), 0) + 1 
    INTO next_version_number
    FROM chapter_versions 
    WHERE chapter_id = NEW.id;
    
    -- Create version record
    INSERT INTO chapter_versions (
      chapter_id,
      user_id,
      version_number,
      title,
      content,
      word_count,
      outline,
      ai_notes,
      change_summary,
      is_auto_save
    ) VALUES (
      NEW.id,
      NEW.user_id,
      next_version_number,
      OLD.title,
      OLD.content,
      OLD.actual_word_count,
      OLD.outline,
      OLD.ai_notes,
      CASE 
        WHEN OLD.title IS DISTINCT FROM NEW.title AND OLD.content IS DISTINCT FROM NEW.content THEN 'Title and content updated'
        WHEN OLD.title IS DISTINCT FROM NEW.title THEN 'Title updated'
        WHEN OLD.content IS DISTINCT FROM NEW.content THEN 'Content updated'
        ELSE 'Chapter updated'
      END,
      false -- Manual save
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic versioning
CREATE TRIGGER chapter_version_trigger
  BEFORE UPDATE ON chapters
  FOR EACH ROW
  EXECUTE FUNCTION create_chapter_version();

-- Function to clean up old versions (keep last 50 per chapter)
CREATE OR REPLACE FUNCTION cleanup_old_versions()
RETURNS void AS $$
BEGIN
  DELETE FROM chapter_versions
  WHERE id IN (
    SELECT id FROM (
      SELECT id,
             ROW_NUMBER() OVER (
               PARTITION BY chapter_id 
               ORDER BY version_number DESC
             ) as rn
      FROM chapter_versions
    ) ranked
    WHERE rn > 50
  );
END;
$$ LANGUAGE plpgsql;

-- Project-level version snapshots for major milestones
CREATE TABLE project_snapshots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  snapshot_data JSONB NOT NULL, -- Complete project state
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_project_snapshots_project_id ON project_snapshots(project_id);
CREATE INDEX idx_project_snapshots_user_id ON project_snapshots(user_id);
CREATE INDEX idx_project_snapshots_created_at ON project_snapshots(created_at);

-- Enable RLS
ALTER TABLE project_snapshots ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own project snapshots" ON project_snapshots
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own project snapshots" ON project_snapshots
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own project snapshots" ON project_snapshots
  FOR DELETE USING (auth.uid() = user_id);