import { DynamicMemoryManager } from './dynamic-memory';

// Shared memory manager instances across routes
const memoryManagers = new Map<string, DynamicMemoryManager>();

export function getMemoryManager(projectId: string): DynamicMemoryManager {
  if (!memoryManagers.has(projectId)) {
    memoryManagers.set(projectId, new DynamicMemoryManager(projectId));
  }
  return memoryManagers.get(projectId)!;
}

export function removeMemoryManager(projectId: string): boolean {
  return memoryManagers.delete(projectId);
}

export function hasMemoryManager(projectId: string): boolean {
  return memoryManagers.has(projectId);
}