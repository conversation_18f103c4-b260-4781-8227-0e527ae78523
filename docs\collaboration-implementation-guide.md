# Collaboration Implementation Guide

## Quick Start

### 1. Run the Database Migration

```bash
supabase migration up
```

This creates the necessary tables for collaboration.

### 2. Use in Your Components

```typescript
import { useCollaboration } from '@/hooks/use-collaboration'

function MyEditor({ projectId, userId }) {
  const [sessionId, setSessionId] = useState(null)
  
  const {
    documentContent,
    participants,
    joinSession,
    applyChange,
    // ... other methods
  } = useCollaboration({
    sessionId,
    userId,
    onDocumentChange: (content) => {
      // Update your editor
    }
  })
  
  // Create session
  const startCollaboration = async () => {
    const res = await fetch('/api/collaboration/sessions', {
      method: 'POST',
      body: JSON.stringify({ projectId })
    })
    const { sessionId } = await res.json()
    setSessionId(sessionId)
  }
  
  // Join when session is ready
  useEffect(() => {
    if (sessionId) {
      joinSession('editor')
    }
  }, [sessionId])
}
```

## API Endpoints

All collaboration features are available through REST API:

### Create Session
```
POST /api/collaboration/sessions
Body: { projectId: string }
Response: { sessionId: string }
```

### Join Session
```
POST /api/collaboration/join
Body: { sessionId: string, role: 'editor' | 'viewer' | 'commenter' }
Response: { participants, documentContent, documentVersion }
```

### Apply Change
```
POST /api/collaboration/change
Body: { 
  sessionId: string,
  change: {
    type: 'insert' | 'delete' | 'format',
    position: { line: number, column: number },
    content?: string,
    length?: number
  }
}
Response: { version: number, changeId: string }
```

### Update Cursor
```
POST /api/collaboration/cursor
Body: { sessionId: string, position: { line: number, column: number } }
```

### Lock/Unlock Section
```
POST /api/collaboration/lock
POST /api/collaboration/unlock
Body: { sessionId: string, section: string }
```

## Real-time Features

The `useCollaboration` hook automatically handles:
- Real-time document synchronization
- User presence (online/offline status)
- Cursor position tracking
- Section locking
- Participant updates

## Permissions

- **Owner**: Full access
- **Editor**: Can edit content
- **Commenter**: Can only add comments
- **Viewer**: Read-only access

## Example Component

See `/src/components/collaboration/collaborative-editor-example.tsx` for a complete working example.

## Performance Tips

1. **Throttle cursor updates** - Already handled in the hook
2. **Batch small changes** - For better performance
3. **Use section locking** - Prevent conflicts in large documents
4. **Clean up sessions** - Leave session when component unmounts

## Troubleshooting

### "Service not available" error
- Make sure ServiceManager is initialized
- Check that database migration has run

### Real-time not working
- Verify Supabase Realtime is enabled
- Check browser console for WebSocket errors
- Ensure authentication is working

### Changes not syncing
- Check network tab for API errors
- Verify user has proper permissions
- Look for optimistic locking conflicts