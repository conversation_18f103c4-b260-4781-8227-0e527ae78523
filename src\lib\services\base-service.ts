import { ServiceConfig, ServiceResponse } from './types';

export abstract class BaseService {
  protected config: ServiceConfig;
  protected isInitialized: boolean = false;

  constructor(config: ServiceConfig) {
    this.config = config;
  }

  abstract initialize(): Promise<void>;
  abstract healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>>;
  abstract shutdown(): Promise<void>;

  protected createResponse<T>(
    success: boolean,
    data?: T,
    error?: string
  ): ServiceResponse<T> {
    return {
      success,
      data,
      error,
      timestamp: Date.now(),
      serviceId: this.config.name,
    };
  }

  protected async withErrorHandling<T>(
    operation: () => Promise<T>
  ): Promise<ServiceResponse<T>> {
    try {
      const result = await operation();
      return this.createResponse(true, result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`[${this.config.name}] Error:`, errorMessage);
      return this.createResponse<T>(false, undefined, errorMessage);
    }
  }

  getConfig(): ServiceConfig {
    return { ...this.config };
  }

  isActive(): boolean {
    return this.isInitialized && this.config.status === 'active';
  }

  setStatus(status: ServiceConfig['status']): void {
    this.config.status = status;
  }
}

export class ServiceRegistry {
  private static instance: ServiceRegistry;
  private services: Map<string, BaseService> = new Map();
  private dependencies: Map<string, string[]> = new Map();

  static getInstance(): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      ServiceRegistry.instance = new ServiceRegistry();
    }
    return ServiceRegistry.instance;
  }

  register(service: BaseService): void {
    const config = service.getConfig();
    this.services.set(config.name, service);
    this.dependencies.set(config.name, config.dependencies);
  }

  get(serviceName: string): BaseService | undefined {
    return this.services.get(serviceName);
  }

  async initializeAll(): Promise<void> {
    const sortedServices = this.topologicalSort();
    
    for (const serviceName of sortedServices) {
      const service = this.services.get(serviceName);
      if (service && !service.isActive()) {
        try {
          await service.initialize();
          console.log(`[ServiceRegistry] Initialized: ${serviceName}`);
        } catch (error) {
          console.error(`[ServiceRegistry] Failed to initialize ${serviceName}:`, error);
          service.setStatus('inactive');
        }
      }
    }
  }

  async shutdownAll(): Promise<void> {
    const services = Array.from(this.services.values());
    await Promise.all(services.map(service => service.shutdown()));
  }

  async healthCheckAll(): Promise<Record<string, Record<string, unknown>>> {
    const results: Record<string, Record<string, unknown>> = {};
    
    Array.from(this.services.entries()).forEach(async ([name, service]) => {
      try {
        const health = await service.healthCheck();
        results[name] = health as unknown as Record<string, unknown>;
      } catch (error) {
        results[name] = {
          success: false,
          error: error instanceof Error ? error.message : 'Health check failed',
          timestamp: Date.now(),
          serviceId: name,
        };
      }
    });
    
    return results;
  }

  getServicesByStatus(status: ServiceConfig['status']): BaseService[] {
    return Array.from(this.services.values()).filter(
      service => service.getConfig().status === status
    );
  }

  private topologicalSort(): string[] {
    const visited = new Set<string>();
    const result: string[] = [];

    const visit = (serviceName: string) => {
      if (visited.has(serviceName)) return;
      visited.add(serviceName);

      const deps = this.dependencies.get(serviceName) || [];
      for (const dep of deps) {
        if (this.services.has(dep)) {
          visit(dep);
        }
      }

      result.push(serviceName);
    };

    Array.from(this.services.keys()).forEach(serviceName => {
      visit(serviceName);
    });

    return result;
  }
}