import { create } from 'zustand'

interface EditorState {
  // Content state
  content: string
  selectedText: string
  selectionStart: number
  selectionEnd: number
  currentChapter: number
  
  // UI state
  showAiChat: boolean
  showStoryBible: boolean
  showChapterNavigator: boolean
  showVersionHistory: boolean
  
  // AI chat state
  chatHistory: Array<{
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
    context?: {
      selectedText?: string
      action?: 'edit' | 'expand' | 'rewrite' | 'suggest'
    }
  }>
  isAiProcessing: boolean
  
  // Chapter state
  chapters: Array<{
    id: string
    number: number
    title: string
    status: 'planned' | 'writing' | 'review' | 'complete'
    wordCount: number
  }>
  
  // Actions
  setContent: (content: string) => void
  setSelection: (text: string, start: number, end: number) => void
  clearSelection: () => void
  toggleAiChat: () => void
  toggleStoryBible: () => void
  toggleChapterNavigator: () => void
  toggleVersionHistory: () => void
  addChatMessage: (role: 'user' | 'assistant', content: string, context?: Record<string, unknown>) => void
  clearChatHistory: () => void
  setAiProcessing: (processing: boolean) => void
  setCurrentChapter: (chapter: number) => void
  updateChapterList: (chapters: Array<{id: string, number: number, title: string, status: string, wordCount: number}>) => void
}

export const useEditorStore = create<EditorState>((set) => ({
  // Initial state
  content: '',
  selectedText: '',
  selectionStart: 0,
  selectionEnd: 0,
  currentChapter: 1,
  showAiChat: false,
  showStoryBible: false,
  showChapterNavigator: true,
  showVersionHistory: false,
  chatHistory: [],
  isAiProcessing: false,
  chapters: [],
  
  // Actions
  setContent: (content) => set({ content }),
  
  setSelection: (selectedText, selectionStart, selectionEnd) => 
    set({ selectedText, selectionStart, selectionEnd }),
  
  clearSelection: () => 
    set({ selectedText: '', selectionStart: 0, selectionEnd: 0 }),
  
  toggleAiChat: () => 
    set((state) => ({ showAiChat: !state.showAiChat })),
  
  toggleStoryBible: () => 
    set((state) => ({ showStoryBible: !state.showStoryBible })),
  
  toggleChapterNavigator: () => 
    set((state) => ({ showChapterNavigator: !state.showChapterNavigator })),
  
  toggleVersionHistory: () => 
    set((state) => ({ showVersionHistory: !state.showVersionHistory })),
  
  addChatMessage: (role, content, context) => 
    set((state) => ({
      chatHistory: [
        ...state.chatHistory,
        {
          id: `msg_${Date.now()}`,
          role,
          content,
          timestamp: new Date(),
          context
        }
      ]
    })),
  
  clearChatHistory: () => set({ chatHistory: [] }),
  
  setAiProcessing: (isAiProcessing) => set({ isAiProcessing }),
  
  setCurrentChapter: (currentChapter) => set({ currentChapter }),
  
  updateChapterList: (chapters) => 
    set({ 
      chapters: chapters.map(ch => ({
        ...ch,
        status: ch.status as 'planned' | 'writing' | 'review' | 'complete'
      }))
    })
}))