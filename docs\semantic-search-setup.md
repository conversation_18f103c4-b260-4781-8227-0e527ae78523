# Semantic Search Setup with <PERSON>pa<PERSON>

This document outlines the database setup required for the semantic search functionality using Supabase's vector capabilities.

## Database Setup

### 1. Enable pgvector Extension

```sql
-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;
```

### 2. Create Content Embeddings Table

```sql
-- Create the content embeddings table
CREATE TABLE IF NOT EXISTS content_embeddings (
  id TEXT PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  embedding vector(1536), -- OpenAI text-embedding-3-small dimension
  metadata JSONB DEFAULT '{}',
  content_type TEXT NOT NULL DEFAULT 'chapter',
  title TEXT,
  tags TEXT[] DEFAULT '{}',
  characters TEXT[] DEFAULT '{}',
  locations TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. <PERSON>reate Indexes

```sql
-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS content_embeddings_project_idx ON content_embeddings(project_id);
CREATE INDEX IF NOT EXISTS content_embeddings_type_idx ON content_embeddings(content_type);
CREATE INDEX IF NOT EXISTS content_embeddings_tags_idx ON content_embeddings USING GIN(tags);

-- Create vector similarity index
CREATE INDEX IF NOT EXISTS content_embeddings_embedding_idx ON content_embeddings 
  USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
```

### 4. Create Search Function

```sql
-- Function for similarity search
CREATE OR REPLACE FUNCTION search_content_vectors(
  query_embedding vector(1536),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id TEXT,
  project_id UUID,
  content TEXT,
  metadata JSONB,
  content_type TEXT,
  title TEXT,
  tags TEXT[],
  chapter_id TEXT,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    ce.id,
    ce.project_id,
    ce.content,
    ce.metadata,
    ce.content_type,
    ce.title,
    ce.tags,
    (ce.metadata->>'chapterId')::TEXT as chapter_id,
    1 - (ce.embedding <=> query_embedding) as similarity
  FROM content_embeddings ce
  WHERE 1 - (ce.embedding <=> query_embedding) > match_threshold
  ORDER BY ce.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;
```

### 5. Helper Function for Vector Extension

```sql
-- Helper function to enable vector extension (called from service)
CREATE OR REPLACE FUNCTION enable_vector_extension()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  CREATE EXTENSION IF NOT EXISTS vector;
END;
$$;
```

### 6. Row Level Security (Optional)

```sql
-- Enable RLS on content_embeddings table
ALTER TABLE content_embeddings ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to access embeddings for their projects
CREATE POLICY "Users can access embeddings for their projects" ON content_embeddings
  FOR ALL USING (
    project_id IN (
      SELECT id FROM projects 
      WHERE user_id = auth.uid()
    )
  );
```

## Environment Variables

Add to your `.env.local`:

```env
# OpenAI API Key for embeddings
OPENAI_API_KEY=your_openai_api_key_here

# Supabase configuration (if not already set)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Usage Examples

### 1. Index a Project

```typescript
// Start indexing a project
const response = await fetch('/api/search/index', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'index_project',
    projectId: 'your-project-id'
  })
});

const { jobId } = await response.json();

// Check indexing status
const statusResponse = await fetch(`/api/search/index?jobId=${jobId}`);
const { job } = await statusResponse.json();
```

### 2. Semantic Search

```typescript
// General semantic search
const searchResponse = await fetch('/api/search/semantic', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    query: 'a tense confrontation between characters',
    projectId: 'your-project-id',
    types: ['chapter', 'scene'],
    limit: 10
  })
});

const { results } = await searchResponse.json();
```

### 3. Emotion-based Search

```typescript
// Search by emotion
const emotionResponse = await fetch('/api/search/emotion', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emotion: 'sadness',
    projectId: 'your-project-id',
    limit: 5
  })
});
```

### 4. Character Moments

```typescript
// Find character moments
const characterResponse = await fetch('/api/search/character-moments', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    characterName: 'John',
    projectId: 'your-project-id',
    emotion: 'anger',
    limit: 10
  })
});
```

## Performance Considerations

1. **Embedding Generation**: OpenAI API calls are rate-limited. The service includes caching and batch processing.

2. **Vector Index**: The ivfflat index provides good performance for similarity search but requires tuning the `lists` parameter based on your data size.

3. **Memory Usage**: Embeddings consume significant memory. Consider using the smaller `text-embedding-3-small` model (1536 dimensions) for most use cases.

4. **Indexing Strategy**: 
   - Index chapters as complete units
   - Index character profiles and world-building elements
   - Consider breaking very long content into smaller chunks

## Monitoring

Monitor the semantic search service through:

```typescript
// Health check
const health = await fetch('/api/search/semantic?action=health');

// Service status
const status = await fetch('/api/services/health');
```

## Troubleshooting

1. **Extension Issues**: Ensure pgvector extension is installed in your Supabase instance
2. **Embedding Failures**: Check OpenAI API key and rate limits
3. **Search Performance**: Verify vector indexes are created and optimize `lists` parameter
4. **Memory Issues**: Consider reducing embedding cache size or using smaller models