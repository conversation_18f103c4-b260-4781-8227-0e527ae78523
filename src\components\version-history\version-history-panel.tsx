'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  History, 
  RotateCcw, 
  Clock, 
  FileText, 
  GitBranch,
  Save,
  Trash2,
  Eye,
  Calendar
} from 'lucide-react'
import { VersionHistoryService, type ChapterVersion, type ProjectSnapshot, formatVersionDate } from '@/lib/version-history'
import { useToast } from '@/hooks/use-toast'

interface VersionHistoryPanelProps {
  chapterId: string
  projectId: string
  userId: string
  onVersionRestore?: (version: ChapterVersion) => void
}

export function VersionHistoryPanel({ chapterId, projectId, userId, onVersionRestore }: VersionHistoryPanelProps) {
  const [versions, setVersions] = useState<ChapterVersion[]>([])
  const [snapshots, setSnapshots] = useState<ProjectSnapshot[]>([])
  const [selectedVersion, setSelectedVersion] = useState<ChapterVersion | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreatingSnapshot, setIsCreatingSnapshot] = useState(false)
  const [snapshotName, setSnapshotName] = useState('')
  const [snapshotDescription, setSnapshotDescription] = useState('')

  const { toast } = useToast()
  const versionService = new VersionHistoryService()

  useEffect(() => {
    loadVersionHistory()
  }, [chapterId, projectId]) // eslint-disable-line react-hooks/exhaustive-deps

  const loadVersionHistory = async () => {
    setIsLoading(true)
    try {
      const [chapterVersions, projectSnapshots] = await Promise.all([
        versionService.getChapterVersions(chapterId, userId),
        versionService.getProjectSnapshots(projectId, userId)
      ])
      setVersions(chapterVersions)
      setSnapshots(projectSnapshots)
    } catch (error) {
      console.error('Error loading version history:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleRestoreVersion = async (version: ChapterVersion) => {
    try {
      const success = await versionService.restoreChapterVersion(chapterId, version.id, userId)
      if (success) {
        onVersionRestore?.(version)
        toast({
          title: "Chapter restored",
          description: "Chapter has been restored successfully!",
        })
        await loadVersionHistory() // Refresh to show new version created by restore
      } else {
        toast({
          title: "Restore failed",
          description: "Failed to restore version. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error restoring version:', error)
      toast({
        title: "Restore failed", 
        description: "Failed to restore version. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleCreateSnapshot = async () => {
    if (!snapshotName.trim()) return

    setIsCreatingSnapshot(true)
    try {
      const success = await versionService.createProjectSnapshot(
        projectId, 
        userId, 
        snapshotName, 
        snapshotDescription || undefined
      )
      
      if (success) {
        setSnapshotName('')
        setSnapshotDescription('')
        await loadVersionHistory()
        toast({
          title: "Snapshot created",
          description: "Project snapshot created successfully!",
        })
      } else {
        toast({
          title: "Snapshot failed",
          description: "Failed to create snapshot. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error creating snapshot:', error)
      toast({
        title: "Snapshot failed",
        description: "Failed to create snapshot. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsCreatingSnapshot(false)
    }
  }

  const handleDeleteSnapshot = async (snapshotId: string) => {
    if (confirm('Are you sure you want to delete this snapshot?')) {
      try {
        const success = await versionService.deleteProjectSnapshot(snapshotId, userId)
        if (success) {
          await loadVersionHistory()
          toast({
            title: "Snapshot deleted",
            description: "Project snapshot has been deleted.",
          })
        } else {
          toast({
            title: "Delete failed",
            description: "Failed to delete snapshot. Please try again.",
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error('Error deleting snapshot:', error)
        toast({
          title: "Delete failed",
          description: "Failed to delete snapshot. Please try again.",
          variant: "destructive",
        })
      }
    }
  }

  const getVersionIcon = (version: ChapterVersion) => {
    if (version.is_auto_save) {
      return <Clock className="h-4 w-4 text-muted-foreground" />
    }
    return <Save className="h-4 w-4 text-blue-500" />
  }

  const getWordCountChange = (currentIndex: number) => {
    if (currentIndex === versions.length - 1) return null
    const current = versions[currentIndex]
    const previous = versions[currentIndex + 1]
    const delta = current.word_count - previous.word_count
    return delta
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <History className="h-4 w-4" />
          Version History
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Version History
          </DialogTitle>
          <DialogDescription>
            View, compare, and restore previous versions of your work
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="chapter-versions" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="chapter-versions">Chapter Versions</TabsTrigger>
            <TabsTrigger value="project-snapshots">Project Snapshots</TabsTrigger>
          </TabsList>

          <TabsContent value="chapter-versions" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">Chapter Version History</h3>
                <p className="text-sm text-muted-foreground">
                  {versions.length} versions available
                </p>
              </div>
            </div>

            <ScrollArea className="h-96">
              <div className="space-y-2">
                {isLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2" />
                    <p className="text-muted-foreground">Loading versions...</p>
                  </div>
                ) : versions.length === 0 ? (
                  <div className="text-center py-8">
                    <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h4 className="font-semibold mb-2">No Version History</h4>
                    <p className="text-muted-foreground text-sm">
                      Versions will be created automatically as you edit your chapter.
                    </p>
                  </div>
                ) : (
                  versions.map((version, index) => {
                    const wordCountDelta = getWordCountChange(index)
                    const isLatest = index === 0

                    return (
                      <Card 
                        key={version.id} 
                        className={`cursor-pointer transition-all ${
                          selectedVersion?.id === version.id ? 'ring-2 ring-primary' : 'hover:shadow-md'
                        }`}
                        onClick={() => setSelectedVersion(version)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3">
                              {getVersionIcon(version)}
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <h4 className="font-medium">
                                    Version {version.version_number}
                                    {isLatest && (
                                      <Badge variant="default" className="ml-2 text-xs">
                                        Current
                                      </Badge>
                                    )}
                                  </h4>
                                  {!version.is_auto_save && (
                                    <Badge variant="secondary" className="text-xs">
                                      Manual Save
                                    </Badge>
                                  )}
                                </div>
                                
                                <p className="text-sm text-muted-foreground mt-1">
                                  {version.change_summary || 'No description'}
                                </p>
                                
                                <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                  <span className="flex items-center gap-1">
                                    <Calendar className="h-3 w-3" />
                                    {formatVersionDate(version.created_at)}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <FileText className="h-3 w-3" />
                                    {version.word_count.toLocaleString()} words
                                  </span>
                                  {wordCountDelta !== null && (
                                    <span className={`flex items-center gap-1 ${
                                      wordCountDelta > 0 ? 'text-green-600' : wordCountDelta < 0 ? 'text-red-600' : 'text-muted-foreground'
                                    }`}>
                                      {wordCountDelta > 0 ? '+' : ''}{wordCountDelta}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setSelectedVersion(version)
                                }}
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                              {!isLatest && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleRestoreVersion(version)
                                  }}
                                >
                                  <RotateCcw className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })
                )}
              </div>
            </ScrollArea>

            {/* Version Preview */}
            {selectedVersion && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    Version {selectedVersion.version_number} Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Title</Label>
                      <p className="text-sm">{selectedVersion.title || 'Untitled'}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Content Preview</Label>
                      <ScrollArea className="h-32 border rounded p-3">
                        <p className="text-sm whitespace-pre-wrap">
                          {selectedVersion.content?.substring(0, 500) || 'No content'}
                          {selectedVersion.content && selectedVersion.content.length > 500 && '...'}
                        </p>
                      </ScrollArea>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-muted-foreground">
                        {selectedVersion.word_count.toLocaleString()} words • {formatVersionDate(selectedVersion.created_at)}
                      </div>
                      {selectedVersion.version_number !== versions[0]?.version_number && (
                        <Button 
                          size="sm" 
                          onClick={() => handleRestoreVersion(selectedVersion)}
                          className="gap-2"
                        >
                          <RotateCcw className="h-3 w-3" />
                          Restore This Version
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="project-snapshots" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">Project Snapshots</h3>
                <p className="text-sm text-muted-foreground">
                  Save complete project states for major milestones
                </p>
              </div>
            </div>

            {/* Create Snapshot */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Create New Snapshot</CardTitle>
                <CardDescription>
                  Save the current state of your entire project
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="snapshot-name">Snapshot Name</Label>
                  <Input
                    id="snapshot-name"
                    value={snapshotName}
                    onChange={(e) => setSnapshotName(e.target.value)}
                    placeholder="e.g., First Draft Complete"
                  />
                </div>
                <div>
                  <Label htmlFor="snapshot-description">Description (Optional)</Label>
                  <Input
                    id="snapshot-description"
                    value={snapshotDescription}
                    onChange={(e) => setSnapshotDescription(e.target.value)}
                    placeholder="Brief description of this milestone"
                  />
                </div>
                <Button 
                  onClick={handleCreateSnapshot}
                  disabled={!snapshotName.trim() || isCreatingSnapshot}
                  className="gap-2"
                >
                  {isCreatingSnapshot ? (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  Create Snapshot
                </Button>
              </CardContent>
            </Card>

            {/* Snapshots List */}
            <ScrollArea className="h-64">
              <div className="space-y-2">
                {snapshots.length === 0 ? (
                  <div className="text-center py-8">
                    <Save className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h4 className="font-semibold mb-2">No Project Snapshots</h4>
                    <p className="text-muted-foreground text-sm">
                      Create snapshots to save important project milestones.
                    </p>
                  </div>
                ) : (
                  snapshots.map((snapshot) => (
                    <Card key={snapshot.id}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium">{snapshot.name}</h4>
                            {snapshot.description && (
                              <p className="text-sm text-muted-foreground mt-1">
                                {snapshot.description}
                              </p>
                            )}
                            <div className="flex items-center gap-1 mt-2 text-xs text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              {formatVersionDate(snapshot.created_at)}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteSnapshot(snapshot.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}