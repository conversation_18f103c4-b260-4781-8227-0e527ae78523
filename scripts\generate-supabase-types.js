#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔄 Generating Supabase types...\n');

// Check if we have the required environment variables
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  console.error('❌ Error: NEXT_PUBLIC_SUPABASE_URL is not set in environment variables');
  console.log('Please ensure you have a .env.local file with your Supabase configuration');
  process.exit(1);
}

// Extract project ID from Supabase URL
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const projectIdMatch = supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/);

if (!projectIdMatch) {
  console.error('❌ Error: Could not extract project ID from Supabase URL');
  process.exit(1);
}

const projectId = projectIdMatch[1];
console.log(`📋 Project ID: ${projectId}`);

try {
  // Check if Supabase CLI is installed
  try {
    execSync('npx supabase --version', { stdio: 'ignore' });
  } catch (error) {
    console.log('📦 Installing Supabase CLI...');
    execSync('npm install -D supabase', { stdio: 'inherit' });
  }

  // Generate types
  console.log('\n🚀 Generating types from Supabase...');
  const outputPath = path.join(__dirname, '..', 'src', 'lib', 'db', 'database.types.ts');
  
  const command = `npx supabase gen types typescript --project-id ${projectId} --schema public`;
  const types = execSync(command, { encoding: 'utf-8' });
  
  // Add header to the generated file
  const header = `/**
 * This file was auto-generated by Supabase CLI.
 * DO NOT EDIT THIS FILE DIRECTLY.
 * 
 * To regenerate this file, run:
 * npm run generate:types
 * 
 * @generated ${new Date().toISOString()}
 */

`;

  // Write the types to file
  fs.writeFileSync(outputPath, header + types);
  
  console.log(`✅ Types generated successfully at: ${outputPath}`);
  
  // Update the main types export file
  const typesIndexPath = path.join(__dirname, '..', 'src', 'lib', 'db', 'types.ts');
  const typesIndexContent = `// Re-export generated database types
export type { Database } from './database.types'
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]

// Helper types
export type Project = Tables<'projects'>
export type Chapter = Tables<'chapters'>
export type Character = Tables<'characters'>
export type StoryBible = Tables<'story_bible'>
export type ProcessingTask = Tables<'processing_tasks'>
export type Profile = Tables<'profiles'>
export type SelectionProfile = Tables<'selection_profiles'>
export type ReferenceMaterial = Tables<'reference_materials'>
export type WritingSession = Tables<'writing_sessions'>
export type ContentEmbedding = Tables<'content_embeddings'>
`;

  fs.writeFileSync(typesIndexPath, typesIndexContent);
  console.log(`✅ Updated types index at: ${typesIndexPath}`);
  
  console.log('\n🎉 Type generation complete!');
  console.log('You can now use strongly typed Supabase queries throughout your application.');
  
} catch (error) {
  console.error('\n❌ Error generating types:', error.message);
  console.log('\nTroubleshooting:');
  console.log('1. Make sure you have access to your Supabase project');
  console.log('2. Check your internet connection');
  console.log('3. Ensure your Supabase project ID is correct');
  process.exit(1);
}