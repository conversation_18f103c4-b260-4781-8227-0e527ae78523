export interface PlotHole {
  id: string;
  type: 'contradiction' | 'inconsistency' | 'gap' | 'timeline';
  severity: 'critical' | 'major' | 'minor';
  description: string;
  location: {
    chapter: number;
    paragraph: number;
  };
  suggestion: string;
  relatedElements: string[];
  confidence: number;
}

export interface PacingData {
  overallScore: number;
  tensionCurve: { 
    chapter: number; 
    tension: number; 
    action: number; 
    dialogue: number; 
    description: number;
    wordCount: number;
    avgSentenceLength: number;
  }[];
  pacingIssues: { 
    chapter: number; 
    issue: string; 
    severity: 'high' | 'medium' | 'low';
    suggestion: string;
  }[];
  recommendations: string[];
  idealPacing?: {
    structureType: string;
    tensionPoints: { chapter: number; expectedTension: number }[];
  };
}

export interface CharacterArcData {
  characterId: string;
  characterName: string;
  role: 'protagonist' | 'antagonist' | 'supporting';
  arcType: 'positive_change' | 'negative_change' | 'flat_arc' | 'corruption' | 'redemption';
  progression: { 
    chapter: number; 
    development: number; 
    emotionalState: string;
    confidence: number;
    agency: number;
    relationships: number;
  }[];
  milestones: { 
    chapter: number; 
    event: string; 
    significance: number;
    type: 'growth' | 'setback' | 'revelation' | 'decision' | 'conflict';
  }[];
  consistency: number;
  completed: boolean;
  themes: string[];
}

export interface ReadabilityData {
  score: number;
  gradeLevel: number;
  ageAppropriate: boolean;
  metrics: {
    avgSentenceLength: number;
    complexWords: number;
    readingEase: number;
    syllablesPerWord?: number;
    passiveVoice?: number;
    transitionWords?: number;
  };
  suggestions: {
    type: 'vocabulary' | 'structure' | 'complexity' | 'flow';
    priority: 'high' | 'medium' | 'low';
    description: string;
    example?: string;
    improvement?: string;
  }[];
  audienceAnalysis: {
    targetAge?: string;
    currentAge?: string;
    match?: boolean;
    childrenFriendly?: number;
    teenAppropriate?: number;
    adultLevel?: number;
  };
  comparison: {
    genre?: string;
    averageScore?: number;
    percentile?: number;
    targetAudience?: string;
    benchmarkScore?: number;
    deviation?: number;
  };
}

export interface EmotionalData {
  chapter: number;
  emotions: {
    joy: number;
    sadness: number;
    anger: number;
    fear: number;
    surprise: number;
    tension: number;
    hope?: number;
    despair?: number;
  };
  intensity: number;
  dominantEmotion: string;
  emotionalRange: {
    min: number;
    max: number;
    variance: number;
  } | number;
  transitions: {
    from: string;
    to: string;
    effectiveness: number;
  }[];
}

export interface Recommendation {
  id: string;
  category: 'plot' | 'pacing' | 'character' | 'readability' | 'emotion';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  action: string;
  impact: string;
}

export interface AnalysisData {
  plotHoles: PlotHole[];
  pacingData: PacingData;
  characterArcs: CharacterArcData[];
  readabilityScore: ReadabilityData;
  emotionalJourney: EmotionalData[];
  overallHealth: number;
  recommendations: Recommendation[];
}