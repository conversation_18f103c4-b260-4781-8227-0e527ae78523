'use client'

import dynamic from 'next/dynamic'
import { Skeleton } from '@/components/ui/skeleton'
import { Card } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'
import type { UnifiedMonacoEditorProps } from './unified-monaco-editor'

// Loading component shown while Monaco Editor loads
function MonacoEditorSkeleton() {
  return (
    <Card className="h-full flex flex-col items-center justify-center bg-muted/10">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">Loading editor...</p>
          <p className="text-xs text-muted-foreground">This may take a moment on first load</p>
        </div>
      </div>
      <Skeleton className="absolute inset-0 opacity-5" />
    </Card>
  )
}

// Dynamically import the Monaco editor with no SSR
export const LazyMonacoEditor = dynamic<UnifiedMonacoEditorProps>(
  () => import('./unified-monaco-editor').then(mod => mod.UnifiedMonacoEditor),
  {
    loading: () => <MonacoEditorSkeleton />,
    ssr: false,
  }
)

// Export the props type for convenience
export type { UnifiedMonacoEditorProps } from './unified-monaco-editor'