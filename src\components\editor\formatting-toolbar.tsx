'use client'

import React, { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Toggle } from '@/components/ui/toggle'
import { 
  Bold, 
  Italic, 
  Underline,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Quote,
  Heading1,
  Heading2,
  Heading3,
  Link,
  Image,
  Code,
} from 'lucide-react'

interface FormattingToolbarProps {
  onFormat: (command: string, value?: string) => void
  className?: string
}

function FormattingToolbarComponent({ onFormat, className = '' }: FormattingToolbarProps) {
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set())

  const toggleFormat = useCallback((format: string) => {
    const newFormats = new Set(activeFormats)
    if (newFormats.has(format)) {
      newFormats.delete(format)
    } else {
      newFormats.add(format)
    }
    setActiveFormats(newFormats)
    onFormat(format)
  }, [activeFormats, onFormat])

  const formatButtons = [
    { icon: Bold, command: 'bold', label: 'Bold' },
    { icon: Italic, command: 'italic', label: 'Italic' },
    { icon: Underline, command: 'underline', label: 'Underline' },
    { icon: Strikethrough, command: 'strikethrough', label: 'Strikethrough' },
  ]

  const alignmentButtons = [
    { icon: AlignLeft, command: 'alignLeft', label: 'Align Left' },
    { icon: AlignCenter, command: 'alignCenter', label: 'Align Center' },
    { icon: AlignRight, command: 'alignRight', label: 'Align Right' },
  ]

  const headingButtons = [
    { icon: Heading1, command: 'heading1', label: 'Heading 1' },
    { icon: Heading2, command: 'heading2', label: 'Heading 2' },
    { icon: Heading3, command: 'heading3', label: 'Heading 3' },
  ]

  const listButtons = [
    { icon: List, command: 'bulletList', label: 'Bullet List' },
    { icon: ListOrdered, command: 'orderedList', label: 'Numbered List' },
  ]

  const insertButtons = [
    { icon: Link, command: 'link', label: 'Insert Link' },
    { icon: Image, command: 'image', label: 'Insert Image' },
    { icon: Quote, command: 'blockquote', label: 'Quote' },
    { icon: Code, command: 'code', label: 'Code Block' },
  ]

  // Create memoized handlers to prevent function recreation in render loops
  const createToggleHandler = useCallback((command: string) => () => toggleFormat(command), [toggleFormat])

  return (
    <div className={`flex items-center gap-1 p-2 border-b bg-background ${className}`}>
      {/* Text Formatting */}
      <div className="flex items-center gap-1">
        {formatButtons.map(({ icon: Icon, command, label }) => (
          <Toggle
            key={command}
            pressed={activeFormats.has(command)}
            onPressedChange={createToggleHandler(command)}
            size="sm"
            aria-label={label}
          >
            <Icon className="h-4 w-4" />
          </Toggle>
        ))}
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Headings */}
      <div className="flex items-center gap-1">
        {headingButtons.map(({ icon: Icon, command, label }) => (
          <Button
            key={command}
            variant="ghost"
            size="sm"
            onClick={() => onFormat(command)}
            aria-label={label}
          >
            <Icon className="h-4 w-4" />
          </Button>
        ))}
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Alignment */}
      <div className="flex items-center gap-1">
        {alignmentButtons.map(({ icon: Icon, command, label }) => (
          <Button
            key={command}
            variant="ghost"
            size="sm"
            onClick={() => onFormat(command)}
            aria-label={label}
          >
            <Icon className="h-4 w-4" />
          </Button>
        ))}
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Lists */}
      <div className="flex items-center gap-1">
        {listButtons.map(({ icon: Icon, command, label }) => (
          <Button
            key={command}
            variant="ghost"
            size="sm"
            onClick={() => onFormat(command)}
            aria-label={label}
          >
            <Icon className="h-4 w-4" />
          </Button>
        ))}
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Insert */}
      <div className="flex items-center gap-1">
        {insertButtons.map(({ icon: Icon, command, label }) => (
          <Button
            key={command}
            variant="ghost"
            size="sm"
            onClick={() => onFormat(command)}
            aria-label={label}
          >
            <Icon className="h-4 w-4" />
          </Button>
        ))}
      </div>
    </div>
  )
}

export const FormattingToolbar = React.memo(FormattingToolbarComponent)