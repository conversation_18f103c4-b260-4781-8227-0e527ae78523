'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useWizardStore } from '@/stores/wizard-store'
import { PROJECT_SCOPES, TARGET_AUDIENCES, CONTENT_RATINGS } from '@/lib/types/project'
import { TemplateSelector } from './template-selector'

interface StepBasicsProps {
  title: string
  description: string
  onTitleChange: (title: string) => void
  onDescriptionChange: (description: string) => void
}

export function StepBasics({ title, description, onTitleChange, onDescriptionChange }: StepBasicsProps) {
  const { selections, updateSelections } = useWizardStore()
  
  return (
    <div className="space-y-8">
      <TemplateSelector />
      
      <Card>
        <CardHeader>
          <CardTitle>Project Information</CardTitle>
          <CardDescription>
            Tell us about your novel project. This information will help our AI agents 
            understand your vision and create appropriate content.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Project Title *</Label>
            <Input
              id="title"
              placeholder="The Chronicles of..."
              value={title}
              onChange={(e) => onTitleChange(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Story Concept</Label>
            <Textarea
              id="description"
              placeholder="A brief description of your story idea, main characters, or central conflict..."
              value={description}
              onChange={(e) => onDescriptionChange(e.target.value)}
              rows={4}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Project Scope & Audience</CardTitle>
          <CardDescription>
            Define the scope and target audience for your project.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Project Scope</Label>
            <Select 
              value={selections.projectScope} 
              onValueChange={(value) => updateSelections({ projectScope: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select project scope" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(PROJECT_SCOPES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Target Audience</Label>
            <Select 
              value={selections.targetAudience} 
              onValueChange={(value) => updateSelections({ targetAudience: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select target audience" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(TARGET_AUDIENCES).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Content Rating</Label>
            <Select 
              value={selections.contentRating} 
              onValueChange={(value) => updateSelections({ contentRating: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select content rating" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(CONTENT_RATINGS).map(([key, value]) => (
                  <SelectItem key={key} value={value}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}