'use client'

import { useEffect } from 'react'
import { useCachedData, chapterCache, cacheKeys, invalidateChapterCache } from '@/lib/cache/client'
import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/db/types'

type Chapter = Database['public']['Tables']['chapters']['Row']

interface UseCachedChaptersOptions {
  projectId: string
  enabled?: boolean
}

export function useCachedChapters({ projectId, enabled = true }: UseCachedChaptersOptions) {
  const supabase = createClient()
  
  const fetcher = async () => {
    const { data, error } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
    
    if (error) throw error
    return data as Chapter[]
  }
  
  const result = useCachedData<Chapter[]>({
    cacheKey: cacheKeys.chapterList(projectId),
    cache: chapterCache,
    fetcher,
    dependencies: [projectId],
    staleTime: 30000, // Consider data stale after 30 seconds
  })

  // Subscribe to real-time updates
  useEffect(() => {
    if (!enabled) return

    const subscription = supabase
      .channel(`chapters-${projectId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chapters',
          filter: `project_id=eq.${projectId}`,
        },
        (payload) => {
          // Invalidate cache and refresh
          if (payload.eventType === 'DELETE' && payload.old) {
            invalidateChapterCache((payload.old as Chapter).id, projectId)
          } else if (payload.new) {
            invalidateChapterCache((payload.new as Chapter).id, projectId)
          }
          result.refresh()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [projectId, enabled, result, supabase])

  return result
}

export function useCachedChapter(chapterId: string | null) {
  const supabase = createClient()
  
  const fetcher = async () => {
    if (!chapterId) return null
    
    const { data, error } = await supabase
      .from('chapters')
      .select('*')
      .eq('id', chapterId)
      .single()
    
    if (error) throw error
    return data as Chapter
  }
  
  const result = useCachedData<Chapter | null>({
    cacheKey: chapterId ? cacheKeys.chapter(chapterId) : 'no-chapter',
    cache: chapterCache,
    fetcher,
    dependencies: [chapterId],
    staleTime: 30000,
  })

  // Subscribe to real-time updates for this specific chapter
  useEffect(() => {
    if (!chapterId) return

    const subscription = supabase
      .channel(`chapter-${chapterId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chapters',
          filter: `id=eq.${chapterId}`,
        },
        () => {
          // Invalidate cache and refresh
          invalidateChapterCache(chapterId)
          result.refresh()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [chapterId, result, supabase])

  return result
}