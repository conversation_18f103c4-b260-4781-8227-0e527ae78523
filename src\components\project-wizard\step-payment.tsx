'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { formatPrice } from '@/lib/stripe'
import { StripePaymentForm } from './stripe-payment-form'

interface StepPaymentProps {
  onPaymentComplete: () => void
}

export function StepPayment({ onPaymentComplete }: StepPaymentProps) {
  const projectFee = 2500 // $25.00 in cents
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Payment</h2>
        <p className="text-muted-foreground">
          Complete your payment to create your project
        </p>
      </div>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Project Creation Fee</CardTitle>
            <CardDescription>
              One-time fee to create and manage your book project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">BookScribe Project</p>
                <p className="text-sm text-muted-foreground">Includes AI assistance and all features</p>
              </div>
              <div className="text-xl font-bold">{formatPrice(projectFee)}</div>
            </div>
          </CardContent>
        </Card>
        
        <StripePaymentForm 
          amount={projectFee}
          onPaymentSuccess={onPaymentComplete}
        />
      </div>
    </div>
  )
}