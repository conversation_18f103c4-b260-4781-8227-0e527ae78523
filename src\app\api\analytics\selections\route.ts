import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for analytics events (100 events per hour)
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(100, clientIP);
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    const body = await request.json();
    const { 
      userId, 
      projectId, 
      selectionProfileId, 
      eventType, 
      selectionData, 
      outcomeData 
    } = body;

    if (!userId || !eventType) {
      return NextResponse.json({ error: 'User ID and event type are required' }, { status: 400 });
    }

    // Validate event type
    const validEventTypes = [
      'profile_used',
      'project_created',
      'project_completed',
      'project_abandoned',
      'selection_modified',
      'writing_started',
      'chapter_completed',
      'export_generated'
    ];

    if (!validEventTypes.includes(eventType)) {
      return NextResponse.json({ error: 'Invalid event type' }, { status: 400 });
    }

    const analyticsData = {
      user_id: userId,
      project_id: projectId || null,
      selection_profile_id: selectionProfileId || null,
      event_type: eventType,
      selection_data: selectionData || {},
      outcome_data: outcomeData || {},
    };

    const { data: analyticsEntry, error } = await supabase
      .from('selection_analytics')
      .insert(analyticsData)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json({ 
      success: true,
      analyticsEntry: {
        id: analyticsEntry.id,
        userId: analyticsEntry.user_id,
        projectId: analyticsEntry.project_id,
        selectionProfileId: analyticsEntry.selection_profile_id,
        eventType: analyticsEntry.event_type,
        selectionData: analyticsEntry.selection_data,
        outcomeData: analyticsEntry.outcome_data,
        createdAt: new Date(analyticsEntry.created_at),
      }
    });
  } catch (error) {
    console.error('Selection analytics API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const projectId = searchParams.get('projectId');
    const eventType = searchParams.get('eventType');
    const profileId = searchParams.get('profileId');
    const days = parseInt(searchParams.get('days') || '30');

    if (!userId && !projectId && !profileId) {
      return NextResponse.json({ error: 'At least one filter parameter is required' }, { status: 400 });
    }

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    let query = supabase
      .from('selection_analytics')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    // Apply filters
    if (userId) {
      query = query.eq('user_id', userId);
    }
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    if (eventType) {
      query = query.eq('event_type', eventType);
    }
    if (profileId) {
      query = query.eq('selection_profile_id', profileId);
    }

    const { data: analytics, error } = await query;

    if (error) throw error;

    // Transform data
    const formattedAnalytics = analytics?.map(entry => ({
      id: entry.id,
      userId: entry.user_id,
      projectId: entry.project_id,
      selectionProfileId: entry.selection_profile_id,
      eventType: entry.event_type,
      selectionData: entry.selection_data,
      outcomeData: entry.outcome_data,
      createdAt: new Date(entry.created_at),
    })) || [];

    return NextResponse.json({ analytics: formattedAnalytics });
  } catch (error) {
    console.error('Selection analytics API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}