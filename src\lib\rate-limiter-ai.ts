import { RateLimiter } from './rate-limiter';

// Specific rate limiters for AI endpoints
export const aiGenerationLimiter = new RateLimiter('ai-generation');
export const aiChatLimiter = new RateLimiter('ai-chat');
export const aiAnalysisLimiter = new RateLimiter('ai-analysis');

// Rate limit configurations for different AI operations
export const AI_RATE_LIMITS = {
  // Content generation: 10 requests per hour
  generation: {
    requests: 10,
    windowMs: 60 * 60 * 1000, // 1 hour
    limiter: aiGenerationLimiter
  },
  // Chat interactions: 30 requests per hour
  chat: {
    requests: 30,
    windowMs: 60 * 60 * 1000, // 1 hour
    limiter: aiChatLimiter
  },
  // Analysis operations: 20 requests per hour
  analysis: {
    requests: 20,
    windowMs: 60 * 60 * 1000, // 1 hour
    limiter: aiAnalysisLimiter
  }
} as const;

/**
 * Get the appropriate rate limiter for an AI operation type
 */
export function getAIRateLimiter(operationType: keyof typeof AI_RATE_LIMITS) {
  return AI_RATE_LIMITS[operationType];
}

/**
 * Create a rate limit response with proper headers
 */
export function createAIRateLimitResponse(retryAfter: number) {
  return new Response(
    JSON.stringify({
      error: 'Too many AI requests. Please try again later.',
      retryAfter,
      message: 'AI generation limits help ensure fair usage and service availability for all users.'
    }),
    {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        'Retry-After': retryAfter.toString(),
        'X-RateLimit-Limit': '10',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + retryAfter * 1000).toISOString()
      }
    }
  );
}