import { NextRequest } from 'next/server';
import { GET, POST } from '../series/route';

// Mock the auth function
jest.mock('@/lib/auth', () => ({
  authenticateUser: jest.fn(),
  handleRouteError: jest.fn((error) => {
    return new Response(JSON.stringify({ error: error.message }), { status: 500 });
  })
}));

// Mock Supabase client
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            data: [],
            error: null
          }))
        }))
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(() => ({
            data: { id: 'test-series-id', title: 'Test Series' },
            error: null
          }))
        }))
      }))
    }))
  }))
}));

import { authenticateUser } from '@/lib/auth';

const mockAuthenticateUser = authenticateUser as jest.MockedFunction<typeof authenticateUser>;

describe('/api/series', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('should return series for authenticated user', async () => {
      mockAuthenticateUser.mockResolvedValue({
        success: true,
        user: { id: 'user-123', email: '<EMAIL>' }
      });

      const request = new NextRequest('http://localhost:3000/api/series');
      const response = await GET(request);
      
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toHaveProperty('series');
      expect(Array.isArray(data.series)).toBe(true);
    });

    it('should return 401 for unauthenticated user', async () => {
      mockAuthenticateUser.mockResolvedValue({
        success: false,
        response: new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 })
      });

      const request = new NextRequest('http://localhost:3000/api/series');
      const response = await GET(request);
      
      expect(response.status).toBe(401);
    });
  });

  describe('POST', () => {
    it('should create a new series', async () => {
      mockAuthenticateUser.mockResolvedValue({
        success: true,
        user: { id: 'user-123', email: '<EMAIL>' }
      });

      const seriesData = {
        title: 'My New Series',
        description: 'A test series',
        genre: 'Fantasy',
        planned_book_count: 3
      };

      const request = new NextRequest('http://localhost:3000/api/series', {
        method: 'POST',
        body: JSON.stringify(seriesData),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await POST(request);
      
      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data).toHaveProperty('series');
      expect(data.series).toHaveProperty('id');
    });

    it('should return 400 for missing title', async () => {
      mockAuthenticateUser.mockResolvedValue({
        success: true,
        user: { id: 'user-123', email: '<EMAIL>' }
      });

      const request = new NextRequest('http://localhost:3000/api/series', {
        method: 'POST',
        body: JSON.stringify({ description: 'Missing title' }),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await POST(request);
      
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('Series title is required');
    });
  });
});