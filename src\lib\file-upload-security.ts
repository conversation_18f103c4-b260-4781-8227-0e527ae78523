/**
 * Comprehensive file upload security utilities
 * 
 * Provides validation, sanitization, and security checks for file uploads
 * to prevent malicious file uploads and abuse.
 */

// File upload security configuration
export const ALLOWED_FILE_TYPES = {
  // Images - optimized sizes for web usage
  'image/jpeg': { maxSize: 5 * 1024 * 1024, extensions: ['jpg', 'jpeg'], category: 'image' },
  'image/png': { maxSize: 5 * 1024 * 1024, extensions: ['png'], category: 'image' },
  'image/gif': { maxSize: 2 * 1024 * 1024, extensions: ['gif'], category: 'image' },
  'image/webp': { maxSize: 5 * 1024 * 1024, extensions: ['webp'], category: 'image' },
  
  // Documents - reasonable sizes for reference materials
  'application/pdf': { maxSize: 10 * 1024 * 1024, extensions: ['pdf'], category: 'document' },
  'application/msword': { maxSize: 10 * 1024 * 1024, extensions: ['doc'], category: 'document' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { 
    maxSize: 10 * 1024 * 1024, extensions: ['docx'], category: 'document' 
  },
  'text/plain': { maxSize: 1 * 1024 * 1024, extensions: ['txt'], category: 'text' },
  'text/markdown': { maxSize: 1 * 1024 * 1024, extensions: ['md'], category: 'text' },
  
  // Additional safe formats (can be expanded)
  'text/csv': { maxSize: 5 * 1024 * 1024, extensions: ['csv'], category: 'data' },
  'application/json': { maxSize: 1 * 1024 * 1024, extensions: ['json'], category: 'data' },
} as const;

// File signature validation (magic numbers) - prevents file type spoofing
export const FILE_SIGNATURES = {
  'image/jpeg': [0xFF, 0xD8, 0xFF],
  'image/png': [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A],
  'image/gif': [0x47, 0x49, 0x46, 0x38],
  'image/webp': [0x52, 0x49, 0x46, 0x46],
  'application/pdf': [0x25, 0x50, 0x44, 0x46],
  'application/msword': [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [0x50, 0x4B, 0x03, 0x04],
  'text/plain': null, // Text files don't have reliable magic numbers
  'text/markdown': null,
  'text/csv': null,
  'application/json': null,
} as const;

// Security limits
export const SECURITY_LIMITS = {
  MAX_FILES_PER_USER_PER_DAY: 50,
  MAX_TOTAL_STORAGE_PER_USER: 500 * 1024 * 1024, // 500MB
  MAX_TOTAL_STORAGE_PER_PROJECT: 1024 * 1024 * 1024, // 1GB
  MAX_FILENAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 1000,
} as const;

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
  config?: typeof ALLOWED_FILE_TYPES[keyof typeof ALLOWED_FILE_TYPES];
  category?: string;
}

/**
 * Validates file signature (magic numbers) to prevent file type spoofing
 */
export function validateFileSignature(buffer: ArrayBuffer, mimeType: string): boolean {
  const signature = FILE_SIGNATURES[mimeType as keyof typeof FILE_SIGNATURES];
  if (signature === null) return true; // Allow files without magic number validation
  if (!signature) return false; // Disallow unknown types
  
  const uint8Array = new Uint8Array(buffer);
  return signature.every((byte, index) => uint8Array[index] === byte);
}

/**
 * Sanitizes filename to prevent directory traversal and injection attacks
 */
export function sanitizeFileName(fileName: string): string {
  return fileName
    // Remove dangerous characters
    .replace(/[^a-zA-Z0-9.\-_]/g, '_')
    // Remove multiple consecutive underscores
    .replace(/_{2,}/g, '_')
    // Remove leading/trailing dots and underscores
    .replace(/^[._]+|[._]+$/g, '')
    // Limit length
    .substring(0, SECURITY_LIMITS.MAX_FILENAME_LENGTH)
    // Ensure not empty
    || `file_${Date.now()}`;
}

/**
 * Validates file type, size, and extension matching
 */
export function validateFileType(file: File): FileValidationResult {
  const mimeType = file.type;
  const config = ALLOWED_FILE_TYPES[mimeType as keyof typeof ALLOWED_FILE_TYPES];
  
  if (!config) {
    return { 
      isValid: false, 
      error: `File type '${mimeType}' is not allowed. Allowed types: ${Object.keys(ALLOWED_FILE_TYPES).join(', ')}` 
    };
  }
  
  // Check file extension matches MIME type
  const fileExt = file.name.split('.').pop()?.toLowerCase();
  if (!fileExt || !(config.extensions as readonly string[]).includes(fileExt)) {
    return { 
      isValid: false, 
      error: `File extension '${fileExt}' does not match MIME type '${mimeType}'. Expected: ${config.extensions.join(', ')}` 
    };
  }
  
  // Check file size
  if (file.size > config.maxSize) {
    const fileSizeMB = (file.size / 1024 / 1024).toFixed(2);
    const maxSizeMB = (config.maxSize / 1024 / 1024).toFixed(2);
    return { 
      isValid: false, 
      error: `File size ${fileSizeMB}MB exceeds limit of ${maxSizeMB}MB for ${mimeType}` 
    };
  }
  
  // File is valid
  const warnings: string[] = [];
  
  // Add warnings for edge cases
  if (file.size > config.maxSize * 0.8) {
    warnings.push('File is near the size limit');
  }
  
  return { 
    isValid: true, 
    config, 
    category: config.category,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Comprehensive file validation including signature checking
 */
export async function validateFileComprehensive(file: File): Promise<FileValidationResult> {
  // Basic validation first
  const basicValidation = validateFileType(file);
  if (!basicValidation.isValid) {
    return basicValidation;
  }
  
  try {
    // Read file buffer for signature validation
    const fileBuffer = await file.arrayBuffer();
    
    // Validate file signature (magic numbers)
    if (!validateFileSignature(fileBuffer, file.type)) {
      return {
        isValid: false,
        error: 'File content does not match declared file type. This may indicate a malicious file or corrupted upload.'
      };
    }
    
    return basicValidation;
  } catch {
    return {
      isValid: false,
      error: 'Unable to read file for validation. The file may be corrupted.'
    };
  }
}

/**
 * Sanitizes text input to prevent injection attacks
 */
export function sanitizeTextInput(input: string, maxLength: number = SECURITY_LIMITS.MAX_DESCRIPTION_LENGTH): string {
  return input
    // Remove null bytes and control characters
    .replace(/[\x00-\x1F\x7F-\x9F]/g, '')
    // Trim whitespace
    .trim()
    // Limit length
    .substring(0, maxLength);
}

/**
 * Generates a secure random filename
 */
export function generateSecureFilename(originalName: string, prefix?: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2);
  const nameParts = originalName.split('.');
  const nameWithoutExt = nameParts[0] || 'file';
  const sanitizedName = sanitizeFileName(nameWithoutExt);
  const extension = originalName.split('.').pop()?.toLowerCase() || '';
  
  const baseName = prefix 
    ? `${prefix}_${timestamp}_${randomString}_${sanitizedName}`
    : `${timestamp}_${randomString}_${sanitizedName}`;
    
  return `${baseName}.${extension}`;
}

/**
 * File upload security headers for responses
 */
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'Content-Security-Policy': "default-src 'none'",
  'X-Frame-Options': 'DENY',
  'Cache-Control': 'no-store, no-cache, must-revalidate',
} as const;

/**
 * Check if file type is processable for AI analysis
 */
export function isProcessableFileType(mimeType: string): boolean {
  const processableTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/markdown',
    'text/csv'
  ];
  
  return processableTypes.includes(mimeType);
}

