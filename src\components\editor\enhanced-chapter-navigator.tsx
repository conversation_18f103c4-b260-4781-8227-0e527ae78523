'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useEditorStore } from '@/stores/editor-store'
import { 
  ChevronLeft, 
  ChevronRight, 
  Plus, 
 
  CheckCircle, 
  Circle, 
  Edit3,
  Search,
  List,
  Target,
  BookOpen,
  TrendingUp,
  Eye,
  ChevronDown,
  ChevronRight as ChevronRightIcon
} from 'lucide-react'

interface ChapterNavigatorProps {
  projectId: string
  currentChapterId?: string
  content?: string
  onChapterSelect: (chapterId: string, chapterNumber: number) => void
  onCreateChapter: () => void
}

interface OutlineItem {
  id: string
  text: string
  level: number
  lineNumber: number
  wordCount: number
}

export function EnhancedChapterNavigator({ 
  currentChapterId, 
  content = '',
  onChapterSelect,
  onCreateChapter 
}: ChapterNavigatorProps) {
  const { 
    showChapterNavigator, 
    toggleChapterNavigator, 
    chapters 
  } = useEditorStore()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [outline, setOutline] = useState<OutlineItem[]>([])
  const [expandedOutlineItems, setExpandedOutlineItems] = useState<Set<string>>(new Set())

  // Generate outline from content
  useEffect(() => {
    if (content) {
      generateOutline(content)
    }
  }, [content])

  const generateOutline = (text: string) => {
    const lines = text.split('\n')
    const outlineItems: OutlineItem[] = []
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim()
      
      // Detect headings (markdown style)
      if (trimmedLine.startsWith('#')) {
        const level = (trimmedLine.match(/^#+/) || [''])[0].length
        const text = trimmedLine.replace(/^#+\s*/, '')
        
        if (text.length > 0) {
          outlineItems.push({
            id: `outline-${index}`,
            text,
            level,
            lineNumber: index + 1,
            wordCount: text.split(/\s+/).length
          })
        }
      }
      
      // Detect scene breaks or chapter divisions
      else if (trimmedLine.match(/^[-*=]{3,}$/) || trimmedLine.match(/^\*\s*\*\s*\*$/)) {
        outlineItems.push({
          id: `break-${index}`,
          text: '--- Scene Break ---',
          level: 3,
          lineNumber: index + 1,
          wordCount: 0
        })
      }
      
      // Detect dialogue or action lines that might be significant
      else if (trimmedLine.startsWith('"') && trimmedLine.length > 20) {
        const preview = trimmedLine.substring(0, 50) + (trimmedLine.length > 50 ? '...' : '')
        outlineItems.push({
          id: `dialogue-${index}`,
          text: preview,
          level: 4,
          lineNumber: index + 1,
          wordCount: trimmedLine.split(/\s+/).length
        })
      }
    })
    
    setOutline(outlineItems)
  }

  const filteredChapters = chapters.filter(chapter => 
    chapter.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chapter.number.toString().includes(searchTerm)
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'review': return <Eye className="h-4 w-4 text-blue-500" />
      case 'writing': return <Edit3 className="h-4 w-4 text-yellow-500" />
      default: return <Circle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete': return 'text-green-600 bg-green-50'
      case 'review': return 'text-blue-600 bg-blue-50'
      case 'writing': return 'text-yellow-600 bg-yellow-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const toggleOutlineItem = (itemId: string) => {
    const newExpanded = new Set(expandedOutlineItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedOutlineItems(newExpanded)
  }

  const totalWords = chapters.reduce((sum, chapter) => sum + chapter.wordCount, 0)
  const completedChapters = chapters.filter(ch => ch.status === 'complete').length

  if (!showChapterNavigator) return null

  return (
    <Card className="h-full w-80 flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            <CardTitle className="text-lg">Navigation</CardTitle>
          </div>
          <div className="flex items-center gap-1">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? <ChevronRight /> : <ChevronLeft />}
            </Button>
            <Button variant="ghost" size="sm" onClick={toggleChapterNavigator}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0 overflow-hidden">
        <Tabs defaultValue="chapters" className="h-full">
          <TabsList className="grid w-full grid-cols-2 mx-4">
            <TabsTrigger value="chapters">Chapters</TabsTrigger>
            <TabsTrigger value="outline">Outline</TabsTrigger>
          </TabsList>

          <TabsContent value="chapters" className="mt-0 h-full">
            <div className="p-4 space-y-4">
              {/* Project Stats */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">{completedChapters}/{chapters.length}</span>
                </div>
                <Progress value={(completedChapters / Math.max(chapters.length, 1)) * 100} className="h-2" />
                
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="text-center p-2 bg-muted rounded">
                    <TrendingUp className="h-3 w-3 mx-auto mb-1" />
                    <div className="font-medium">{totalWords.toLocaleString()}</div>
                    <div className="text-muted-foreground">Words</div>
                  </div>
                  <div className="text-center p-2 bg-muted rounded">
                    <Target className="h-3 w-3 mx-auto mb-1" />
                    <div className="font-medium">{chapters.length}</div>
                    <div className="text-muted-foreground">Chapters</div>
                  </div>
                </div>
              </div>

              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search chapters..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Chapter List */}
              <ScrollArea className="flex-1">
                <div className="space-y-2">
                  {filteredChapters.map((chapter) => (
                    <Card 
                      key={chapter.id}
                      className={`p-3 cursor-pointer transition-all hover:shadow-md ${
                        currentChapterId === chapter.id ? 'ring-2 ring-primary bg-primary/5' : ''
                      }`}
                      onClick={() => onChapterSelect(chapter.id, chapter.number)}
                    >
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(chapter.status)}
                            <span className="font-medium text-sm">
                              Chapter {chapter.number}
                            </span>
                          </div>
                          <Badge variant="secondary" className="text-xs">
                            {chapter.wordCount.toLocaleString()}
                          </Badge>
                        </div>
                        
                        <h4 className="font-medium text-sm truncate">
                          {chapter.title}
                        </h4>
                        
                        <div className="flex items-center justify-between">
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getStatusColor(chapter.status)}`}
                          >
                            {chapter.status}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {chapter.wordCount} words
                          </span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>

              {/* Add Chapter Button */}
              <Button onClick={onCreateChapter} className="w-full" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Chapter
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="outline" className="mt-0 h-full">
            <div className="p-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-medium text-sm">Current Chapter Outline</h3>
                  <Badge variant="secondary" className="text-xs">
                    {outline.length} items
                  </Badge>
                </div>

                <ScrollArea className="h-96">
                  {outline.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <List className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No outline detected</p>
                      <p className="text-xs mt-1">
                        Use headings (# ## ###) to create structure
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {outline.map((item) => (
                        <div 
                          key={item.id}
                          className="flex items-center gap-2 p-2 rounded hover:bg-muted cursor-pointer"
                          style={{ paddingLeft: `${(item.level - 1) * 12 + 8}px` }}
                        >
                          {item.level <= 3 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0"
                              onClick={() => toggleOutlineItem(item.id)}
                            >
                              {expandedOutlineItems.has(item.id) ? 
                                <ChevronDown className="h-3 w-3" /> : 
                                <ChevronRightIcon className="h-3 w-3" />
                              }
                            </Button>
                          )}
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className={`text-xs font-medium ${
                                item.level === 1 ? 'text-lg' :
                                item.level === 2 ? 'text-base' :
                                item.level === 3 ? 'text-sm' :
                                'text-xs text-muted-foreground'
                              }`}>
                                {item.text}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                Line {item.lineNumber}
                              </Badge>
                              {item.wordCount > 0 && (
                                <span className="text-xs text-muted-foreground">
                                  {item.wordCount} words
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}