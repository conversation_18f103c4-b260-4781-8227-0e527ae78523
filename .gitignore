# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Supabase
.supabase/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
Thumbs.db

# Logs
logs
*.log

# Build artifacts
dist/

# Test files
test-supabase.mjs