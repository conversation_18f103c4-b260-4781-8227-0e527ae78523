import { openai, defaultConfig } from '../openai'
import { 
  ChapterPlannerInput, 
  ChapterOutline, 
  AgentResponse 
} from '../types/agents'

export class ChapterPlannerAgent {
  private config = {
    ...defaultConfig,
    temperature: 0.7,
    maxTokens: 8000
  }

  async generateChapterOutlines(input: ChapterPlannerInput): Promise<AgentResponse<ChapterOutline[]>> {
    const startTime = Date.now()
    
    try {
      const systemPrompt = this.buildSystemPrompt(input)
      const userPrompt = this.buildUserPrompt(input)

      const response = await openai.chat.completions.create({
        model: this.config.model,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        response_format: { type: 'json_object' }
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response content received')
      }

      const result = JSON.parse(content)
      const chapters = result.chapters as ChapterOutline[]
      
      return {
        success: true,
        data: chapters,
        executionTime: Date.now() - startTime,
        tokensUsed: response.usage?.total_tokens
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      }
    }
  }

  private buildSystemPrompt(input: ChapterPlannerInput): string {
    const avgWordsPerChapter = Math.round(input.targetWordCount / input.targetChapters)
    
    return `You are an expert Chapter Planner AI agent specializing in breaking down story structures into detailed chapter outlines. Your task is to create comprehensive chapter outlines that ensure proper pacing, character development, and plot progression.

PLANNING REQUIREMENTS:
- Total chapters: ${input.targetChapters}
- Total word count: ${input.targetWordCount.toLocaleString()} words
- Average words per chapter: ${avgWordsPerChapter.toLocaleString()} words
- Pacing preference: ${input.projectSelections.pacingPreference}
- Structure type: ${input.projectSelections.structureType}
- Chapter structure: ${input.projectSelections.chapterStructure}

CHAPTER OUTLINE GUIDELINES:
- Ensure each chapter advances the plot meaningfully
- Balance action, dialogue, and exposition
- Plan character appearances and development moments
- Include conflict and resolution elements
- Create compelling chapter endings (hooks/cliffhangers)
- Maintain proper pacing throughout the story
- Distribute word count appropriately across chapters
- Track character arcs and relationship development

SCENE PLANNING:
- Break each chapter into 2-4 distinct scenes
- Specify setting, characters, purpose, and conflict for each scene
- Ensure smooth transitions between scenes
- Estimate word count for each scene

RESPONSE FORMAT:
Return a valid JSON object with a "chapters" array containing ChapterOutline objects.
Each chapter must include: chapterNumber, title, targetWordCount, summary, scenes array, povCharacter (if applicable), timeline, keyEvents, and transitions.

Ensure the chapter outlines create a cohesive, engaging narrative flow that supports the overall story structure.`
  }

  private buildUserPrompt(input: ChapterPlannerInput): string {
    const { storyStructure, characters, projectSelections } = input
    
    return `Please create detailed chapter outlines for this story:

STORY STRUCTURE:
Title: ${storyStructure.title}
Genre: ${storyStructure.genre}
Themes: ${storyStructure.themes.join(', ')}

ACTS BREAKDOWN:
${storyStructure.acts.map(act => `
Act ${act.number}: ${act.title} (${act.wordCount.toLocaleString()} words)
${act.description}
Key Events: ${act.keyEvents.join(', ')}
`).join('\n')}

MAIN CONFLICTS:
${storyStructure.conflicts.map(conflict => `${conflict.type}: ${conflict.description}`).join('\n')}

CHARACTER ROSTER:
${characters.map(char => `
${char.name} (${char.role}): ${char.description}
Arc: ${char.arc.startingPoint} → ${char.arc.transformation} → ${char.arc.endingPoint}
`).join('\n')}

PROJECT SPECIFICATIONS:
- Narrative Voice: ${projectSelections.narrativeVoice}
- Timeline Complexity: ${projectSelections.timelineComplexity}
- POV Characters: ${projectSelections.povCharacterCount} (${projectSelections.povCharacterType})
- Chapter Structure: ${projectSelections.chapterStructure}

PACING AND STRUCTURE:
- Overall Pacing: ${projectSelections.pacingPreference}
- Structure Framework: ${projectSelections.structureType}
- Target Chapters: ${input.targetChapters}
- Target Word Count: ${input.targetWordCount.toLocaleString()} words

STORY TIMELINE:
${storyStructure.timeline.map(event => `Chapter ${event.chapter || 'TBD'}: ${event.event} (${event.importance})`).join('\n')}

Create chapter outlines that:
1. Follow the act structure and hit all major story beats
2. Develop characters through their planned arcs
3. Maintain appropriate pacing for the genre and preferences
4. Include compelling hooks and transitions
5. Distribute word count to match the overall target
6. Support the chosen narrative voice and POV structure

Ensure each chapter serves a clear purpose in advancing the story toward its resolution.`
  }
}