'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useEditorStore } from '@/stores/editor-store'
import { useStoryBible } from '@/hooks/use-story-bible'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Book, 
  Users, 
  Map, 
  Clock, 
  Lightbulb,
  X,
  ChevronDown,
  ChevronRight,
  Plus,
  Trash2,
  Loader2
} from 'lucide-react'

interface StoryBiblePanelProps {
  projectId: string
}

export function StoryBiblePanel({ projectId }: StoryBiblePanelProps) {
  const { showStoryBible, toggleStoryBible } = useEditorStore()
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['characters']))
  const [isAddingCharacter, setIsAddingCharacter] = useState(false)
  const [newCharacter, setNewCharacter] = useState({
    name: '',
    role: '',
    description: '',
    backstory: '',
    traits: [] as string[]
  })

  const {
    storyBible,
    isLoading,
    loadStoryBible,
    addCharacter,
    deleteCharacter
  } = useStoryBible(projectId)

  useEffect(() => {
    if (projectId) {
      loadStoryBible()
    }
  }, [projectId, loadStoryBible])

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const handleAddCharacter = async () => {
    if (!newCharacter.name.trim() || !newCharacter.role.trim()) return

    const success = await addCharacter(newCharacter)
    if (success) {
      setNewCharacter({
        name: '',
        role: '',
        description: '',
        backstory: '',
        traits: []
      })
      setIsAddingCharacter(false)
    }
  }

  const handleDeleteCharacter = async (id: string) => {
    if (confirm('Are you sure you want to delete this character?')) {
      await deleteCharacter(id)
    }
  }

  if (!showStoryBible) return null

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Book className="h-5 w-5" />
            Story Bible
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={toggleStoryBible}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <Tabs defaultValue="reference" className="h-full">
          <TabsList className="grid w-full grid-cols-2 mx-4">
            <TabsTrigger value="reference">Reference</TabsTrigger>
            <TabsTrigger value="tracking">Tracking</TabsTrigger>
          </TabsList>

          <TabsContent value="reference" className="mt-0 h-full">
            <ScrollArea className="h-full">
              {isLoading ? (
                <div className="p-4 space-y-4">
                  <div className="space-y-3">
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                  </div>
                  <div className="space-y-3">
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                </div>
              ) : (
              <div className="p-4 space-y-4">
                {/* Characters Section */}
                <div className="space-y-2">
                  <Button
                    variant="ghost"
                    className="w-full justify-start p-2 h-auto"
                    onClick={() => toggleSection('characters')}
                  >
                    <div className="flex items-center gap-2">
                      {expandedSections.has('characters') ? 
                        <ChevronDown className="h-4 w-4" /> : 
                        <ChevronRight className="h-4 w-4" />
                      }
                      <Users className="h-4 w-4" />
                      <span className="font-medium">Characters</span>
                      <Badge variant="secondary" className="ml-auto">
                        {storyBible?.characters?.length || 0}
                      </Badge>
                    </div>
                  </Button>

                  {expandedSections.has('characters') && (
                    <div className="ml-6 space-y-3">
                      {/* Add Character Button */}
                      <Dialog open={isAddingCharacter} onOpenChange={setIsAddingCharacter}>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" className="w-full">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Character
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add New Character</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="name">Name</Label>
                              <Input
                                id="name"
                                value={newCharacter.name}
                                onChange={(e) => setNewCharacter({...newCharacter, name: e.target.value})}
                                placeholder="Character name"
                              />
                            </div>
                            <div>
                              <Label htmlFor="role">Role</Label>
                              <Input
                                id="role"
                                value={newCharacter.role}
                                onChange={(e) => setNewCharacter({...newCharacter, role: e.target.value})}
                                placeholder="e.g., Protagonist, Antagonist, Supporting"
                              />
                            </div>
                            <div>
                              <Label htmlFor="description">Description</Label>
                              <Textarea
                                id="description"
                                value={newCharacter.description}
                                onChange={(e) => setNewCharacter({...newCharacter, description: e.target.value})}
                                placeholder="Brief character description"
                                rows={3}
                              />
                            </div>
                            <div className="flex gap-2">
                              <Button onClick={handleAddCharacter} disabled={!newCharacter.name.trim()}>
                                Add Character
                              </Button>
                              <Button variant="outline" onClick={() => setIsAddingCharacter(false)}>
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>

                      {storyBible?.characters && storyBible.characters.length > 0 ? (
                        storyBible.characters.map((character) => (
                          <Card key={character.id} className="p-3">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <h4 className="font-medium">{character.name}</h4>
                                  <Badge variant="outline" className="text-xs">
                                    {character.role}
                                  </Badge>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteCharacter(character.id)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {character.description}
                              </p>
                              {character.traits?.length > 0 && (
                                <div className="flex flex-wrap gap-1">
                                  {character.traits.slice(0, 3).map((trait, index) => (
                                    <Badge key={index} variant="secondary" className="text-xs">
                                      {trait}
                                    </Badge>
                                  ))}
                                  {character.traits.length > 3 && (
                                    <Badge variant="secondary" className="text-xs">
                                      +{character.traits.length - 3}
                                    </Badge>
                                  )}
                                </div>
                              )}
                            </div>
                          </Card>
                        ))
                      ) : (
                        <p className="text-sm text-muted-foreground">No characters defined yet</p>
                      )}
                    </div>
                  )}
                </div>

                {/* World Rules Section */}
                <div className="space-y-2">
                  <Button
                    variant="ghost"
                    className="w-full justify-start p-2 h-auto"
                    onClick={() => toggleSection('world')}
                  >
                    <div className="flex items-center gap-2">
                      {expandedSections.has('world') ? 
                        <ChevronDown className="h-4 w-4" /> : 
                        <ChevronRight className="h-4 w-4" />
                      }
                      <Map className="h-4 w-4" />
                      <span className="font-medium">World Rules</span>
                      <Badge variant="secondary" className="ml-auto">
                        {Object.keys(storyBible?.worldRules || {}).length}
                      </Badge>
                    </div>
                  </Button>

                  {expandedSections.has('world') && (
                    <div className="ml-6 space-y-2">
                      {Object.entries(storyBible?.worldRules || {}).map(([key, value]) => (
                        <Card key={key} className="p-3">
                          <div className="space-y-1">
                            <h4 className="font-medium text-sm capitalize">
                              {key.replace(/_/g, ' ')}
                            </h4>
                            <p className="text-sm text-muted-foreground">{value}</p>
                          </div>
                        </Card>
                      ))}
                      {Object.keys(storyBible?.worldRules || {}).length === 0 && (
                        <p className="text-sm text-muted-foreground">No world rules defined yet</p>
                      )}
                    </div>
                  )}
                </div>

                {/* Timeline Section */}
                <div className="space-y-2">
                  <Button
                    variant="ghost"
                    className="w-full justify-start p-2 h-auto"
                    onClick={() => toggleSection('timeline')}
                  >
                    <div className="flex items-center gap-2">
                      {expandedSections.has('timeline') ? 
                        <ChevronDown className="h-4 w-4" /> : 
                        <ChevronRight className="h-4 w-4" />
                      }
                      <Clock className="h-4 w-4" />
                      <span className="font-medium">Timeline</span>
                      <Badge variant="secondary" className="ml-auto">
                        {storyBible?.timeline?.length || 0}
                      </Badge>
                    </div>
                  </Button>

                  {expandedSections.has('timeline') && (
                    <div className="ml-6 space-y-2">
                      {storyBible?.timeline && storyBible.timeline.length > 0 ? (
                        storyBible.timeline.map((event, index) => (
                          <Card key={index} className="p-3">
                            <div className="flex items-start gap-3">
                              <Badge variant="outline" className="text-xs">
                                Ch. {event.chapter}
                              </Badge>
                              <p className="text-sm flex-1">{event.event}</p>
                            </div>
                          </Card>
                        ))
                      ) : (
                        <p className="text-sm text-muted-foreground">No timeline events yet</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="tracking" className="mt-0 h-full">
            <ScrollArea className="h-full">
              {isLoading ? (
                <div className="p-4 space-y-4">
                  <Skeleton className="h-8 w-32" />
                  <div className="space-y-3">
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                  <div className="grid grid-cols-3 gap-3">
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                </div>
              ) : (
              <div className="p-4 space-y-4">
                {/* Plot Threads */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Lightbulb className="h-4 w-4" />
                    <span className="font-medium">Plot Threads</span>
                  </div>

                  <div className="space-y-2">
                    {storyBible?.plotThreads && storyBible.plotThreads.length > 0 ? (
                      storyBible.plotThreads.map((thread) => (
                        <Card key={thread.id} className="p-3">
                          <div className="flex items-start gap-3">
                            <Badge 
                              variant={thread.status === 'active' ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {thread.status}
                            </Badge>
                            <p className="text-sm flex-1">{thread.description}</p>
                          </div>
                        </Card>
                      ))
                    ) : (
                      <p className="text-sm text-muted-foreground">No plot threads tracked yet</p>
                    )}
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="space-y-3">
                  <h4 className="font-medium">Quick Reference</h4>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="p-3 bg-muted rounded">
                      <p className="text-muted-foreground">Characters</p>
                      <p className="font-medium">{storyBible?.characters?.length || 0}</p>
                    </div>
                    <div className="p-3 bg-muted rounded">
                      <p className="text-muted-foreground">Active Plots</p>
                      <p className="font-medium">
                        {storyBible?.plotThreads?.filter(t => t.status === 'active').length || 0}
                      </p>
                    </div>
                    <div className="p-3 bg-muted rounded">
                      <p className="text-muted-foreground">World Rules</p>
                      <p className="font-medium">{Object.keys(storyBible?.worldRules || {}).length}</p>
                    </div>
                    <div className="p-3 bg-muted rounded">
                      <p className="text-muted-foreground">Events</p>
                      <p className="font-medium">{storyBible?.timeline?.length || 0}</p>
                    </div>
                  </div>
                </div>
              </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}