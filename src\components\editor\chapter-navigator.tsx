'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import { useEditorStore } from '@/stores/editor-store'
import { 
  ChevronLeft, 
  ChevronRight, 
  Plus, 
  FileText, 
  CheckCircle, 
  Circle, 
  Edit3,
  Search
} from 'lucide-react'

interface ChapterNavigatorProps {
  currentChapterId?: string
  onChapterSelect: (chapterId: string, chapterNumber: number) => void
  onCreateChapter: () => void
}

export function ChapterNavigator({ 
  currentChapterId, 
  onChapterSelect,
  onCreateChapter 
}: ChapterNavigatorProps) {
  const { 
    showChapterNavigator, 
    chapters
  } = useEditorStore()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [isCollapsed, setIsCollapsed] = useState(false)

  const filteredChapters = chapters.filter(chapter => 
    chapter.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chapter.number.toString().includes(searchTerm)
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'writing':
        return <Edit3 className="h-4 w-4 text-blue-600" />
      case 'review':
        return <Circle className="h-4 w-4 text-orange-600" />
      default:
        return <Circle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'complete': return 'Complete'
      case 'writing': return 'Writing'
      case 'review': return 'Review'
      default: return 'Planned'
    }
  }

  if (!showChapterNavigator) return null

  return (
    <Card className={`h-full transition-all duration-300 ${isCollapsed ? 'w-12' : 'w-80'}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <CardTitle className="text-lg flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Chapters
            </CardTitle>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>

      {!isCollapsed && (
        <CardContent className="flex-1 flex flex-col p-0">
          {/* Search */}
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search chapters..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Chapter List */}
          <ScrollArea className="flex-1">
            <div className="p-4 space-y-2">
              {filteredChapters.map((chapter) => (
                <Button
                  key={chapter.id}
                  variant={currentChapterId === chapter.id ? "default" : "ghost"}
                  className="w-full justify-start p-3 h-auto"
                  onClick={() => onChapterSelect(chapter.id, chapter.number)}
                >
                  <div className="flex items-start gap-3 w-full">
                    <div className="flex-shrink-0 mt-0.5">
                      {getStatusIcon(chapter.status)}
                    </div>
                    
                    <div className="flex-1 text-left space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">
                          Chapter {chapter.number}
                        </span>
                        <span className="text-xs px-2 py-0.5 bg-muted rounded">
                          {getStatusLabel(chapter.status)}
                        </span>
                      </div>
                      
                      <p className="text-sm font-normal line-clamp-2">
                        {chapter.title || `Chapter ${chapter.number}`}
                      </p>
                      
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{chapter.wordCount.toLocaleString()} words</span>
                      </div>
                    </div>
                  </div>
                </Button>
              ))}

              {filteredChapters.length === 0 && searchTerm && (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No chapters found</p>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Add Chapter Button */}
          <div className="p-4 border-t">
            <Button
              onClick={onCreateChapter}
              className="w-full"
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Chapter
            </Button>
          </div>

          {/* Quick Stats */}
          <div className="p-4 border-t bg-muted/50">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Total Chapters</p>
                <p className="font-medium">{chapters.length}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Total Words</p>
                <p className="font-medium">
                  {chapters.reduce((sum, ch) => sum + ch.wordCount, 0).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Completed</p>
                <p className="font-medium">
                  {chapters.filter(ch => ch.status === 'complete').length}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">In Progress</p>
                <p className="font-medium">
                  {chapters.filter(ch => ch.status === 'writing').length}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}