# Vercel Deployment Guide for BookScribe

> **Note**: This guide follows a simplicity-first approach. All routes use the standard Node.js runtime for maximum compatibility and ease of maintenance.

## Prerequisites

1. Vercel account (Pro recommended for 5-minute function timeouts)
2. GitHub repository connected to Vercel
3. Environment variables ready

## Environment Variables

Set these in your Vercel dashboard under Project Settings > Environment Variables:

### Required Variables

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Stripe (if using payments)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NODE_OPTIONS=--max-old-space-size=4096
```

## Deployment Steps

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Add Vercel configuration"
   git push origin main
   ```

2. **Import to Vercel**
   - Go to https://vercel.com/new
   - Import your GitHub repository
   - Select "Next.js" as framework (auto-detected)
   - Add environment variables
   - Deploy

3. **Configure Custom Domain** (optional)
   - Go to Project Settings > Domains
   - Add your custom domain
   - Update DNS records as instructed

## Function Timeouts

The `vercel.json` configures extended timeouts for AI-heavy operations:

- **5 minutes (300s)**: AI content generation, analysis routes
- **2 minutes (120s)**: Bulk operations, exports
- **1 minute (60s)**: Search and service management

**Note**: 5-minute timeouts require Vercel Pro plan. On free/hobby plan, max is 10 seconds.

## Post-Deployment

1. **Test Critical Paths**
   - Authentication flow
   - AI content generation
   - Project creation and management
   - Export functionality

2. **Monitor Functions**
   - Check Vercel dashboard for function logs
   - Monitor for timeout errors
   - Review function usage metrics

3. **Update Supabase**
   - Add production URL to Supabase auth redirect whitelist
   - Update any webhooks to use production URL

## Troubleshooting

### Build Failures
- Check build logs in Vercel dashboard
- Ensure all environment variables are set
- Verify Node.js version compatibility

### Function Timeouts
- Upgrade to Pro plan for longer timeouts
- Optimize long-running operations
- Consider background jobs for very long tasks

### ServiceManager Issues
- Services initialize via instrumentation.ts
- Check function logs for initialization errors
- Ensure OpenAI API key is valid

## Performance Optimization

1. **Use ISR for Static Content**
   ```typescript
   export const revalidate = 3600; // Revalidate every hour
   ```

2. **Optimize Images**
   - Use next/image component
   - Configure image domains in next.config.js

3. **Enable Caching**
   - API routes can return cache headers
   - Use Vercel Edge Cache for static assets

## Monitoring

1. **Vercel Analytics** (built-in)
   - Page load performance
   - Web vitals
   - Visitor analytics

2. **Function Logs**
   - Real-time logs in dashboard
   - Error tracking
   - Performance metrics

3. **Alerts**
   - Set up alerts for function errors
   - Monitor API rate limits
   - Track usage quotas