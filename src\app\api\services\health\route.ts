import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import ServiceManager from '@/lib/services/service-manager';
import { authenticateAdmin, handleRouteError } from '@/lib/auth';
import { adminLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter';

export async function GET(request: NextRequest) {
  try {
    // Rate limiting for health checks
    const clientIP = getClientIP(request);
    const rateLimitResult = adminLimiter.check(20, clientIP); // 20 requests per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Admin authentication required for service health information
    const adminAuth = await authenticateAdmin();
    if (!adminAuth.success) {
      return adminAuth.response!;
    }
    const serviceManager = ServiceManager.getInstance();
    
    // Ensure services are initialized
    await serviceManager.initialize();
    
    const healthStatus = await serviceManager.healthCheck();
    const serviceStatus = serviceManager.getServiceStatus();
    
    const overall = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: serviceStatus,
      health: healthStatus,
      summary: {
        active: serviceManager.getActiveServices().length,
        inactive: serviceManager.getInactiveServices().length,
        total: Object.keys(serviceStatus).length
      }
    };

    // Determine overall health
    const hasUnhealthyServices = Object.values(healthStatus).some(
      (health: Record<string, unknown>) => !health.success
    );
    
    if (hasUnhealthyServices) {
      overall.status = 'degraded';
    }

    return NextResponse.json(overall);
  } catch (error) {
    return handleRouteError(error, 'Service Health GET');
  }
}

export async function POST(request: NextRequest) {
  try {
    // Strict rate limiting for service management operations (very dangerous)
    const clientIP = getClientIP(request);
    const rateLimitResult = adminLimiter.check(5, clientIP); // Only 5 requests per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Admin authentication required for service management
    const adminAuth = await authenticateAdmin();
    if (!adminAuth.success) {
      return adminAuth.response!;
    }
    const { action, service } = await request.json();
    const serviceManager = ServiceManager.getInstance();

    switch (action) {
      case 'restart':
        if (!service) {
          return NextResponse.json(
            { error: 'Service name required for restart' },
            { status: 400 }
          );
        }
        
        const restarted = await serviceManager.restartService(service);
        return NextResponse.json({ success: restarted });

      case 'initialize':
        await serviceManager.initialize();
        return NextResponse.json({ success: true });

      case 'shutdown':
        await serviceManager.shutdown();
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    return handleRouteError(error, 'Service Health POST');
  }
}