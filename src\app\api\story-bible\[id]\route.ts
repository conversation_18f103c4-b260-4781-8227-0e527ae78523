import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get story bible entry with project ownership check
    const { data: entry, error } = await supabase
      .from('story_bible')
      .select(`
        *,
        projects!inner (
          id,
          title,
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Story bible entry not found' }, { status: 404 })
      }
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Clean up the response to remove nested project data
    const { projects: _, ...cleanEntry } = entry
    // Suppress unused variable warning for intentionally unused destructured property
    void _

    return NextResponse.json({ entry: cleanEntry })

  } catch (error) {
    console.error('Error fetching story bible entry:', error)
    return NextResponse.json(
      { error: 'Failed to fetch story bible entry' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First verify ownership through project
    const { data: entry } = await supabase
      .from('story_bible')
      .select(`
        id,
        entry_key,
        entry_type,
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (!entry) {
      return NextResponse.json({ error: 'Story bible entry not found' }, { status: 404 })
    }

    // Delete entry
    const { error } = await supabase
      .from('story_bible')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting story bible entry:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: `Story bible entry "${entry.entry_key}" (${entry.entry_type}) deleted successfully`
    })

  } catch (error) {
    console.error('Error deleting story bible entry:', error)
    return NextResponse.json(
      { error: 'Failed to delete story bible entry' },
      { status: 500 }
    )
  }
}