// Type definitions for Supabase realtime subscriptions

import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js';

export interface CollaborationParticipant {
  session_id: string;
  user_id: string;
  status: 'online' | 'offline';
  cursor_position?: { line: number; column: number };
  role: 'owner' | 'editor' | 'viewer' | 'commenter';
}

export interface CollaborationChange {
  id: string;
  session_id: string;
  user_id: string;
  change_type: 'insert' | 'delete' | 'format';
  position: { line: number; column: number };
  content?: string;
  length?: number;
  timestamp: string;
}

export interface CollaborationLock {
  session_id: string;
  section: string;
  user_id: string;
  locked_at: string;
}

export interface CollaborationSession {
  id: string;
  document_content: string;
  document_version: number;
  created_at: string;
  updated_at: string;
}

export type CollaborationParticipantPayload = RealtimePostgresChangesPayload<CollaborationParticipant>;
export type CollaborationChangePayload = RealtimePostgresChangesPayload<CollaborationChange>;
export type CollaborationLockPayload = RealtimePostgresChangesPayload<CollaborationLock>;
export type CollaborationSessionPayload = RealtimePostgresChangesPayload<CollaborationSession>;