import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'

const inviteCollaboratorSchema = z.object({
  projectId: z.string().uuid(),
  email: z.string().email(),
  role: z.enum(['viewer', 'commenter', 'editor', 'admin']),
  message: z.string().optional(),
})


export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const status = searchParams.get('status')

    if (!projectId) {
      // Get all invitations for the current user
      let query = supabase
        .from('project_collaborators')
        .select('*, projects(title, description), users!project_collaborators_invited_by_fkey(email)')
        .eq('user_email', user.email!)
        .order('created_at', { ascending: false })

      if (status) {
        query = query.eq('status', status)
      }

      const { data: invitations, error } = await query

      if (error) {
        console.error('Error fetching invitations:', error)
        return NextResponse.json({ error: 'Failed to fetch invitations' }, { status: 500 })
      }

      return NextResponse.json({ invitations: invitations || [] })
    } else {
      // Get collaborators for a specific project
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('user_id')
        .eq('id', projectId)
        .single()

      if (projectError || !project) {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 })
      }

      // Check if user is the owner or a collaborator
      const isOwner = project.user_id === user.id
      
      if (!isOwner) {
        const { data: collaborator } = await supabase
          .from('project_collaborators')
          .select('role')
          .eq('project_id', projectId)
          .eq('user_email', user.email!)
          .eq('status', 'accepted')
          .single()

        if (!collaborator) {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
        }
      }

      const { data: collaborators, error } = await supabase
        .from('project_collaborators')
        .select('*, users!project_collaborators_user_id_fkey(email, full_name)')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching collaborators:', error)
        return NextResponse.json({ error: 'Failed to fetch collaborators' }, { status: 500 })
      }

      return NextResponse.json({ 
        collaborators: collaborators || [],
        isOwner 
      })
    }
  } catch (error) {
    console.error('Error in project collaborators GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { projectId, email, role, message } = inviteCollaboratorSchema.parse(body)

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found or unauthorized' }, { status: 404 })
    }

    // Check if user is inviting themselves
    if (email === user.email) {
      return NextResponse.json({ error: 'Cannot invite yourself' }, { status: 400 })
    }

    // Check if invitation already exists
    const { data: existing } = await supabase
      .from('project_collaborators')
      .select('id, status')
      .eq('project_id', projectId)
      .eq('user_email', email)
      .single()

    if (existing) {
      if (existing.status === 'accepted') {
        return NextResponse.json({ error: 'User is already a collaborator' }, { status: 400 })
      }
      return NextResponse.json({ error: 'Invitation already sent' }, { status: 400 })
    }

    // Find the invited user's ID if they exist
    const { data: invitedUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single()

    // Create the invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('project_collaborators')
      .insert({
        project_id: projectId,
        user_id: invitedUser?.id,
        user_email: email,
        role,
        status: 'pending',
        invited_by: user.id,
        invitation_message: message,
      })
      .select()
      .single()

    if (inviteError) {
      console.error('Error creating invitation:', inviteError)
      return NextResponse.json({ error: 'Failed to create invitation' }, { status: 500 })
    }

    // Create notification for the invited user if they exist
    if (invitedUser) {
      await supabase
        .from('notifications')
        .insert({
          user_id: invitedUser.id,
          type: 'collaboration_invite',
          title: 'Project Collaboration Invitation',
          message: `You've been invited to collaborate on "${project.title}"`,
          data: { 
            project_id: projectId,
            invitation_id: invitation.id,
            role 
          },
        })
    }

    // TODO: Send email invitation if user doesn't exist in the system

    return NextResponse.json({ invitation })
  } catch (error) {
    console.error('Error in project collaborators POST:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 })
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const collaboratorId = searchParams.get('id')

    if (!collaboratorId) {
      return NextResponse.json({ error: 'Collaborator ID required' }, { status: 400 })
    }

    // Get the collaboration details
    const { data: collaboration, error: fetchError } = await supabase
      .from('project_collaborators')
      .select('project_id, user_email, projects(user_id)')
      .eq('id', collaboratorId)
      .single()

    if (fetchError || !collaboration) {
      return NextResponse.json({ error: 'Collaboration not found' }, { status: 404 })
    }

    // Check if user is the project owner or the collaborator themselves
    const projectData = collaboration.projects as { user_id: string } | { user_id: string }[]
    const isOwner = Array.isArray(projectData) ? projectData[0]?.user_id === user.id : projectData?.user_id === user.id
    const isSelf = collaboration.user_email === user.email

    if (!isOwner && !isSelf) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Delete the collaboration
    const { error: deleteError } = await supabase
      .from('project_collaborators')
      .delete()
      .eq('id', collaboratorId)

    if (deleteError) {
      console.error('Error deleting collaboration:', deleteError)
      return NextResponse.json({ error: 'Failed to delete collaboration' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: isSelf ? 'Left project successfully' : 'Collaborator removed' 
    })
  } catch (error) {
    console.error('Error in project collaborators DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}