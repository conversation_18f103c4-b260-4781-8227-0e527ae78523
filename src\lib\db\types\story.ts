// Story structure and plot-related type definitions

export interface StoryStructure {
  acts?: Array<{
    number: number
    description: string
    chapters: number[]
    key_events: string[]
  }>
  plot_points?: Array<{
    type: 'inciting_incident' | 'plot_point_1' | 'midpoint' | 'plot_point_2' | 'climax' | 'resolution'
    chapter: number
    description: string
  }>
  world_building?: {
    setting: string
    rules: string[]
    locations: Array<{ name: string; description: string }>
  }
  timeline?: Array<{
    date: string
    event: string
    chapter?: number
  }>
}

export interface ChapterScenes {
  scenes: Array<{
    number: number
    setting: string
    characters: string[]
    objectives: string[]
    conflicts: string[]
    resolutions: string[]
  }>
}

export interface PlotAdvancement {
  main_plot: {
    threads: string[]
    advancement: string
    conflicts_introduced: string[]
    conflicts_resolved: string[]
  }
  subplots: Array<{
    name: string
    advancement: string
    status: 'active' | 'resolved' | 'paused'
  }>
}

export interface AiAnalysis {
  objectives?: string[]
  conflicts?: string[]
  resolutions?: string[]
  notes?: string[]
  quality_score?: number
  suggestions?: string[]
}

// Type aliases for clarity
export type StoryStructureData = StoryStructure
export type ChapterScenesData = ChapterScenes
export type PlotAdvancementData = PlotAdvancement
export type AiAnalysisData = AiAnalysis