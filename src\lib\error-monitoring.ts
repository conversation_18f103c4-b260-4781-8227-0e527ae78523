/**
 * Comprehensive Error Monitoring System
 * 
 * Provides centralized error tracking, performance monitoring,
 * and user experience insights for production deployment.
 */

import { config } from '@/lib/config';

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  timestamp?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'security' | 'performance' | 'business_logic' | 'user_experience' | 'system';
  metadata?: Record<string, unknown>;
}

export interface PerformanceMetrics {
  responseTime: number;
  memoryUsage?: number;
  cpuUsage?: number;
  databaseQueryTime?: number;
  externalApiTime?: number;
}

class ErrorMonitoringService {
  private static instance: ErrorMonitoringService;
  private isEnabled: boolean;
  private environment: string;

  constructor() {
    this.isEnabled = config.isProduction;
    this.environment = config.env.NODE_ENV;
  }

  static getInstance(): ErrorMonitoringService {
    if (!ErrorMonitoringService.instance) {
      ErrorMonitoringService.instance = new ErrorMonitoringService();
    }
    return ErrorMonitoringService.instance;
  }

  /**
   * Log application errors with context
   */
  async logError(error: Error | string, context: ErrorContext): Promise<void> {
    if (!this.isEnabled) {
      console.error('Error logged:', error, context);
      return;
    }

    const errorData = {
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      name: typeof error === 'object' ? error.name : 'UnknownError',
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        environment: this.environment,
      },
    };

    try {
      // In production, you would send to external monitoring service
      // Examples: Sentry, LogRocket, DataDog, etc.
      console.error('Production Error:', errorData);
      
      // For now, we'll store in browser localStorage for development
      if (typeof window !== 'undefined') {
        const errors = JSON.parse(localStorage.getItem('app_errors') || '[]');
        errors.push(errorData);
        // Keep only last 100 errors
        localStorage.setItem('app_errors', JSON.stringify(errors.slice(-100)));
      }
    } catch (loggingError) {
      console.error('Failed to log error:', loggingError);
    }
  }

  /**
   * Log performance metrics
   */
  async logPerformance(operation: string, metrics: PerformanceMetrics): Promise<void> {
    if (!this.isEnabled) {
      console.log('Performance logged:', operation, metrics);
      return;
    }

    const performanceData = {
      operation,
      metrics,
      timestamp: new Date().toISOString(),
      environment: this.environment,
    };

    try {
      // In production, send to monitoring service
      console.log('Performance Metric:', performanceData);
      
      if (typeof window !== 'undefined') {
        const metrics = JSON.parse(localStorage.getItem('app_performance') || '[]');
        metrics.push(performanceData);
        localStorage.setItem('app_performance', JSON.stringify(metrics.slice(-50)));
      }
    } catch (error) {
      console.error('Failed to log performance metric:', error);
    }
  }

  /**
   * Log security incidents
   */
  async logSecurityIncident(incident: string, context: Omit<ErrorContext, 'category' | 'severity'>): Promise<void> {
    await this.logError(incident, {
      ...context,
      category: 'security',
      severity: 'critical',
    });
  }

  /**
   * Log user experience issues
   */
  async logUserExperience(issue: string, context: Omit<ErrorContext, 'category'>): Promise<void> {
    await this.logError(issue, {
      ...context,
      category: 'user_experience',
    });
  }
}

// Global error handler for unhandled errors
if (typeof window !== 'undefined') {
  const monitor = ErrorMonitoringService.getInstance();

  window.addEventListener('error', (event) => {
    monitor.logError(event.error || event.message, {
      severity: 'high',
      category: 'system',
      url: window.location.href,
      userAgent: navigator.userAgent,
      metadata: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      },
    });
  });

  window.addEventListener('unhandledrejection', (event) => {
    monitor.logError(`Unhandled Promise Rejection: ${event.reason}`, {
      severity: 'high',
      category: 'system',
      url: window.location.href,
      userAgent: navigator.userAgent,
    });
  });
}

// Performance monitoring wrapper
export function withPerformanceMonitoring<T extends (...args: unknown[]) => unknown>(
  operation: string,
  fn: T
): T {
  return ((...args: Parameters<T>) => {
    const startTime = performance.now();
    const monitor = ErrorMonitoringService.getInstance();

    try {
      const result = fn(...args);

      // Handle both sync and async functions
      if (result instanceof Promise) {
        return result
          .then((value) => {
            const endTime = performance.now();
            monitor.logPerformance(operation, {
              responseTime: endTime - startTime,
            });
            return value;
          })
          .catch((error) => {
            const endTime = performance.now();
            monitor.logError(error, {
              severity: 'medium',
              category: 'performance',
              metadata: { operation, responseTime: endTime - startTime },
            });
            throw error;
          });
      } else {
        const endTime = performance.now();
        monitor.logPerformance(operation, {
          responseTime: endTime - startTime,
        });
        return result;
      }
    } catch (error) {
      const endTime = performance.now();
      monitor.logError(error as Error, {
        severity: 'medium',
        category: 'business_logic',
        metadata: { operation, responseTime: endTime - startTime },
      });
      throw error;
    }
  }) as T;
}

// Export singleton instance
export const errorMonitor = ErrorMonitoringService.getInstance();

// Utility functions for common use cases
export const logError = (error: Error | string, context: Partial<ErrorContext> = {}) => {
  return errorMonitor.logError(error, {
    severity: 'medium',
    category: 'business_logic',
    ...context,
  });
};

export const logSecurityIncident = (incident: string, context: Partial<ErrorContext> = {}) => {
  return errorMonitor.logSecurityIncident(incident, context);
};

export const logUserExperience = (issue: string, context: Partial<ErrorContext> = {}) => {
  return errorMonitor.logUserExperience(issue, {
    severity: 'low',
    ...context,
  });
};

export const logPerformance = (operation: string, metrics: PerformanceMetrics) => {
  return errorMonitor.logPerformance(operation, metrics);
};

export default errorMonitor;