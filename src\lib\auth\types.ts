import { NextResponse } from 'next/server'
import type { User } from '@supabase/supabase-js'
import { createClient } from '@/lib/supabase/server'

export interface AuthResult {
  success: boolean
  user?: User
  supabase?: Awaited<ReturnType<typeof createClient>>
  response?: NextResponse
}

export interface OwnershipResult {
  success: boolean
  response?: NextResponse
  data?: unknown
}

export interface AuthError {
  message: string
  status: number
}

export const AUTH_ERRORS = {
  UNAUTHORIZED: { message: 'Unauthorized', status: 401 },
  FORBIDDEN: { message: 'Access forbidden', status: 403 },
  NOT_FOUND: { message: 'Resource not found', status: 404 },
  INVALID_REQUEST: { message: 'Invalid request', status: 400 },
  SERVER_ERROR: { message: 'Internal server error', status: 500 }
} as const

export type AuthErrorType = keyof typeof AUTH_ERRORS