'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useWizardStore } from '@/stores/wizard-store'

export function StepTechnical() {
  const { selections, updateSelections } = useWizardStore()
  
  const chapterCountTypes = [
    { value: 'fixed', label: 'Fixed Number' },
    { value: 'flexible', label: 'Flexible Range' },
    { value: 'scene_based', label: 'Scene-Based Division' }
  ]
  
  const povCharacterTypes = [
    { value: 'single', label: 'Single POV' },
    { value: 'dual', label: 'Dual POV' },
    { value: 'multiple', label: 'Multiple POV (3-5)' },
    { value: 'ensemble', label: 'Ensemble Cast (6+)' }
  ]
  
  const researchNeeds = [
    'Historical Accuracy',
    'Scientific Accuracy',
    'Cultural Authenticity',
    'Technical Expertise',
    'Legal/Medical Research',
    'Geographic Research',
    'Language/Dialect Research',
    'Military/Warfare Research'
  ]
  
  const factCheckingLevels = [
    { value: 'minimal', label: 'Minimal' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'extensive', label: 'Extensive' },
    { value: 'expert_review', label: 'Expert Review Required' }
  ]
  
  const handleArrayToggle = (array: string[], item: string, key: keyof typeof selections) => {
    const currentArray = array || []
    const newArray = currentArray.includes(item)
      ? currentArray.filter(i => i !== item)
      : [...currentArray, item]
    updateSelections({ [key]: newArray })
  }
  
  const getEstimatedTimeframe = () => {
    const wordCount = selections.targetWordCount || 80000
    const wordsPerDay = 1000 // Assuming 1000 words per day
    const days = Math.ceil(wordCount / wordsPerDay)
    const weeks = Math.ceil(days / 7)
    const months = Math.ceil(weeks / 4)
    
    if (months < 3) {
      return `${weeks} weeks`
    } else {
      return `${months} months`
    }
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Technical Specifications</CardTitle>
          <CardDescription>
            Configure the technical aspects of your novel structure.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="targetWordCount">Target Word Count</Label>
              <Input
                id="targetWordCount"
                type="number"
                min="1000"
                max="500000"
                value={selections.targetWordCount || ''}
                onChange={(e) => updateSelections({ targetWordCount: parseInt(e.target.value) || 80000 })}
                placeholder="80000"
              />
              <p className="text-xs text-muted-foreground">
                Standard novel: 70k-100k words
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetChapters">Target Chapters</Label>
              <Input
                id="targetChapters"
                type="number"
                min="1"
                max="100"
                value={selections.targetChapters || ''}
                onChange={(e) => updateSelections({ targetChapters: parseInt(e.target.value) || 20 })}
                placeholder="20"
              />
              <p className="text-xs text-muted-foreground">
                Average: 3k-5k words per chapter
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Chapter Count Type</Label>
            <Select 
              value={selections.chapterCountType} 
              onValueChange={(value) => updateSelections({ chapterCountType: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select chapter count approach" />
              </SelectTrigger>
              <SelectContent>
                {chapterCountTypes.map(({ value, label }) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="povCharacterCount">POV Character Count</Label>
              <Input
                id="povCharacterCount"
                type="number"
                min="1"
                max="10"
                value={selections.povCharacterCount || ''}
                onChange={(e) => updateSelections({ povCharacterCount: parseInt(e.target.value) || 1 })}
                placeholder="1"
              />
            </div>

            <div className="space-y-2">
              <Label>POV Character Type</Label>
              <Select 
                value={selections.povCharacterType} 
                onValueChange={(value) => updateSelections({ povCharacterType: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select POV structure" />
                </SelectTrigger>
                <SelectContent>
                  {povCharacterTypes.map(({ value, label }) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Research & References</CardTitle>
          <CardDescription>
            Specify any research requirements and accuracy needs for your story.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Research Needs (select all that apply)</Label>
            <div className="grid grid-cols-2 gap-2">
              {researchNeeds.map((need) => (
                <div key={need} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`research-${need}`}
                    checked={selections.researchNeeds?.includes(need) || false}
                    onCheckedChange={() => 
                      handleArrayToggle(selections.researchNeeds || [], need, 'researchNeeds')
                    }
                  />
                  <Label htmlFor={`research-${need}`} className="text-sm font-normal">
                    {need}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Fact-Checking Level</Label>
            <Select 
              value={selections.factCheckingLevel} 
              onValueChange={(value) => updateSelections({ factCheckingLevel: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select fact-checking requirement" />
              </SelectTrigger>
              <SelectContent>
                {factCheckingLevels.map(({ value, label }) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customResearchNotes">Custom Research Notes (Optional)</Label>
            <Textarea
              id="customResearchNotes"
              placeholder="Describe specific research requirements, expert consultation needs, or reference materials..."
              value={selections.customResearchNotes || ''}
              onChange={(e) => updateSelections({ customResearchNotes: e.target.value })}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Project Estimation</CardTitle>
          <CardDescription>
            Based on your specifications, here&apos;s an estimated timeline and scope.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-3">
              <div>
                <Label className="text-sm font-medium">Estimated Writing Time</Label>
                <p className="text-2xl font-bold text-primary">{getEstimatedTimeframe()}</p>
                <p className="text-xs text-muted-foreground">At 1,000 words/day</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Average Chapter Length</Label>
                <p className="text-lg font-semibold">
                  {selections.targetWordCount && selections.targetChapters 
                    ? Math.round((selections.targetWordCount || 80000) / (selections.targetChapters || 20)).toLocaleString()
                    : '4,000'
                  } words
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <Label className="text-sm font-medium">Project Complexity</Label>
                <p className="text-lg font-semibold">
                  {(() => {
                    let complexity = 0
                    if ((selections.researchNeeds?.length || 0) > 3) complexity += 1
                    if (selections.factCheckingLevel === 'extensive' || selections.factCheckingLevel === 'expert_review') complexity += 1
                    if ((selections.povCharacterCount || 1) > 2) complexity += 1
                    if (selections.timelineComplexity === 'multiple_timelines' || selections.timelineComplexity === 'non_linear') complexity += 1
                    
                    if (complexity === 0) return 'Simple'
                    if (complexity <= 2) return 'Moderate'
                    return 'Complex'
                  })()}
                </p>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Scope Category</Label>
                <p className="text-lg font-semibold">
                  {(() => {
                    const wordCount = selections.targetWordCount || 80000
                    if (wordCount < 60000) return 'Short Novel'
                    if (wordCount < 100000) return 'Standard Novel'
                    if (wordCount < 150000) return 'Long Novel'
                    return 'Epic Novel'
                  })()}
                </p>
              </div>
            </div>
          </div>

          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium mb-2">Final Configuration Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Word Count:</strong> {(selections.targetWordCount || 80000).toLocaleString()}
              </div>
              <div>
                <strong>Chapters:</strong> {selections.targetChapters || 20}
              </div>
              <div>
                <strong>POV Characters:</strong> {selections.povCharacterCount || 1}
              </div>
              <div>
                <strong>Structure:</strong> {selections.structureType || 'Not specified'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}