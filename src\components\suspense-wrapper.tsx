import { Suspense, type ReactNode } from 'react'
import { PageLoader, SectionLoader, CardSkeleton } from '@/components/ui/loading-states'

interface SuspenseWrapperProps {
  children: ReactNode
  fallback?: ReactNode
  type?: 'page' | 'section' | 'card' | 'custom'
  message?: string
}

export function SuspenseWrapper({
  children,
  fallback,
  type = 'section',
  message
}: SuspenseWrapperProps) {
  const getFallback = () => {
    if (fallback) return fallback
    
    switch (type) {
      case 'page':
        return <PageLoader message={message} />
      case 'section':
        return <SectionLoader />
      case 'card':
        return <CardSkeleton />
      default:
        return <SectionLoader />
    }
  }
  
  return <Suspense fallback={getFallback()}>{children}</Suspense>
}