'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Save } from 'lucide-react';
import type { ProjectSettings } from '@/lib/types/project-settings';
import type { SelectionProfile } from '@/lib/db/types';
import { supabase as createSupabaseClient } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';

interface ProfileSelectorProps {
  onProfileSelect: (profile: Partial<ProjectSettings>) => void;
  currentSettings: Partial<ProjectSettings>;
}

export function ProfileSelector({ onProfileSelect, currentSettings }: ProfileSelectorProps) {
  const [profiles, setProfiles] = useState<SelectionProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [profileName, setProfileName] = useState('');
  const [profileDescription, setProfileDescription] = useState('');
  const [isPublic, setIsPublic] = useState(false);
  const { toast } = useToast();
  const supabase = createSupabaseClient();

  const loadProfiles = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('selection_profiles')
        .select('*')
        .or(`user_id.eq.${user.id},is_public.eq.true`)
        .order('usage_count', { ascending: false });

      if (error) throw error;
      setProfiles(data || []);
    } catch (_error) {
      console.error('Error loading profiles:', _error);
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    loadProfiles();
  }, [loadProfiles]);

  const saveCurrentAsProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const profileData = {
        user_id: user.id,
        name: profileName,
        description: profileDescription,
        is_public: isPublic,
        ...currentSettings,
      };

      const { error } = await supabase
        .from('selection_profiles')
        .insert(profileData);

      if (error) throw error;

      toast({
        title: 'Profile saved!',
        description: 'Your selection profile has been saved for future use.',
      });

      setShowSaveDialog(false);
      setProfileName('');
      setProfileDescription('');
      setIsPublic(false);
      loadProfiles();
    } catch {
      toast({
        title: 'Error saving profile',
        description: 'Please try again.',
        variant: 'destructive',
      });
    }
  };

  const applyProfile = async (profile: SelectionProfile) => {
    try {
      // Increment usage count
      await supabase
        .from('selection_profiles')
        .update({ usage_count: profile.usage_count + 1 })
        .eq('id', profile.id);

      // Extract settings from profile
      const profileSettings: Partial<ProjectSettings> = {
        primaryGenre: profile.primary_genre as any,
        subgenre: profile.subgenre || undefined,
        customGenre: profile.custom_genre || undefined,
        narrativeVoice: profile.narrative_voice as any,
        tense: profile.tense as any,
        tone: profile.tone_options as any,
        writingStyle: profile.writing_style as any,
        customStyleDescription: profile.custom_style_description || undefined,
        structureType: profile.structure_type as any,
        pacingPreference: profile.pacing_preference as any,
        chapterStructure: profile.chapter_structure as any,
        timelineComplexity: profile.timeline_complexity as any,
        customStructureNotes: profile.custom_structure_notes || undefined,
        protagonistTypes: profile.protagonist_types as any,
        antagonistTypes: profile.antagonist_types as any,
        characterComplexity: profile.character_complexity as any,
        characterArcTypes: profile.character_arc_types as any,
        customCharacterConcepts: profile.custom_character_concepts || undefined,
        timePeriod: profile.time_period as any,
        geographicSetting: profile.geographic_setting as any,
        worldType: profile.world_type as any,
        magicTechLevel: profile.magic_tech_level as any,
        customSettingDescription: profile.custom_setting_description || undefined,
        majorThemes: profile.major_themes as any,
        philosophicalThemes: profile.philosophical_themes as any,
        socialThemes: profile.social_themes as any,
        customThemes: profile.custom_themes || undefined,
        targetAudience: profile.target_audience as any,
        contentRating: profile.content_rating as any,
        contentWarnings: profile.content_warnings as any,
        culturalSensitivityNotes: profile.cultural_sensitivity_notes || undefined,
        projectScope: profile.project_scope as any,
        seriesType: profile.series_type as any,
        interconnectionLevel: profile.interconnection_level as any,
        customScopeDescription: profile.custom_scope_description || undefined,
        targetWordCount: profile.target_word_count || undefined,
        chapterCountType: profile.chapter_count_type as any,
        povCharacterCount: profile.pov_character_count || undefined,
        povCharacterType: profile.pov_character_type as any,
        researchNeeds: profile.research_needs as any,
        factCheckingLevel: profile.fact_checking_level as any,
        customResearchNotes: profile.custom_research_notes || undefined,
      };

      onProfileSelect(profileSettings);

      toast({
        title: 'Profile applied!',
        description: `Applied settings from "${profile.name}".`,
      });
    } catch {
      toast({
        title: 'Error applying profile',
        description: 'Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return <div className="animate-pulse">Loading profiles...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Selection Profiles</h3>
          <p className="text-sm text-slate-600 dark:text-slate-400">
            Use saved configurations or save your current settings
          </p>
        </div>
        
        <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Save className="w-4 h-4 mr-2" />
              Save Current
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Save Selection Profile</DialogTitle>
              <DialogDescription>
                Save your current settings as a reusable profile
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="profileName">Profile Name</Label>
                <Input
                  id="profileName"
                  value={profileName}
                  onChange={(e) => setProfileName(e.target.value)}
                  placeholder="e.g., Epic Fantasy Template"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="profileDescription">Description</Label>
                <Textarea
                  id="profileDescription"
                  value={profileDescription}
                  onChange={(e) => setProfileDescription(e.target.value)}
                  placeholder="Describe when to use this profile..."
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label>Visibility</Label>
                <Select value={isPublic ? 'public' : 'private'} onValueChange={(v) => setIsPublic(v === 'public')}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="private">Private (only you can use)</SelectItem>
                    <SelectItem value="public">Public (share with community)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={saveCurrentAsProfile} disabled={!profileName}>
                  Save Profile
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {profiles.map((profile) => (
          <Card key={profile.id} className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-base">{profile.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {profile.description}
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  {profile.is_public && (
                    <Badge variant="outline" className="text-xs">Public</Badge>
                  )}
                  <Badge variant="secondary" className="text-xs">
                    Used {profile.usage_count} times
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div className="flex flex-wrap gap-1">
                  {profile.primary_genre && (
                    <Badge variant="outline" className="text-xs">
                      {profile.primary_genre}
                    </Badge>
                  )}
                  {profile.writing_style && (
                    <Badge variant="outline" className="text-xs">
                      {profile.writing_style}
                    </Badge>
                  )}
                  {profile.target_word_count && (
                    <Badge variant="outline" className="text-xs">
                      {profile.target_word_count.toLocaleString()} words
                    </Badge>
                  )}
                </div>
                <Button 
                  size="sm" 
                  className="w-full"
                  onClick={() => applyProfile(profile)}
                >
                  Use This Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
        
        {profiles.length === 0 && (
          <div className="col-span-2 text-center py-8 text-slate-500">
            No saved profiles yet. Create your first profile by configuring your settings and saving them.
          </div>
        )}
      </div>
    </div>
  );
}