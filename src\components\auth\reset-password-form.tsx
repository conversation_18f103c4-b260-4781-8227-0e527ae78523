'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { InputField } from '@/components/ui/form-field'
import { useFormValidation } from '@/hooks/use-form-validation'
import { resetPasswordSchema, type ResetPasswordFormData } from '@/lib/validation/auth-schemas'

export function ResetPasswordForm() {
  const [formData, setFormData] = useState<ResetPasswordFormData>({
    password: '',
    confirmPassword: '',
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const supabase = createClient()

  const {
    validateForm,
    handleFieldBlur,
    getFieldError,
    shouldShowFieldError,
  } = useFormValidation({
    schema: resetPasswordSchema,
    validateOnBlur: true,
  })

  useEffect(() => {
    // Check if we have the required tokens from the URL
    const accessToken = searchParams.get('access_token')
    const refreshToken = searchParams.get('refresh_token')
    
    if (!accessToken || !refreshToken) {
      setError('Invalid reset link. Please request a new password reset.')
    }
  }, [searchParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    const validation = await validateForm(formData)
    if (!validation.isValid) {
      return
    }

    setLoading(true)

    try {
      const { error } = await supabase.auth.updateUser({
        password: formData.password
      })
      
      if (error) throw error
      
      setSuccess(true)
      setTimeout(() => {
        router.push('/login')
      }, 2000)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Password updated successfully</CardTitle>
          <CardDescription>
            Your password has been reset. Redirecting to login...
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Set new password</CardTitle>
        <CardDescription>
          Enter your new password below
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <InputField
            id="password"
            label="New Password"
            type="password"
            placeholder="Must contain uppercase, lowercase, and number"
            value={formData.password}
            onChange={(value) => setFormData(prev => ({ ...prev, password: value as string }))}
            onBlur={() => handleFieldBlur('password', formData.password, formData)}
            error={shouldShowFieldError('password') ? getFieldError('password') : undefined}
            required
            disabled={loading}
          />
          
          <InputField
            id="confirmPassword"
            label="Confirm Password"
            type="password"
            placeholder="Re-enter your new password"
            value={formData.confirmPassword}
            onChange={(value) => setFormData(prev => ({ ...prev, confirmPassword: value as string }))}
            onBlur={() => handleFieldBlur('confirmPassword', formData.confirmPassword, formData)}
            error={shouldShowFieldError('confirmPassword') ? getFieldError('confirmPassword') : undefined}
            required
            disabled={loading}
          />
          
          {error && (
            <div className="rounded-lg border border-destructive/50 bg-destructive/10 p-3">
              <p className="text-sm text-destructive" role="alert">{error}</p>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? 'Updating...' : 'Update Password'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}