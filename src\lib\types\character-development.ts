// Character Development Grid Types

// Database types
export interface Character {
  id: string;
  project_id: string;
  name: string;
  role: string;
  description?: string;
  backstory?: string;
  personality_traits?: Record<string, unknown>;
  character_arc?: {
    type?: string;
    [key: string]: unknown;
  };
  relationships?: Record<string, unknown>;
  created_at?: string;
}

export interface Chapter {
  id: string;
  project_id: string;
  chapter_number: number;
  title?: string;
  target_word_count?: number;
  actual_word_count?: number;
  outline?: string;
  content?: string;
  status: string;
  ai_notes?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface DevelopmentGridData {
  characterId: string;
  characterName: string;
  dimensions: DevelopmentDimension[];
  chapters: ChapterDevelopment[];
}

export interface DevelopmentDimension {
  id: string;
  name: string;
  description: string;
  weight: number;
}

export interface ChapterDevelopment {
  chapter: number;
  dimensions: {
    [dimensionId: string]: {
      value: number; // -100 to 100 (regression to growth)
      events: string[];
      type: 'growth' | 'stagnant' | 'regression' | 'breakthrough';
    };
  };
}

export interface ArcPattern {
  type: string;
  name: string;
  confidence: number;
  matchedSegments: { start: number; end: number; }[];
  deviations: Deviation[];
  currentPhase?: string;
  predictedCompletion?: { minChapter: number; maxChapter: number; };
}

export interface Deviation {
  chapter: number;
  dimension: string;
  severity: 'minor' | 'moderate' | 'major';
  description: string;
  suggestion: string;
}

export interface ArcPrediction {
  characterId: string;
  currentTrajectory: 'positive' | 'negative' | 'stagnant' | 'volatile';
  completionLikelihood: number;
  suggestedInterventions: {
    dimension: string;
    chapters: number[];
    action: string;
    impact: 'high' | 'medium' | 'low';
  }[];
  riskFactors: string[];
}

// Predefined development dimensions
export const DEVELOPMENT_DIMENSIONS: DevelopmentDimension[] = [
  {
    id: 'emotional-growth',
    name: 'Emotional Growth',
    description: 'Changes in emotional maturity and control',
    weight: 1.0
  },
  {
    id: 'belief-system',
    name: 'Belief System',
    description: 'Evolution of core beliefs and values',
    weight: 0.9
  },
  {
    id: 'skill-progression',
    name: 'Skill Progression',
    description: 'Abilities and competencies gained/lost',
    weight: 0.8
  },
  {
    id: 'relationships',
    name: 'Relationship Dynamics',
    description: 'How character connections evolve',
    weight: 0.9
  },
  {
    id: 'goal-alignment',
    name: 'Goal Alignment',
    description: 'Shifts in motivations and objectives',
    weight: 0.85
  },
  {
    id: 'internal-conflict',
    name: 'Internal Conflict',
    description: 'Resolution or escalation of inner struggles',
    weight: 0.95
  },
  {
    id: 'external-agency',
    name: 'External Agency',
    description: "Character's ability to influence their world",
    weight: 0.8
  }
];

// Common arc patterns for recognition
export const ARC_PATTERNS = [
  {
    id: 'heros-journey',
    name: "Hero's Journey",
    phases: ['Ordinary World', 'Call to Adventure', 'Refusal', 'Meeting Mentor', 'Crossing Threshold', 'Trials', 'Revelation', 'Transformation', 'Return']
  },
  {
    id: 'tragic-fall',
    name: 'Tragic Fall',
    phases: ['Noble Beginning', 'Fatal Flaw Emerges', 'Initial Success', 'Warnings Ignored', 'Point of No Return', 'Downward Spiral', 'Tragic End']
  },
  {
    id: 'redemption-arc',
    name: 'Redemption Arc',
    phases: ['Fall from Grace', 'Rock Bottom', 'Catalyst for Change', 'Struggle', 'Breakthrough', 'Making Amends', 'New Purpose']
  },
  {
    id: 'coming-of-age',
    name: 'Coming of Age',
    phases: ['Innocence', 'First Challenge', 'Loss of Innocence', 'Finding Identity', 'Testing Values', 'Self-Discovery', 'Maturity']
  },
  {
    id: 'corruption-arc',
    name: 'Corruption Arc',
    phases: ['Initial Virtue', 'First Compromise', 'Justification', 'Escalation', 'Moral Decay', 'Point of Corruption', 'Embracing Darkness']
  }
];