'use client'

import { useState, useEffect, useCallback, memo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Lightbulb, 
  RefreshCw, 
  ThumbsDown, 
  Copy,
  Wand2,
  X
} from 'lucide-react'

interface AISuggestionsProps {
  content: string
  cursorPosition: number
  onInsertSuggestion: (text: string) => void
  onClose: () => void
  visible: boolean
}

interface Suggestion {
  id: string
  type: 'completion' | 'improvement' | 'continuation'
  text: string
  context: string
  confidence: number
}

const AISuggestionsComponent = function AISuggestions({ 
  content, 
  cursorPosition, 
  onInsertSuggestion, 
  onClose,
  visible 
}: AISuggestionsProps) {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [activeType, setActiveType] = useState<'completion' | 'improvement' | 'continuation'>('completion')

  const generateSuggestions = useCallback(async () => {
    setIsLoading(true)
    try {
      // Get context around cursor position
      const beforeCursor = content.substring(Math.max(0, cursorPosition - 500), cursorPosition)
      const afterCursor = content.substring(cursorPosition, cursorPosition + 100)
      
      const response = await fetch('/api/agents/suggestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: activeType,
          beforeCursor,
          afterCursor,
          fullContent: content
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setSuggestions(data.suggestions || [])
      } else {
        console.error('Failed to generate suggestions:', data.error)
        setSuggestions([])
      }
    } catch (error) {
      console.error('Error generating suggestions:', error)
      setSuggestions([])
    } finally {
      setIsLoading(false)
    }
  }, [content, cursorPosition, activeType])

  useEffect(() => {
    if (visible && content) {
      generateSuggestions()
    }
  }, [visible, content, generateSuggestions])

  const trackSuggestionUsage = useCallback(async (suggestionId: string, action: 'accepted' | 'rejected') => {
    try {
      await fetch('/api/agents/suggestions/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ suggestionId, action })
      })
    } catch (error) {
      console.error('Error tracking suggestion usage:', error)
    }
  }, [])

  const handleInsertSuggestion = useCallback((suggestion: Suggestion) => {
    onInsertSuggestion(suggestion.text)
    // Track suggestion usage for learning
    trackSuggestionUsage(suggestion.id, 'accepted')
  }, [onInsertSuggestion, trackSuggestionUsage])

  const getSuggestionTypeIcon = useCallback((type: string) => {
    switch (type) {
      case 'completion': return Wand2
      case 'improvement': return Lightbulb
      case 'continuation': return RefreshCw
      default: return Lightbulb
    }
  }, [])

  const getSuggestionTypeColor = useCallback((type: string) => {
    switch (type) {
      case 'completion': return 'bg-blue-500'
      case 'improvement': return 'bg-green-500'
      case 'continuation': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }, [])

  if (!visible) return null

  return (
    <Card className="absolute right-4 top-4 w-80 max-h-96 z-50 shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4 text-yellow-500" />
            <CardTitle className="text-sm">AI Suggestions</CardTitle>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Suggestion Type Tabs */}
        <div className="flex gap-1">
          {(['completion', 'improvement', 'continuation'] as const).map((type) => (
            <Button
              key={type}
              variant={activeType === type ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveType(type)}
              className="text-xs"
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-muted-foreground">Generating suggestions...</span>
          </div>
        ) : (
          <ScrollArea className="h-64">
            <div className="space-y-3">
              {suggestions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Lightbulb className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No suggestions available</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={generateSuggestions}
                    className="mt-2"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Refresh
                  </Button>
                </div>
              ) : (
                suggestions.map((suggestion) => {
                  const IconComponent = getSuggestionTypeIcon(suggestion.type)
                  return (
                    <Card key={suggestion.id} className="p-3 hover:shadow-md transition-shadow">
                      <div className="space-y-2">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${getSuggestionTypeColor(suggestion.type)}`} />
                            <IconComponent className="h-3 w-3 text-muted-foreground" />
                            <Badge variant="secondary" className="text-xs">
                              {Math.round(suggestion.confidence * 100)}%
                            </Badge>
                          </div>
                        </div>
                        
                        <p className="text-xs text-muted-foreground bg-muted p-2 rounded">
                          {suggestion.context}
                        </p>
                        
                        <p className="text-sm font-medium">
                          {suggestion.text}
                        </p>
                        
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            onClick={() => handleInsertSuggestion(suggestion)}
                            className="flex-1"
                          >
                            Insert
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigator.clipboard.writeText(suggestion.text)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => trackSuggestionUsage(suggestion.id, 'rejected')}
                          >
                            <ThumbsDown className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  )
                })
              )}
            </div>
          </ScrollArea>
        )}
        
        <div className="flex justify-between items-center mt-3 pt-3 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={generateSuggestions}
            disabled={isLoading}
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Refresh
          </Button>
          
          <p className="text-xs text-muted-foreground">
            {suggestions.length} suggestions
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

// Memoize the component to prevent unnecessary re-renders
export const AISuggestions = memo(AISuggestionsComponent)