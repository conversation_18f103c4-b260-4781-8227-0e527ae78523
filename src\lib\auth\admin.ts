import { NextResponse } from 'next/server';
import { authenticateUser } from './server';
import { config } from '@/lib/config';
import type { User } from '@supabase/supabase-js';

export interface AdminAuthResult {
  success: boolean;
  user?: User;
  response?: NextResponse;
}

/**
 * Authenticates a user and verifies they have admin privileges
 */
export async function authenticateAdmin(): Promise<AdminAuthResult> {
  // First, authenticate the user
  const authResult = await authenticateUser();
  
  if (!authResult.success || !authResult.user) {
    return {
      success: false,
      response: authResult.response
    };
  }

  // Check if user is in admin emails list
  const adminEmails = config.adminEmails;
  const userEmail = authResult.user.email;

  if (!userEmail || adminEmails.length === 0) {
    return {
      success: false,
      response: NextResponse.json({
        error: 'Unauthorized',
        message: 'Admin access required'
      }, { status: 403 })
    };
  }

  if (!adminEmails.includes(userEmail)) {
    return {
      success: false,
      response: NextResponse.json({
        error: 'Forbidden',
        message: 'This action requires admin privileges'
      }, { status: 403 })
    };
  }

  return {
    success: true,
    user: authResult.user
  };
}

/**
 * Middleware to check admin access for API routes
 */
export function requireAdmin<T extends (...args: unknown[]) => Promise<NextResponse>>(
  handler: T
): T {
  return (async (...args: Parameters<T>) => {
    const adminAuth = await authenticateAdmin();
    
    if (!adminAuth.success) {
      return adminAuth.response!;
    }

    // Call the original handler with admin user context
    return handler(...args);
  }) as T;
}