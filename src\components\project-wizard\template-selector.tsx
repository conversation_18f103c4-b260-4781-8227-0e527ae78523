'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useWizardStore } from '@/stores/wizard-store'
import { 
  <PERSON><PERSON><PERSON>, 
  Sword, 
  Rocket, 
  Heart, 
  Skull, 
  Crown,
  BookOpen
} from 'lucide-react'

interface Template {
  id: string
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  genre: string
  selections: Record<string, unknown>
  popular?: boolean
}

const templates: Template[] = [
  {
    id: 'epic-fantasy',
    name: 'Epic Fantasy',
    description: 'Grand adventures with magic, heroes, and mythical creatures',
    icon: Sword,
    genre: 'Fantasy',
    popular: true,
    selections: {
      primaryGenre: 'Fantasy',
      subgenre: 'Epic Fantasy',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Epic & Heroic', 'Dark & Gritty'],
      writingStyle: 'Literary',
      structureType: "Hero's Journey",
      pacingPreference: 'Balanced',
      characterComplexity: 'Complex/Layered',
      timePeriod: 'Medieval',
      worldType: 'Fantasy World',
      magicTechLevel: 'High Magic',
      targetAudience: 'Adult',
      contentRating: 'PG-13',
      targetWordCount: 120000,
      targetChapters: 30
    }
  },
  {
    id: 'space-opera',
    name: 'Space Opera',
    description: 'Galactic adventures with advanced technology and alien civilizations',
    icon: Rocket,
    genre: 'Science Fiction',
    popular: true,
    selections: {
      primaryGenre: 'Science Fiction',
      subgenre: 'Space Opera',
      narrativeVoice: 'Third Person Omniscient',
      tense: 'Past',
      toneOptions: ['Epic & Heroic', 'Mysterious & Suspenseful'],
      writingStyle: 'Commercial',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Fast-Paced Action',
      characterComplexity: 'Complex/Layered',
      timePeriod: 'Far Future',
      worldType: 'Sci-Fi Universe',
      magicTechLevel: 'Advanced Technology',
      targetAudience: 'Adult',
      contentRating: 'PG-13',
      targetWordCount: 100000,
      targetChapters: 25
    }
  },
  {
    id: 'romantic-drama',
    name: 'Contemporary Romance',
    description: 'Modern love stories with emotional depth and character development',
    icon: Heart,
    genre: 'Romance',
    selections: {
      primaryGenre: 'Romance',
      narrativeVoice: 'First Person',
      tense: 'Present',
      toneOptions: ['Romantic & Passionate', 'Intimate & Personal'],
      writingStyle: 'Commercial',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Character-Driven',
      characterComplexity: 'Complex/Layered',
      timePeriod: 'Contemporary',
      worldType: 'Real World',
      targetAudience: 'New Adult',
      contentRating: 'R',
      targetWordCount: 80000,
      targetChapters: 20
    }
  },
  {
    id: 'mystery-thriller',
    name: 'Mystery Thriller',
    description: 'Suspenseful stories with puzzles to solve and secrets to uncover',
    icon: Skull,
    genre: 'Mystery/Thriller',
    selections: {
      primaryGenre: 'Mystery/Thriller',
      subgenre: 'Psychological Thriller',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Mysterious & Suspenseful', 'Dark & Gritty'],
      writingStyle: 'Commercial',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Fast-Paced Action',
      characterComplexity: 'Complex/Layered',
      timePeriod: 'Contemporary',
      worldType: 'Real World',
      targetAudience: 'Adult',
      contentRating: 'R',
      targetWordCount: 90000,
      targetChapters: 22
    }
  },
  {
    id: 'historical-fiction',
    name: 'Historical Fiction',
    description: 'Stories set in the past with authentic historical details',
    icon: Crown,
    genre: 'Historical Fiction',
    selections: {
      primaryGenre: 'Historical Fiction',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Epic & Heroic', 'Intimate & Personal'],
      writingStyle: 'Literary',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Character-Driven',
      characterComplexity: 'Complex/Layered',
      timePeriod: 'Victorian Era',
      worldType: 'Real World',
      targetAudience: 'Adult',
      contentRating: 'PG-13',
      targetWordCount: 95000,
      targetChapters: 24
    }
  },
  {
    id: 'custom',
    name: 'Start from Scratch',
    description: 'Create your own unique story with full customization',
    icon: BookOpen,
    genre: 'Custom',
    selections: {}
  }
]

export function TemplateSelector() {
  const { updateSelections } = useWizardStore()
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template.id)
    if (template.id === 'custom') {
      // Clear all selections for custom
      updateSelections({})
    } else {
      // Apply template selections
      updateSelections(template.selections)
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Choose Your Story Template</h2>
        <p className="text-muted-foreground">
          Start with a proven template or build from scratch
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {templates.map((template) => {
          const Icon = template.icon
          const isSelected = selectedTemplate === template.id
          
          return (
            <Card 
              key={template.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-primary border-primary' : ''
              }`}
              onClick={() => handleTemplateSelect(template)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon className="h-5 w-5" />
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                  </div>
                  {template.popular && (
                    <Badge variant="secondary" className="text-xs">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Popular
                    </Badge>
                  )}
                </div>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {template.genre !== 'Custom' && (
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{template.genre}</Badge>
                      {Boolean(template.selections.targetWordCount) && (
                        <Badge variant="outline">
                          {(Number(template.selections.targetWordCount) / 1000).toFixed(0)}k words
                        </Badge>
                      )}
                    </div>
                  )}
                  
                  {isSelected && (
                    <div className="pt-2 border-t">
                      <Button size="sm" className="w-full">
                        ✓ Selected
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {selectedTemplate && selectedTemplate !== 'custom' && (
        <Card className="bg-primary/5">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <Sparkles className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h3 className="font-medium mb-2">Template Applied!</h3>
                <p className="text-sm text-muted-foreground">
                  Your project has been pre-configured with settings for{' '}
                  <strong>{templates.find(t => t.id === selectedTemplate)?.name}</strong>.
                  You can customize any of these settings in the following steps.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}