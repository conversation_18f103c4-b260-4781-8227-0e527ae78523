import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { AuthResult, AuthError, AUTH_ERRORS } from './types'
import { config } from '@/lib/config'

/**
 * Authenticate user from request and return user + supabase client
 */
export async function authenticateUser(): Promise<AuthResult> {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return {
        success: false,
        response: createErrorResponse(AUTH_ERRORS.UNAUTHORIZED)
      }
    }

    return {
      success: true,
      user,
      supabase
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      response: createErrorResponse(AUTH_ERRORS.SERVER_ERROR)
    }
  }
}

/**
 * Create standardized error response
 */
export function createErrorResponse(error: AuthError): NextResponse {
  return NextResponse.json(
    { error: error.message },
    { status: error.status }
  )
}

/**
 * Validate required environment variables
 */
export function validateEnvironment(variables: string[]): { valid: boolean; missing: string[] } {
  const missing = variables.filter(variable => !process.env[variable])
  return {
    valid: missing.length === 0,
    missing
  }
}

/**
 * Success response interface
 */
interface SuccessResponse {
  success: true
  message?: string
  [key: string]: unknown
}

/**
 * Create consistent success response format
 */
export function createSuccessResponse(data: Record<string, unknown>, message?: string): NextResponse {
  const response: SuccessResponse = { success: true, ...data }
  if (message) {
    response.message = message
  }
  return NextResponse.json(response)
}

/**
 * Handle async route errors consistently
 */
export function handleRouteError(error: unknown, context: string): NextResponse {
  console.error(`${context} error:`, error)
  
  if (error instanceof Error) {
    // Don't expose internal errors in production
    const isDevelopment = config.isDevelopment
    const errorMessage = isDevelopment ? error.message : 'Internal server error'
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
  
  return createErrorResponse(AUTH_ERRORS.SERVER_ERROR)
}