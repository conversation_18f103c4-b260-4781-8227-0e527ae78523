"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface GradientTextProps {
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  animate?: boolean;
}

export function GradientText({ 
  children, 
  className,
  gradient = "from-amber-600 via-orange-500 to-amber-600",
  animate = true
}: GradientTextProps) {
  return (
    <motion.span
      className={cn(
        "bg-clip-text text-transparent bg-gradient-to-r",
        gradient,
        animate && "bg-[length:200%_auto] animate-gradient",
        className
      )}
      initial={animate ? { backgroundPosition: "0% center" } : undefined}
      animate={animate ? { backgroundPosition: "200% center" } : undefined}
      transition={animate ? { duration: 5, repeat: Infinity, ease: "linear" } : undefined}
    >
      {children}
    </motion.span>
  );
}