// Character-related type definitions

export interface CharacterData {
  [character_id: string]: {
    name: string
    role: string
    description: string
    backstory: string
    personality_traits: string[]
    relationships: Array<{ character_id: string; relationship: string; description: string }>
    voice_data?: {
      speaking_style: string
      vocabulary: string[]
      mannerisms: string[]
    }
  }
}

export interface CharacterStates {
  [character_id: string]: {
    emotional_state: string
    goals: string[]
    knowledge: string[]
    relationships: { [other_character_id: string]: string }
  }
}

export interface VoiceData {
  speaking_style: string
  vocabulary: string[]
  mannerisms: string[]
}

export interface PersonalityTraits {
  traits: string[]
}

export interface CharacterArc {
  starting_point?: string
  transformation?: string
  ending_point?: string
  milestones?: Array<{
    chapter: number
    description: string
    development: string
  }>
  current_stage?: string
}

export interface CharacterRelationship {
  character_id: string
  relationship_type: string
  description: string
  dynamics: string[]
  evolution?: Array<{
    chapter: number
    change: string
  }>
}

// Type aliases for clarity
export type CharacterDataMap = CharacterData
export type CharacterStatesData = CharacterStates
export type VoiceDataStructure = VoiceData
export type PersonalityTraitsData = PersonalityTraits
export type CharacterArcData = CharacterArc