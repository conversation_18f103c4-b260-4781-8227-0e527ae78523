'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  AlertTriangle, 
  Bug, 
  Send, 
  Copy, 
  CheckCircle,
  Clock,
  MessageSquare,
  Info
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { useErrorReport } from '@/hooks/use-error-handling'

interface ErrorReportData {
  id: string
  type: 'javascript' | 'api' | 'user_reported' | 'crash'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  stack?: string
  url: string
  userAgent: string
  timestamp: string
  userId?: string
  userEmail?: string
  reproductionSteps?: string
  expectedBehavior?: string
  actualBehavior?: string
  additionalInfo?: string
  attachments?: File[]
  includeEnvironmentInfo: boolean
  allowContact: boolean
}

interface ErrorReportingDialogProps {
  error?: Error
  errorId?: string
  trigger?: React.ReactNode
  onSubmit?: (report: ErrorReportData) => void
  defaultOpen?: boolean
}

interface ErrorReportFormProps {
  error?: Error
  errorId?: string
  onSubmit: (report: ErrorReportData) => void
  onCancel?: () => void
}

function ErrorSeverityBadge({ severity }: { severity: ErrorReportData['severity'] }) {
  const variants = {
    low: { color: 'bg-blue-100 text-blue-800', label: 'Low' },
    medium: { color: 'bg-yellow-100 text-yellow-800', label: 'Medium' },
    high: { color: 'bg-orange-100 text-orange-800', label: 'High' },
    critical: { color: 'bg-red-100 text-red-800', label: 'Critical' }
  }

  const variant = variants[severity]
  
  return (
    <Badge className={variant.color}>
      {variant.label} Priority
    </Badge>
  )
}

function ErrorReportForm({ 
  error, 
  errorId, 
  onSubmit, 
  onCancel 
}: ErrorReportFormProps) {
  const { generateErrorId } = useErrorReport()
  const [isSubmitting, setIsSubmitting] = React.useState(false)
  const [formData, setFormData] = React.useState<Partial<ErrorReportData>>({
    id: errorId || generateErrorId(),
    type: error ? 'javascript' : 'user_reported',
    severity: 'medium',
    message: error?.message || '',
    stack: error?.stack,
    url: typeof window !== 'undefined' ? window.location.href : '',
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
    timestamp: new Date().toISOString(),
    includeEnvironmentInfo: true,
    allowContact: false,
    reproductionSteps: '',
    expectedBehavior: '',
    actualBehavior: '',
    additionalInfo: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const report: ErrorReportData = {
        ...formData,
        id: formData.id!,
        type: formData.type!,
        severity: formData.severity!,
        message: formData.message!,
        url: formData.url!,
        userAgent: formData.userAgent!,
        timestamp: formData.timestamp!,
        includeEnvironmentInfo: formData.includeEnvironmentInfo!,
        allowContact: formData.allowContact!
      }

      await onSubmit(report)
      
      toast({
        title: "Report Submitted",
        description: "Thank you for your feedback. We'll investigate this issue.",
        duration: 5000
      })

    } catch {
      toast({
        variant: "destructive",
        title: "Submission Failed",
        description: "Failed to submit error report. Please try again.",
        duration: 5000
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const updateFormData = <K extends keyof ErrorReportData>(field: K, value: ErrorReportData[K]) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Error Information */}
      {error && (
        <Alert>
          <Bug className="h-4 w-4" />
          <AlertTitle>Error Details</AlertTitle>
          <AlertDescription>
            <div className="mt-2 space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline">ID: {formData.id}</Badge>
                <ErrorSeverityBadge severity={formData.severity!} />
              </div>
              <div className="text-sm font-mono bg-muted p-2 rounded">
                {error.message}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Severity Selection */}
      <div className="space-y-2">
        <Label>Issue Severity</Label>
        <div className="flex gap-2">
          {(['low', 'medium', 'high', 'critical'] as const).map((severity) => (
            <Button
              key={severity}
              type="button"
              variant={formData.severity === severity ? 'default' : 'outline'}
              size="sm"
              onClick={() => updateFormData('severity', severity)}
            >
              <ErrorSeverityBadge severity={severity} />
            </Button>
          ))}
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-3">
        <Label>Contact Information (Optional)</Label>
        <Input
          placeholder="Your email address"
          type="email"
          value={formData.userEmail || ''}
          onChange={(e) => updateFormData('userEmail', e.target.value)}
        />
        <div className="flex items-center space-x-2">
          <Checkbox
            id="allowContact"
            checked={formData.allowContact}
            onCheckedChange={(checked) => updateFormData('allowContact', Boolean(checked))}
          />
          <Label htmlFor="allowContact" className="text-sm">
            Allow us to contact you about this issue
          </Label>
        </div>
      </div>

      {/* Description Fields */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="reproductionSteps">How to reproduce this issue</Label>
          <Textarea
            id="reproductionSteps"
            placeholder="1. Go to...&#10;2. Click on...&#10;3. See error..."
            value={formData.reproductionSteps}
            onChange={(e) => updateFormData('reproductionSteps', e.target.value)}
            rows={4}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="expectedBehavior">What did you expect to happen?</Label>
          <Textarea
            id="expectedBehavior"
            placeholder="Describe what you expected to happen..."
            value={formData.expectedBehavior}
            onChange={(e) => updateFormData('expectedBehavior', e.target.value)}
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="actualBehavior">What actually happened?</Label>
          <Textarea
            id="actualBehavior"
            placeholder="Describe what actually happened..."
            value={formData.actualBehavior}
            onChange={(e) => updateFormData('actualBehavior', e.target.value)}
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="additionalInfo">Additional Information</Label>
          <Textarea
            id="additionalInfo"
            placeholder="Any other details that might help us..."
            value={formData.additionalInfo}
            onChange={(e) => updateFormData('additionalInfo', e.target.value)}
            rows={3}
          />
        </div>
      </div>

      {/* Privacy Options */}
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="includeEnvironmentInfo"
            checked={formData.includeEnvironmentInfo}
            onCheckedChange={(checked) => updateFormData('includeEnvironmentInfo', Boolean(checked))}
          />
          <Label htmlFor="includeEnvironmentInfo" className="text-sm">
            Include technical information (browser, OS, etc.)
          </Label>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        
        <Button 
          type="submit" 
          disabled={isSubmitting || !formData.message}
        >
          {isSubmitting ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Submitting...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Submit Report
            </>
          )}
        </Button>
      </div>
    </form>
  )
}

export function ErrorReportingDialog({
  error,
  errorId,
  trigger,
  onSubmit,
  defaultOpen = false
}: ErrorReportingDialogProps) {
  const [open, setOpen] = React.useState(defaultOpen)
  const { } = useErrorReport()

  const handleSubmit = async (report: ErrorReportData) => {
    try {
      // Submit to external service
      const _response = await fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(report)
      })

      if (!_response.ok) {
        throw new Error('Failed to submit report')
      }

      // Call custom handler
      onSubmit?.(report)
      
      // Close dialog
      setOpen(false)

    } catch (_error) {
      console.error('Failed to submit error report:', _error)
      throw error
    }
  }

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Bug className="h-4 w-4 mr-2" />
      Report Issue
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            Report an Issue
          </DialogTitle>
          <DialogDescription>
            Help us improve BookScribe by reporting bugs or issues you encounter.
            Your feedback is valuable to us.
          </DialogDescription>
        </DialogHeader>
        
        <ErrorReportForm
          error={error}
          errorId={errorId}
          onSubmit={handleSubmit}
          onCancel={() => setOpen(false)}
        />
      </DialogContent>
    </Dialog>
  )
}

// Quick error reporting component for inline use
export function QuickErrorReport({ 
  error, 
  className 
}: { 
  error: Error
  className?: string 
}) {
  const [copied, setCopied] = React.useState(false)
  const { generateErrorId } = useErrorReport()
  
  const errorId = React.useMemo(() => generateErrorId(), [generateErrorId])
  
  const copyErrorDetails = async () => {
    const details = {
      id: errorId,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      url: window.location.href
    }
    
    try {
      await navigator.clipboard.writeText(JSON.stringify(details, null, 2))
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      
      toast({
        title: "Copied to clipboard",
        description: "Error details have been copied.",
        duration: 2000
      })
    } catch (_err) {
      console.error('Failed to copy:', _err)
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Error ID: {errorId}</AlertTitle>
        <AlertDescription>
          Reference this ID when contacting support.
        </AlertDescription>
      </Alert>
      
      <div className="flex gap-2">
        <Button
          onClick={copyErrorDetails}
          variant="outline"
          size="sm"
          className="flex-1"
        >
          {copied ? (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Copied!
            </>
          ) : (
            <>
              <Copy className="h-4 w-4 mr-2" />
              Copy Details
            </>
          )}
        </Button>
        
        <ErrorReportingDialog
          error={error}
          errorId={errorId}
          trigger={
            <Button variant="outline" size="sm" className="flex-1">
              <MessageSquare className="h-4 w-4 mr-2" />
              Report Issue
            </Button>
          }
        />
      </div>
    </div>
  )
}

// Error status indicator for showing current error state
export function ErrorStatusIndicator({ 
  hasErrors = false,
  errorCount = 0,
  onClick 
}: { 
  hasErrors?: boolean
  errorCount?: number
  onClick?: () => void 
}) {
  if (!hasErrors) return null

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onClick}
      className="border-destructive text-destructive hover:bg-destructive hover:text-destructive-foreground"
    >
      <AlertTriangle className="h-4 w-4 mr-2" />
      {errorCount > 1 ? `${errorCount} Errors` : 'Error'}
    </Button>
  )
}

// Global error provider for collecting and managing errors
interface ErrorContextValue {
  errors: Error[]
  addError: (error: Error) => void
  removeError: (index: number) => void
  clearErrors: () => void
  reportAllErrors: () => void
}

const ErrorContext = React.createContext<ErrorContextValue | null>(null)

export function GlobalErrorProvider({ children }: { children: React.ReactNode }) {
  const [errors, setErrors] = React.useState<Error[]>([])

  const addError = React.useCallback((error: Error) => {
    setErrors(prev => [...prev, error])
  }, [])

  const removeError = React.useCallback((index: number) => {
    setErrors(prev => prev.filter((_, i) => i !== index))
  }, [])

  const clearErrors = React.useCallback(() => {
    setErrors([])
  }, [])

  const reportAllErrors = React.useCallback(() => {
    // Implement batch error reporting
    console.log('Reporting all errors:', errors)
  }, [errors])

  const value = React.useMemo(() => ({
    errors,
    addError,
    removeError,
    clearErrors,
    reportAllErrors
  }), [errors, addError, removeError, clearErrors, reportAllErrors])

  return (
    <ErrorContext.Provider value={value}>
      {children}
      
      {/* Global error indicator */}
      {errors.length > 0 && (
        <div className="fixed bottom-4 right-4 z-50">
          <ErrorReportingDialog
            trigger={
              <ErrorStatusIndicator
                hasErrors={true}
                errorCount={errors.length}
              />
            }
          />
        </div>
      )}
    </ErrorContext.Provider>
  )
}

export function useGlobalErrors() {
  const context = React.useContext(ErrorContext)
  if (!context) {
    throw new Error('useGlobalErrors must be used within GlobalErrorProvider')
  }
  return context
}