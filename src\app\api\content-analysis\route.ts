import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import OpenAI from 'openai';
import { ContentAnalyzer } from '@/lib/analysis/content-analyzer';
import { VoiceAnalyzer } from '@/lib/analysis/voice-analyzer';
import { createClient } from '@/lib/supabase/server';
import { config } from '@/lib/config';

const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { content, projectId, chapterNumber, userVoiceProfile } = await request.json();

    if (!content || !projectId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Initialize analyzers
    const contentAnalyzer = new ContentAnalyzer(openai);
    const voiceAnalyzer = new VoiceAnalyzer(openai);

    // Get project context for analysis
    const { data: project } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single();

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Get story bible for context
    const { data: storyBible } = await supabase
      .from('story_bibles')
      .select('*')
      .eq('project_id', projectId)
      .single();

    // Parallel analysis for performance
    const [
      grammaticalSuggestions,
      styleSuggestions,
      narrativeSuggestions,
      voiceMatchSuggestions,
      plotConsistencySuggestions,
    ] = await Promise.all([
      contentAnalyzer.analyzeGrammar(content),
      contentAnalyzer.analyzeStyle(content, project),
      contentAnalyzer.analyzeNarrative(content, project, storyBible),
      userVoiceProfile ? voiceAnalyzer.analyzeVoiceMatch(content, userVoiceProfile) : [],
      contentAnalyzer.analyzePlotConsistency(content, storyBible, chapterNumber),
    ]);

    // Combine all suggestions
    const allSuggestions = [
      ...grammaticalSuggestions,
      ...styleSuggestions,
      ...narrativeSuggestions,
      ...voiceMatchSuggestions,
      ...plotConsistencySuggestions,
    ];

    // Filter and prioritize suggestions
    const prioritizedSuggestions = prioritizeSuggestions(allSuggestions as unknown as Array<Record<string, unknown>>, project);

    // Log analysis for improvement
    await logAnalysis(projectId, chapterNumber, content.length, prioritizedSuggestions.length);

    return NextResponse.json(prioritizedSuggestions);

  } catch (error) {
    console.error('Error in content analysis:', error);
    return NextResponse.json(
      { error: 'Analysis failed' },
      { status: 500 }
    );
  }
}

function prioritizeSuggestions(suggestions: Array<Record<string, unknown>>, project: Record<string, unknown>) {
  // Score suggestions based on project settings and context
  return suggestions
    .map(suggestion => ({
      ...suggestion,
      priority: calculatePriority(suggestion, project),
    }))
    .sort((a, b) => b.priority - a.priority)
    .slice(0, 20) // Limit to top 20 suggestions to avoid overwhelming
    .map(({ priority: _priority, ...suggestion }) => suggestion);
}

function calculatePriority(suggestion: Record<string, unknown>, project: Record<string, unknown>): number {
  let priority = 1;

  // Higher priority for errors
  if (suggestion.severity === 'error') priority += 10;
  else if (suggestion.severity === 'warning') priority += 5;

  // Boost character and dialogue suggestions for character-driven stories
  if (project.character_complexity === 'complex_layered') {
    if (suggestion.type === 'character' || suggestion.type === 'dialogue') {
      priority += 3;
    }
  }

  // Boost pacing suggestions for action-heavy genres
  if (['adventure', 'mystery_thriller'].includes(project.primary_genre as string)) {
    if (suggestion.type === 'pacing') {
      priority += 3;
    }
  }

  // Boost style suggestions for literary fiction
  if (project.writing_style === 'literary') {
    if (suggestion.type === 'style') {
      priority += 2;
    }
  }

  return priority;
}

async function logAnalysis(projectId: string, chapterNumber: number, contentLength: number, suggestionCount: number) {
  try {
    const supabase = await createClient();
    await supabase.from('content_analysis_logs').insert({
      project_id: projectId,
      chapter_number: chapterNumber,
      content_length: contentLength,
      suggestions_count: suggestionCount,
      analyzed_at: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error logging analysis:', error);
  }
}