import { createClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';
import { config } from '@/lib/config';

export async function GET() {
  // Block access in production
  if (config.isProduction) {
    return NextResponse.json(
      { error: 'Not found' },
      { status: 404 }
    );
  }
  try {
    // Test server client creation
    const supabase = await createClient();
    
    // Test database connection
    const { error: dbError } = await supabase
      .from('projects')
      .select('count')
      .limit(1);
    
    if (dbError) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database connection failed',
        details: dbError.message 
      });
    }
    
    // Test auth session
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      return NextResponse.json({ 
        success: false, 
        error: 'Auth session check failed',
        details: sessionError.message 
      });
    }
    
    return NextResponse.json({
      success: true,
      database: 'Connected',
      auth: 'Working',
      session: session?.session ? 'Active session found' : 'No active session',
      environment: {
        supabaseUrl: config.supabase.url.substring(0, 30) + '...',
        hasAnonKey: !!config.supabase.anonKey
      }
    });
    
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: 'Test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}