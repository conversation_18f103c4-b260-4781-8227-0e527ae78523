import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { ServiceManager } from '@/lib/services/service-manager'

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const body = await request.json()
    const { sessionId, section } = body

    if (!sessionId || !section) {
      return NextResponse.json(
        { error: 'Session ID and section are required' },
        { status: 400 }
      )
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationHub = await serviceManager.getCollaborationHub()
    if (!collaborationHub) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationHub.unlockSection(
      sessionId,
      authResult.user.id,
      section
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to unlock section' },
        { status: 400 }
      )
    }

    return NextResponse.json({ success: true, locked: false })
  } catch (error) {
    console.error('Collaboration unlock error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}