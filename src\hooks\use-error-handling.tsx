'use client'

import React, { useCallback, useState, createContext, useContext } from 'react'
import { toast } from '@/hooks/use-toast'
import { config } from '@/lib/config'

export interface ErrorContext {
  [key: string]: string | number | boolean | null | undefined | ErrorContext | ErrorContext[]
}

export interface ErrorReport {
  id: string
  message: string
  stack?: string
  timestamp: string
  url: string
  userAgent: string
  userId?: string
  additionalContext?: ErrorContext
}

interface UseAsyncErrorOptions {
  showToast?: boolean
  toastTitle?: string
  toastDescription?: string
  onError?: (error: Error) => void
  retryable?: boolean
}

interface UseErrorReportOptions {
  enableReporting?: boolean
  includeUserInfo?: boolean
  includeEnvironmentInfo?: boolean
  onReported?: (report: ErrorReport) => void
}

// Hook for handling async errors in components
export function useAsyncError(options: UseAsyncErrorOptions = {}) {
  const {
    showToast = true,
    toastTitle = "Error",
    toastDescription,
    onError
  } = options

  const throwError = useCallback((error: unknown) => {
    let errorObj: Error

    if (error instanceof Error) {
      errorObj = error
    } else if (typeof error === 'string') {
      errorObj = new Error(error)
    } else {
      errorObj = new Error('An unknown error occurred')
    }

    // Call custom error handler
    onError?.(errorObj)

    // Show toast notification
    if (showToast) {
      toast({
        variant: "destructive",
        title: toastTitle,
        description: toastDescription || errorObj.message
      })
    }

    // Throw error to be caught by ErrorBoundary
    throw errorObj
  }, [showToast, toastTitle, toastDescription, onError])

  const handleAsyncError = useCallback(<T,>(
    asyncFn: () => Promise<T>,
    errorMessage?: string
  ): Promise<T> => {
    const execute = async (): Promise<T> => {
      try {
        return await asyncFn()
      } catch (error) {
        if (errorMessage) {
          throwError(new Error(errorMessage))
        } else {
          throwError(error)
        }
        throw error // This won't be reached, but TypeScript needs it
      }
    }
    return execute()
  }, [throwError])

  return {
    throwError,
    handleAsyncError
  }
}

// Hook for error reporting
export function useErrorReport(options: UseErrorReportOptions = {}) {
  const {
    enableReporting = config.isProduction,
    includeUserInfo = true,
    onReported
  } = options

  const generateErrorId = useCallback(() => {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }, [])

  const reportError = useCallback(async (
    error: Error,
    additionalContext?: ErrorContext
  ): Promise<string | null> => {
    if (!enableReporting) {
      console.error('Error reporting disabled:', error)
      return null
    }

    const errorId = generateErrorId()
    
    try {
      const report: ErrorReport = {
        id: errorId,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        additionalContext
      }

      // Include user info if available and enabled
      if (includeUserInfo) {
        try {
          const userResponse = await fetch('/api/auth/user', { 
            credentials: 'include' 
          })
          if (userResponse.ok) {
            const userData = await userResponse.json()
            report.userId = userData.id
          }
        } catch {
          // Ignore user info fetch errors
        }
      }

      // Report to error tracking service
      const response = await fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(report)
      })

      if (!response.ok) {
        throw new Error(`Failed to report error: ${response.statusText}`)
      }

      onReported?.(report)
      return errorId

    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
      return null
    }
  }, [enableReporting, includeUserInfo, onReported, generateErrorId])

  const reportAndThrow = useCallback(async (
    error: Error,
    additionalContext?: ErrorContext
  ) => {
    await reportError(error, additionalContext)
    throw error
  }, [reportError])

  return {
    reportError,
    reportAndThrow,
    generateErrorId
  }
}

// Hook for handling API responses with automatic error handling
export function useAPICall() {
  const { handleAsyncError } = useAsyncError({
    showToast: true,
    toastTitle: "API Error"
  })
  const { reportError } = useErrorReport()

  const callAPI = useCallback(<T,>(
    url: string,
    options?: RequestInit,
    errorMessage?: string
  ): Promise<T> => {
    return handleAsyncError(async () => {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers
        },
        ...options
      })

      if (!response.ok) {
        const error = new Error(
          errorMessage || `API call failed: ${response.statusText}`
        )
        
        // Add response details to error
        Object.assign(error, {
          status: response.status,
          statusText: response.statusText,
          url: response.url
        })

        // Report API errors with additional context
        await reportError(error, {
          url,
          method: options?.method || 'GET',
          status: response.status,
          responseHeaders: Object.fromEntries(response.headers.entries())
        })

        throw error
      }

      return response.json()
    }, errorMessage)
  }, [handleAsyncError, reportError])

  return { callAPI }
}

// Hook for handling form submission errors
export function useFormError() {
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [submitError, setSubmitError] = useState<string | null>(null)

  const setFieldError = useCallback((field: string, message: string) => {
    setErrors(prev => ({ ...prev, [field]: message }))
  }, [])

  const clearFieldError = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[field]
      return newErrors
    })
  }, [])

  const clearAllErrors = useCallback(() => {
    setErrors({})
    setSubmitError(null)
  }, [])

  const handleSubmitError = useCallback((error: unknown) => {
    if (error instanceof Error) {
      setSubmitError(error.message)
    } else if (typeof error === 'string') {
      setSubmitError(error)
    } else {
      setSubmitError('An unexpected error occurred')
    }
  }, [])

  const getFieldError = useCallback((field: string) => {
    return errors[field]
  }, [errors])

  const hasErrors = React.useMemo(() => {
    return Object.keys(errors).length > 0 || submitError !== null
  }, [errors, submitError])

  return {
    errors,
    submitError,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    handleSubmitError,
    getFieldError,
    hasErrors
  }
}

// Hook for retry functionality
export function useRetry(maxRetries = 3, baseDelay = 1000) {
  const [retryCount, setRetryCount] = useState(0)
  const [isRetrying, setIsRetrying] = useState(false)

  const executeWithRetry = useCallback(<T,>(
    asyncFn: () => Promise<T>,
    onRetryAttempt?: (attempt: number, error: Error) => void
  ): Promise<T> => {
    const execute = async (): Promise<T> => {
      let lastError: Error

      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          setRetryCount(attempt)
          setIsRetrying(attempt > 0)
          
          if (attempt > 0) {
            // Exponential backoff
            const delay = baseDelay * Math.pow(2, attempt - 1)
            await new Promise(resolve => setTimeout(resolve, delay))
          }

          const result = await asyncFn()
          setIsRetrying(false)
          return result

        } catch (error) {
          lastError = error instanceof Error ? error : new Error('Unknown error')
          
          if (attempt < maxRetries) {
            onRetryAttempt?.(attempt + 1, lastError)
          }
        }
      }

      setIsRetrying(false)
      throw lastError!
    }
    
    return execute()
  }, [maxRetries, baseDelay, setRetryCount, setIsRetrying])

  const reset = useCallback(() => {
    setRetryCount(0)
    setIsRetrying(false)
  }, [])

  return {
    executeWithRetry,
    retryCount,
    isRetrying,
    reset,
    canRetry: retryCount < maxRetries
  }
}

// Context for global error handling
interface ErrorContextValue {
  reportError: (error: Error, context?: ErrorContext) => Promise<string | null>
  showError: (error: Error | string) => void
  clearErrors: () => void
}

const ErrorContext = createContext<ErrorContextValue | null>(null)

export function ErrorProvider({ children }: { children: React.ReactNode }) {
  const { reportError } = useErrorReport()
  
  const showError = useCallback((error: Error | string) => {
    const message = error instanceof Error ? error.message : error
    toast({
      variant: "destructive",
      title: "Error",
      description: message
    })
  }, [])

  const clearErrors = useCallback(() => {
    // Implementation depends on how you want to clear errors globally
    console.log('Clearing global errors')
  }, [])

  const value = React.useMemo(() => ({
    reportError,
    showError,
    clearErrors
  }), [reportError, showError, clearErrors])

  return (
    <ErrorContext.Provider value={value}>
      {children}
    </ErrorContext.Provider>
  )
}

export function useErrorContext() {
  const context = useContext(ErrorContext)
  if (!context) {
    throw new Error('useErrorContext must be used within an ErrorProvider')
  }
  return context
}