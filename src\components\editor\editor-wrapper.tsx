'use client'

import React from 'react'
import { APIErrorBoundary } from '@/components/error/api-error-boundary'
import { EditorPanelSkeleton } from '@/components/loading/skeleton-loader'
import { AILoader } from '@/components/loading/loading-spinner'
import { useAPICall } from '@/hooks/use-error-handling'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertTriangle, RefreshCw, FileText, Wand2, Save, Upload } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { config } from '@/lib/config'

interface EditorWrapperProps {
  children: React.ReactNode
  chapterId?: string
  projectId?: string
  onError?: (error: Error) => void
  onSave?: () => void
  autoSave?: boolean
  autoSaveInterval?: number
}

function EditorErrorFallback({ 
  error, 
  retry, 
  chapterId 
}: { 
  error: Error
  retry: () => void
  chapterId?: string 
}) {
  return (
    <Card className="m-4">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
          <FileText className="h-6 w-6 text-red-600" />
        </div>
        <CardTitle>Editor Failed to Load</CardTitle>
        <CardDescription>
          There was an error loading the editor. Your work is safe.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error?.message && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription>{error.message}</AlertDescription>
          </Alert>
        )}
        
        <div className="flex gap-2">
          <Button onClick={retry} variant="outline" className="flex-1">
            <RefreshCw className="h-4 w-4 mr-2" />
            Reload Editor
          </Button>
          {chapterId && (
            <Button 
              onClick={() => window.open(`/api/chapters/${chapterId}/export`, '_blank')}
              variant="outline"
              className="flex-1"
            >
              <Upload className="h-4 w-4 mr-2" />
              Export Backup
            </Button>
          )}
        </div>
        
        <p className="text-xs text-muted-foreground text-center">
          Your content is automatically saved and can be recovered.
        </p>
      </CardContent>
    </Card>
  )
}

function EditorLoadingFallback() {
  return (
    <div className="h-full w-full">
      <EditorPanelSkeleton />
    </div>
  )
}

export function EditorWrapper({
  children,
  chapterId,
  onError,
  onSave,
  autoSave = true,
  autoSaveInterval = 30000 // 30 seconds
}: EditorWrapperProps) {
  const { callAPI } = useAPICall()
  const [lastSaved, setLastSaved] = React.useState<Date | null>(null)
  const [isSaving, setIsSaving] = React.useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false)
  const autoSaveTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)

  // Auto-save functionality
  const performSave = React.useCallback(async () => {
    if (!chapterId || isSaving) return

    try {
      setIsSaving(true)
      
      // Get content from editor (this would need to be implemented based on your editor)
      const content = (window as any).editorContent || ''
      
      await callAPI(`/api/chapters/${chapterId}`, {
        method: 'PUT',
        body: JSON.stringify({ content })
      })
      
      setLastSaved(new Date())
      setHasUnsavedChanges(false)
      onSave?.()
      
      toast({
        title: "Saved",
        description: "Your changes have been saved automatically.",
        duration: 2000
      })
      
    } catch (error) {
      console.error('Auto-save failed:', error)
      toast({
        variant: "destructive",
        title: "Save Failed",
        description: "Failed to save your changes. Please try manual save."
      })
    } finally {
      setIsSaving(false)
    }
  }, [chapterId, isSaving, callAPI, onSave])

  // Handle content changes for auto-save
  const handleContentChange = React.useCallback(() => {
    setHasUnsavedChanges(true)
    
    if (autoSave) {
      // Clear existing timeout
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
      
      // Set new timeout
      autoSaveTimeoutRef.current = setTimeout(() => {
        performSave()
      }, autoSaveInterval)
    }
  }, [autoSave, autoSaveInterval, performSave])

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
    }
  }, [])

  // Save before page unload if there are unsaved changes
  React.useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return e.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  const handleError = React.useCallback((error: Error) => {
    console.error('Editor error:', error)
    onError?.(error)
    
    // Show toast for editor errors
    toast({
      variant: "destructive",
      title: "Editor Error",
      description: "An error occurred in the editor. Your work has been preserved."
    })
  }, [onError])

  const handleRetry = React.useCallback(() => {
    window.location.reload()
  }, [])

  return (
    <APIErrorBoundary
      fallbackComponent={({ error, retry }) => (
        <EditorErrorFallback 
          error={error} 
          retry={retry} 
          chapterId={chapterId} 
        />
      )}
      onRetry={handleRetry}
      retryable={true}
      showErrorDetails={config.isDevelopment}
    >
      <React.Suspense fallback={<EditorLoadingFallback />}>
        <div className="relative h-full">
          {/* Save indicator */}
          {(isSaving || hasUnsavedChanges || lastSaved) && (
            <div className="absolute top-2 right-2 z-10 flex items-center gap-2 bg-background/80 backdrop-blur-sm border rounded-md px-3 py-1 text-xs">
              {isSaving ? (
                <>
                  <Save className="h-3 w-3 animate-spin" />
                  <span>Saving...</span>
                </>
              ) : hasUnsavedChanges ? (
                <>
                  <div className="h-2 w-2 bg-amber-500 rounded-full" />
                  <span>Unsaved changes</span>
                </>
              ) : lastSaved ? (
                <>
                  <div className="h-2 w-2 bg-green-500 rounded-full" />
                  <span>Saved {lastSaved.toLocaleTimeString()}</span>
                </>
              ) : null}
            </div>
          )}
          
          {/* Enhanced children with error handling */}
          {React.Children.map(children, child => {
            if (React.isValidElement(child)) {
              return React.cloneElement(child, {
                onContentChange: handleContentChange,
                onError: handleError
              } as any)
            }
            return child
          })}
        </div>
      </React.Suspense>
    </APIErrorBoundary>
  )
}

// AI Panel Wrapper for handling AI suggestion errors
export function AIPanelWrapper({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <APIErrorBoundary
      fallbackComponent={({ retry }) => (
        <Card className="h-full">
          <CardHeader className="text-center">
            <div className="mx-auto mb-2 p-2 bg-red-100 rounded-full w-fit">
              <Wand2 className="h-4 w-4 text-red-600" />
            </div>
            <CardTitle className="text-sm">AI Assistant Unavailable</CardTitle>
            <CardDescription className="text-xs">
              The AI assistant is temporarily unavailable.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={retry} variant="outline" size="sm" className="w-full">
              <RefreshCw className="h-3 w-3 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      )}
      retryable={true}
    >
      <React.Suspense fallback={<AILoader text="Loading AI assistant..." />}>
        {children}
      </React.Suspense>
    </APIErrorBoundary>
  )
}

// Chapter Navigator Wrapper
export function ChapterNavigatorWrapper({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <APIErrorBoundary
      fallbackComponent={({ retry }) => (
        <div className="p-4 border rounded-lg">
          <div className="text-center space-y-2">
            <AlertTriangle className="h-4 w-4 mx-auto text-amber-500" />
            <p className="text-sm font-medium">Navigation Error</p>
            <p className="text-xs text-muted-foreground">
              Failed to load chapter navigation.
            </p>
            <Button onClick={retry} variant="outline" size="sm">
              <RefreshCw className="h-3 w-3 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      )}
      retryable={true}
    >
      <React.Suspense fallback={
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="h-8 bg-muted animate-pulse rounded" />
          ))}
        </div>
      }>
        {children}
      </React.Suspense>
    </APIErrorBoundary>
  )
}