import { BaseService } from './base-service';
import { ServiceResponse } from './types';
import { createClient } from '../supabase/client';
import OpenAI from 'openai';
import { config } from '@/lib/config';

interface SearchResult {
  id: string;
  content: string;
  metadata: {
    projectId: string;
    chapterId?: string;
    type: 'chapter' | 'character' | 'scene' | 'note' | 'world' | 'plot';
    title?: string;
    tags?: string[];
    relevance: number;
  };
  similarity: number;
}

interface EmbeddingJob {
  id: string;
  projectId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processedCount: number;
  totalCount: number;
  error?: string;
}

export class SemanticSearchService extends BaseService {
  private openai: OpenAI;
  private supabase = createClient();
  private embeddingJobs: Map<string, EmbeddingJob> = new Map();
  private embeddingCache: Map<string, number[]> = new Map();

  constructor() {
    super({
      name: 'semantic-search',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/search/semantic', '/api/search/embeddings'],
      dependencies: ['context-manager'],
      healthCheck: '/api/search/health'
    });

    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });
  }

  async initialize(): Promise<void> {
    await this.ensureVectorExtension();
    await this.createEmbeddingTables();
    this.startEmbeddingProcessor();
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    const activeJobs = Array.from(this.embeddingJobs.values()).filter(
      job => job.status === 'processing'
    ).length;

    return this.createResponse(true, {
      status: `${activeJobs} active embedding jobs, ${this.embeddingCache.size} cached embeddings`,
      uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now()),
    });
  }

  async shutdown(): Promise<void> {
    this.embeddingJobs.clear();
    this.embeddingCache.clear();
    this.setStatus('inactive');
  }

  async search(query: string, options: {
    projectId?: string;
    types?: string[];
    limit?: number;
    threshold?: number;
    tags?: string[];
  } = {}): Promise<ServiceResponse<SearchResult[]>> {
    return this.withErrorHandling(async () => {
      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);
      
      // Build the search query
      let searchQuery = this.supabase.rpc('search_content_vectors', {
        query_embedding: queryEmbedding,
        match_threshold: options.threshold || 0.7,
        match_count: options.limit || 10
      });

      // Apply filters
      if (options.projectId) {
        searchQuery = searchQuery.eq('project_id', options.projectId);
      }

      if (options.types && options.types.length > 0) {
        searchQuery = searchQuery.in('content_type', options.types);
      }

      if (options.tags && options.tags.length > 0) {
        searchQuery = searchQuery.contains('tags', options.tags);
      }

      const { data, error } = await searchQuery;

      if (error) throw error;

      // Transform results
      const results: SearchResult[] = (data || []).map((item: Record<string, unknown>) => ({
        id: item.id,
        content: item.content,
        metadata: {
          projectId: item.project_id,
          chapterId: item.chapter_id,
          type: item.content_type,
          title: item.title,
          tags: item.tags,
          relevance: item.similarity
        },
        similarity: item.similarity
      }));

      return results;
    });
  }

  async similarContent(contentId: string, options: {
    projectId?: string;
    limit?: number;
    excludeSelf?: boolean;
  } = {}): Promise<ServiceResponse<SearchResult[]>> {
    return this.withErrorHandling(async () => {
      // Get the content and its embedding
      const { data: content, error } = await this.supabase
        .from('content_embeddings')
        .select('*')
        .eq('id', contentId)
        .single();

      if (error || !content) throw new Error('Content not found');

      // Search for similar content
      let searchQuery = this.supabase.rpc('search_content_vectors', {
        query_embedding: content.embedding,
        match_threshold: 0.7,
        match_count: (options.limit || 10) + (options.excludeSelf ? 1 : 0)
      });

      if (options.projectId) {
        searchQuery = searchQuery.eq('project_id', options.projectId);
      }

      const { data, error: searchError } = await searchQuery;

      if (searchError) throw searchError;

      // Transform and filter results
      let results: SearchResult[] = (data || []).map((item: Record<string, unknown>) => ({
        id: item.id,
        content: item.content,
        metadata: {
          projectId: item.project_id,
          chapterId: item.chapter_id,
          type: item.content_type,
          title: item.title,
          tags: item.tags,
          relevance: item.similarity
        },
        similarity: item.similarity
      }));

      if (options.excludeSelf) {
        results = results.filter(r => r.id !== contentId);
      }

      return results.slice(0, options.limit || 10);
    });
  }

  async indexContent(content: {
    id: string;
    projectId: string;
    type: 'chapter' | 'character' | 'scene' | 'note' | 'world' | 'plot';
    title: string;
    content: string;
    metadata?: {
      chapterId?: string;
      tags?: string[];
      characters?: string[];
      locations?: string[];
    };
  }): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      // Generate embedding
      const embedding = await this.generateEmbedding(content.content);

      // Prepare metadata
      const metadata = {
        title: content.title,
        content_type: content.type,
        project_id: content.projectId,
        chapter_id: content.metadata?.chapterId,
        tags: content.metadata?.tags || [],
        characters: content.metadata?.characters || [],
        locations: content.metadata?.locations || [],
        indexed_at: new Date().toISOString()
      };

      // Upsert into embeddings table
      const { error } = await this.supabase
        .from('content_embeddings')
        .upsert({
          id: content.id,
          project_id: content.projectId,
          content: content.content,
          embedding,
          metadata,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      // Cache the embedding
      this.embeddingCache.set(content.id, embedding);

      return true;
    });
  }

  async indexProject(projectId: string): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const jobId = `embed_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const job: EmbeddingJob = {
        id: jobId,
        projectId,
        status: 'pending',
        processedCount: 0,
        totalCount: 0
      };

      this.embeddingJobs.set(jobId, job);

      // Start processing asynchronously
      this.processProjectEmbeddings(job);

      return jobId;
    });
  }

  async getIndexingStatus(jobId: string): Promise<ServiceResponse<EmbeddingJob | null>> {
    return this.withErrorHandling(async () => {
      return this.embeddingJobs.get(jobId) || null;
    });
  }

  async searchByEmotion(emotion: string, projectId: string, limit = 10): Promise<ServiceResponse<SearchResult[]>> {
    return this.withErrorHandling(async () => {
      // Create an emotion-focused query
      const emotionQuery = `Find content that expresses or evokes ${emotion} emotion, feelings of ${emotion}, or ${emotion} themes`;
      
      return (await this.search(emotionQuery, { projectId, limit })).data || [];
    });
  }

  async searchByTheme(theme: string, projectId: string, limit = 10): Promise<ServiceResponse<SearchResult[]>> {
    return this.withErrorHandling(async () => {
      // Create a theme-focused query
      const themeQuery = `Find content related to the theme of ${theme}, exploring ${theme}, or discussing ${theme}`;
      
      return (await this.search(themeQuery, { projectId, limit })).data || [];
    });
  }

  async findRelatedScenes(sceneDescription: string, projectId: string): Promise<ServiceResponse<{
    similar: SearchResult[];
    complementary: SearchResult[];
    contrasting: SearchResult[];
  }>> {
    return this.withErrorHandling(async () => {
      // Find similar scenes
      const similarResult = await this.search(sceneDescription, {
        projectId,
        types: ['scene', 'chapter'],
        limit: 5
      });

      // Find complementary scenes (that could follow or precede)
      const complementaryQuery = `Scenes that would naturally follow or precede: ${sceneDescription}`;
      const complementaryResult = await this.search(complementaryQuery, {
        projectId,
        types: ['scene', 'chapter'],
        limit: 5
      });

      // Find contrasting scenes (opposite mood/setting)
      const contrastingQuery = `Scenes with opposite mood, setting, or tone to: ${sceneDescription}`;
      const contrastingResult = await this.search(contrastingQuery, {
        projectId,
        types: ['scene', 'chapter'],
        limit: 5
      });

      return {
        similar: similarResult.data || [],
        complementary: complementaryResult.data || [],
        contrasting: contrastingResult.data || []
      };
    });
  }

  async searchCharacterMoments(characterName: string, projectId: string, options: {
    emotion?: string;
    action?: string;
    limit?: number;
  } = {}): Promise<ServiceResponse<SearchResult[]>> {
    return this.withErrorHandling(async () => {
      let query = `Scenes featuring ${characterName}`;
      
      if (options.emotion) {
        query += ` experiencing ${options.emotion} or showing ${options.emotion}`;
      }
      
      if (options.action) {
        query += ` ${options.action}`;
      }

      return (await this.search(query, {
        projectId,
        limit: options.limit || 10,
        types: ['chapter', 'scene']
      })).data || [];
    });
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    // Check cache first
    const cacheKey = this.hashText(text);
    if (this.embeddingCache.has(cacheKey)) {
      return this.embeddingCache.get(cacheKey)!;
    }

    // Generate new embedding
    const response = await this.openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: text.slice(0, 8191), // Limit to model's max input
    });

    if (!response.data || response.data.length === 0) {
      throw new Error('No embedding data returned from OpenAI');
    }

    const embedding = response.data[0].embedding;
    if (!embedding) {
      throw new Error('Invalid embedding format from OpenAI');
    }
    
    // Cache the result
    this.embeddingCache.set(cacheKey, embedding);

    return embedding;
  }

  private async processProjectEmbeddings(job: EmbeddingJob): Promise<void> {
    try {
      job.status = 'processing';

      // Get all content for the project
      const { data: chapters } = await this.supabase
        .from('chapters')
        .select('*')
        .eq('project_id', job.projectId);

      const { data: storyBible } = await this.supabase
        .from('story_bibles')
        .select('*')
        .eq('project_id', job.projectId)
        .single();

      const { data: notes } = await this.supabase
        .from('notes')
        .select('*')
        .eq('project_id', job.projectId);

      const allContent = [];

      // Process chapters
      if (chapters) {
        allContent.push(...chapters.map(ch => ({
          id: `chapter_${ch.id}`,
          type: 'chapter' as const,
          title: ch.title || `Chapter ${ch.chapter_number}`,
          content: ch.content || '',
          metadata: { chapterId: ch.id }
        })));
      }

      // Process characters
      if (storyBible?.character_data?.protagonists) {
        allContent.push(...(storyBible.character_data.protagonists as Array<Record<string, unknown>>).map((char: Record<string, unknown>) => ({
          id: `character_${char.id}`,
          type: 'character' as const,
          title: String(char.name || ''),
          content: `${String(char.name || '')}: ${String(char.description || '')} ${String(char.personality || '')} ${String(char.background || '')}`,
          metadata: { tags: ['character', String(char.role || '')] }
        })));
      }

      // Process world building
      if (storyBible?.world_data?.locations) {
        allContent.push(...(storyBible.world_data.locations as Array<Record<string, unknown>>).map((loc: Record<string, unknown>) => ({
          id: `location_${loc.id}`,
          type: 'world' as const,
          title: String(loc.name || ''),
          content: String(loc.description || ''),
          metadata: { tags: ['location', 'world-building'] }
        })));
      }

      // Process notes
      if (notes) {
        allContent.push(...notes.map(note => ({
          id: `note_${note.id}`,
          type: 'note' as const,
          title: note.title || 'Note',
          content: note.content || '',
          metadata: { tags: note.tags || [] }
        })));
      }

      job.totalCount = allContent.length;

      // Process in batches
      const batchSize = 10;
      for (let i = 0; i < allContent.length; i += batchSize) {
        const batch = allContent.slice(i, i + batchSize);
        
        await Promise.all(batch.map(async (item) => {
          await this.indexContent({
            ...item,
            projectId: job.projectId
          });
          job.processedCount++;
        }));
      }

      job.status = 'completed';
    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to process embeddings:', error);
    }
  }

  private hashText(text: string): string {
    // Simple hash function for caching
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return `embed_${hash}`;
  }

  private async ensureVectorExtension(): Promise<void> {
    // Enable pgvector extension if not already enabled
    try {
      await this.supabase.rpc('enable_vector_extension');
    } catch (error) {
      // Extension might already be enabled
      console.log('Vector extension status:', error);
    }
  }

  private async createEmbeddingTables(): Promise<void> {
    try {
      // Create content_embeddings table
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS content_embeddings (
          id TEXT PRIMARY KEY,
          project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
          content TEXT NOT NULL,
          embedding vector(1536),
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      await this.supabase.rpc('exec_sql', { sql: createTableQuery });

      // Create indexes
      const createIndexQueries = [
        `CREATE INDEX IF NOT EXISTS content_embeddings_project_idx ON content_embeddings(project_id);`,
        `CREATE INDEX IF NOT EXISTS content_embeddings_embedding_idx ON content_embeddings 
         USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);`
      ];

      for (const query of createIndexQueries) {
        await this.supabase.rpc('exec_sql', { sql: query });
      }

      // Create similarity search function
      const createFunctionQuery = `
        CREATE OR REPLACE FUNCTION search_content_vectors(
          query_embedding vector(1536),
          match_threshold float,
          match_count int
        )
        RETURNS TABLE (
          id TEXT,
          project_id UUID,
          content TEXT,
          metadata JSONB,
          similarity float
        )
        LANGUAGE plpgsql
        AS $$
        BEGIN
          RETURN QUERY
          SELECT
            ce.id,
            ce.project_id,
            ce.content,
            ce.metadata,
            1 - (ce.embedding <=> query_embedding) as similarity
          FROM content_embeddings ce
          WHERE 1 - (ce.embedding <=> query_embedding) > match_threshold
          ORDER BY ce.embedding <=> query_embedding
          LIMIT match_count;
        END;
        $$;
      `;
      
      await this.supabase.rpc('exec_sql', { sql: createFunctionQuery });
    } catch (error) {
      console.error('Error creating embedding tables:', error);
      throw new Error('Failed to create embedding tables');
    }
  }

  private startEmbeddingProcessor(): void {
    // Process embedding queue periodically
    setInterval(() => {
      // Clean up old completed jobs
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      Array.from(this.embeddingJobs.entries()).forEach(([jobId, job]) => {
        if (job.status === 'completed' && parseInt(job.id.split('_')[1]) < oneHourAgo) {
          this.embeddingJobs.delete(jobId);
        }
      });

      // Clean up old cache entries
      if (this.embeddingCache.size > 1000) {
        const entriesToRemove = this.embeddingCache.size - 800;
        const keys = Array.from(this.embeddingCache.keys());
        for (let i = 0; i < entriesToRemove; i++) {
          this.embeddingCache.delete(keys[i]);
        }
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  // Add missing methods for API compatibility
  async searchByThemes(theme: string, projectId: string, limit?: number): Promise<SearchResult[]> {
    const result = await this.searchByTheme(theme, projectId, limit);
    return result.data || [];
  }

  async findRelatedContent(
    content: string, 
    projectId: string, 
    type?: string, 
    limit?: number
  ): Promise<SearchResult[]> {
    const result = await this.search(content, {
      projectId,
      types: type ? [type] : undefined,
      limit: limit || 10
    });
    return result.data || [];
  }

  startEmbeddingJob(projectId: string): EmbeddingJob {
    const jobId = `embedding_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const job: EmbeddingJob = {
      id: jobId,
      projectId,
      status: 'pending',
      processedCount: 0,
      totalCount: 0
    };
    
    this.embeddingJobs.set(jobId, job);
    return job;
  }

  async searchNarrativeElements(
    query: string,
    projectId: string,
    options: { element?: string; limit?: number } = {}
  ): Promise<SearchResult[]> {
    const enhancedQuery = options.element ? `${query} ${options.element}` : query;
    const result = await this.search(enhancedQuery, {
      projectId,
      limit: options.limit || 10
    });
    return result.data || [];
  }
}