# Environment Setup Guide

## Required API Keys

Before running BookScribe in production, you need to configure the following environment variables in your `.env.local` file:

### 1. OpenAI API Key (REQUIRED)
```
OPENAI_API_KEY=sk-proj-your-actual-openai-key-here
```
- **Where to get it**: [OpenAI API Keys](https://platform.openai.com/api-keys)
- **Used for**: AI-powered chapter generation, character development, story analysis
- **Cost**: Pay-per-use based on OpenAI pricing

### 2. Supabase Service Role Key (REQUIRED)
```
SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key-here
```
- **Where to get it**: Supabase Dashboard → Project Settings → API
- **Used for**: Server-side database operations, bypassing RLS
- **Security**: Never expose this key to client-side code

### 3. Stripe Keys (REQUIRED for payments)
```
STRIPE_SECRET_KEY=sk_live_your-actual-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_live_your-actual-stripe-publishable-key
```
- **Where to get them**: [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
- **Used for**: Payment processing, subscriptions
- **Note**: Use test keys (sk_test_/pk_test_) for development

### 4. Google Gemini API Key (OPTIONAL)
```
GOOGLE_GEMINI_API_KEY=your-gemini-api-key-here
```
- **Where to get it**: [Google AI Studio](https://makersuite.google.com/app/apikey)
- **Used for**: Alternative AI model (fallback option)
- **Status**: Currently not fully implemented

## Environment Configuration

### Development
```bash
cp .env.example .env.local
# Edit .env.local with your actual API keys
```

### Production
Set environment variables in your deployment platform:
- Vercel: Project Settings → Environment Variables
- Netlify: Site Settings → Environment Variables
- Railway: Project → Variables

## Testing Configuration

Run this command to test your environment setup:
```bash
npm run test:env
```

## Security Checklist

- [ ] Never commit real API keys to version control
- [ ] Use different keys for development/staging/production
- [ ] Rotate keys regularly
- [ ] Monitor API usage and costs
- [ ] Set up billing alerts for OpenAI and Stripe

## Troubleshooting

### "OpenAI API key not found" Error
- Check that `OPENAI_API_KEY` is set in your environment
- Verify the key starts with `sk-proj-` or `sk-`
- Check OpenAI dashboard for key status

### "Supabase connection failed" Error
- Verify `SUPABASE_SERVICE_ROLE_KEY` is set
- Check Supabase project is active
- Verify RLS policies are configured

### "Stripe initialization failed" Error
- Check both secret and publishable keys are set
- Verify keys match your environment (test vs live)
- Check Stripe dashboard for key status