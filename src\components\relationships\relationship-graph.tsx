'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Heart, 
  Users, 
  Zap, 
  Shield, 
  Target, 
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw,
  Eye,
  Settings
} from 'lucide-react';

interface RelationshipNode {
  id: string;
  character: string;
  importance: number;
  centrality: number;
  connectionCount: number;
  clusterIds: string[];
  position?: { x: number; y: number };
}

interface RelationshipEdge {
  id: string;
  source: string;
  target: string;
  relationship: {
    type: { primary: string; secondary?: string };
    intensity: number;
    sentiment: 'positive' | 'negative' | 'neutral' | 'complex';
    status: string;
  };
  weight: number;
  bidirectional: boolean;
  tension: number;
}

interface RelationshipCluster {
  id: string;
  name: string;
  members: string[];
  type: string;
  strength: number;
  stability: number;
}

interface RelationshipGraphProps {
  projectId: string;
  chapterRange?: { start: number; end: number };
  onNodeSelect?: (character: string) => void;
  onEdgeSelect?: (relationshipId: string) => void;
}

export function RelationshipGraph({ 
  projectId, 
  chapterRange, 
  onNodeSelect, 
  onEdgeSelect 
}: RelationshipGraphProps) {
  const [nodes, setNodes] = useState<RelationshipNode[]>([]);
  const [edges, setEdges] = useState<RelationshipEdge[]>([]);
  const [clusters, setClusters] = useState<RelationshipCluster[]>([]);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Filters
  const [intensityFilter, setIntensityFilter] = useState([0, 1]);
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [showClusters, setShowClusters] = useState(true);
  const [layout, setLayout] = useState<'force' | 'circular' | 'hierarchical'>('force');

  const fetchGraphData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/relationships/graph', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId, chapterRange })
      });
      
      if (response.ok) {
        const data = await response.json();
        setNodes(data.nodes || []);
        setEdges(data.edges || []);
        setClusters(data.clusters || []);
      }
    } catch (error) {
      console.error('Error fetching relationship graph:', error);
    } finally {
      setLoading(false);
    }
  }, [projectId, chapterRange]);

  useEffect(() => {
    fetchGraphData();
  }, [fetchGraphData]);

  const filteredEdges = useMemo(() => {
    return edges.filter(edge => {
      const intensityMatch = edge.weight >= (intensityFilter[0] || 0) && edge.weight <= (intensityFilter[1] || 1);
      const typeMatch = typeFilter === 'all' || edge.relationship.type.primary === typeFilter;
      return intensityMatch && typeMatch;
    });
  }, [edges, intensityFilter, typeFilter]);

  const filteredNodes = useMemo(() => {
    const connectedNodeIds = new Set<string>();
    filteredEdges.forEach(edge => {
      connectedNodeIds.add(edge.source);
      connectedNodeIds.add(edge.target);
    });
    return nodes.filter(node => connectedNodeIds.has(node.character));
  }, [nodes, filteredEdges]);

  const getNodeColor = (node: RelationshipNode) => {
    if (selectedNode === node.character) return '#3b82f6';
    if (node.importance > 0.8) return '#ef4444';
    if (node.importance > 0.6) return '#f59e0b';
    if (node.importance > 0.4) return '#10b981';
    return '#6b7280';
  };

  const getNodeSize = (node: RelationshipNode) => {
    return Math.max(20, node.importance * 40);
  };

  const getEdgeColor = (edge: RelationshipEdge) => {
    if (selectedEdge === edge.id) return '#3b82f6';
    
    switch (edge.relationship.sentiment) {
      case 'positive': return '#10b981';
      case 'negative': return '#ef4444';
      case 'complex': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  const getEdgeWidth = (edge: RelationshipEdge) => {
    return Math.max(1, edge.weight * 5);
  };

  const getEdgeStyle = (edge: RelationshipEdge) => {
    if (edge.relationship.type.primary === 'rivalry' || edge.relationship.type.primary === 'conflict') {
      return 'dashed';
    }
    if (edge.relationship.type.primary === 'romantic') {
      return 'dotted';
    }
    return 'solid';
  };

  const getRelationshipIcon = (type: string) => {
    switch (type) {
      case 'romantic': return <Heart className="w-4 h-4" />;
      case 'family': return <Users className="w-4 h-4" />;
      case 'rivalry': return <Zap className="w-4 h-4" />;
      case 'alliance': return <Shield className="w-4 h-4" />;
      case 'mentorship': return <Target className="w-4 h-4" />;
      default: return <Users className="w-4 h-4" />;
    }
  };

  const getTrendIcon = (edge: RelationshipEdge) => {
    if (edge.tension > 0.6) return <TrendingDown className="w-3 h-3 text-red-500" />;
    if (edge.weight > 0.7) return <TrendingUp className="w-3 h-3 text-green-500" />;
    return <Minus className="w-3 h-3 text-gray-400" />;
  };

  const handleNodeClick = (character: string) => {
    setSelectedNode(character === selectedNode ? null : character);
    setSelectedEdge(null);
    onNodeSelect?.(character);
  };

  const handleEdgeClick = (edgeId: string) => {
    setSelectedEdge(edgeId === selectedEdge ? null : edgeId);
    setSelectedNode(null);
    onEdgeSelect?.(edgeId);
  };

  const selectedNodeData = selectedNode ? nodes.find(n => n.character === selectedNode) : null;
  const selectedEdgeData = selectedEdge ? edges.find(e => e.id === selectedEdge) : null;

  const relationshipTypes = Array.from(new Set(edges.map(e => e.relationship.type.primary)));

  // Simple force-directed layout simulation
  const simulateLayout = useCallback(() => {
    const width = 800;
    const height = 600;
    const centerX = width / 2;
    const centerY = height / 2;

    // Initialize positions if not set
    filteredNodes.forEach((node, index) => {
      if (!node.position) {
        const angle = (index * 2 * Math.PI) / filteredNodes.length;
        const radius = Math.min(width, height) * 0.3;
        node.position = {
          x: centerX + radius * Math.cos(angle),
          y: centerY + radius * Math.sin(angle)
        };
      }
    });

    // Simple force simulation (would use D3.js or similar in production)
    for (let iteration = 0; iteration < 50; iteration++) {
      filteredNodes.forEach(node => {
        let fx = 0, fy = 0;

        // Repulsion from other nodes
        filteredNodes.forEach(other => {
          if (node.id !== other.id && node.position && other.position) {
            const dx = node.position.x - other.position.x;
            const dy = node.position.y - other.position.y;
            const distance = Math.sqrt(dx * dx + dy * dy) || 1;
            const force = 1000 / (distance * distance);
            fx += (dx / distance) * force;
            fy += (dy / distance) * force;
          }
        });

        // Attraction from connected nodes
        filteredEdges.forEach(edge => {
          if (edge.source === node.character || edge.target === node.character) {
            const other = filteredNodes.find(n => 
              n.character === (edge.source === node.character ? edge.target : edge.source)
            );
            if (other && node.position && other.position) {
              const dx = other.position.x - node.position.x;
              const dy = other.position.y - node.position.y;
              const distance = Math.sqrt(dx * dx + dy * dy) || 1;
              const force = edge.weight * 0.1;
              fx += (dx / distance) * force;
              fy += (dy / distance) * force;
            }
          }
        });

        // Center attraction
        if (node.position) {
          const dx = centerX - node.position.x;
          const dy = centerY - node.position.y;
          fx += dx * 0.001;
          fy += dy * 0.001;

          // Apply forces
          node.position.x += fx * 0.1;
          node.position.y += fy * 0.1;

          // Keep in bounds
          node.position.x = Math.max(50, Math.min(width - 50, node.position.x));
          node.position.y = Math.max(50, Math.min(height - 50, node.position.y));
        }
      });
    }
  }, [filteredNodes, filteredEdges]);

  useEffect(() => {
    if (filteredNodes.length > 0 && layout === 'force') {
      simulateLayout();
    }
  }, [filteredNodes, filteredEdges, layout, simulateLayout]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Relationship Graph</h2>
          <p className="text-gray-600">
            Visualize character relationships and their evolution
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={fetchGraphData} disabled={loading} size="sm">
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5" />
            <span>Graph Controls</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Layout</label>
              <Select value={layout} onValueChange={(value: string) => setLayout(value as 'circular' | 'force' | 'hierarchical')}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="force">Force-Directed</SelectItem>
                  <SelectItem value="circular">Circular</SelectItem>
                  <SelectItem value="hierarchical">Hierarchical</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Relationship Type</label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {relationshipTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Intensity Range: {(intensityFilter[0] || 0).toFixed(1)} - {(intensityFilter[1] || 1).toFixed(1)}
              </label>
              <Slider
                value={intensityFilter}
                onValueChange={setIntensityFilter}
                min={0}
                max={1}
                step={0.1}
                className="w-full"
              />
            </div>

            <div className="flex items-center space-x-2 pt-6">
              <input
                type="checkbox"
                id="showClusters"
                checked={showClusters}
                onChange={(e) => setShowClusters(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="showClusters" className="text-sm font-medium">
                Show Clusters
              </label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Graph Visualization */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Relationship Network</CardTitle>
              <CardDescription>
                {filteredNodes.length} characters, {filteredEdges.length} relationships
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative bg-gray-50 rounded-lg" style={{ height: '600px' }}>
                <svg width="100%" height="100%" className="absolute inset-0">
                  {/* Clusters Background */}
                  {showClusters && clusters.map(cluster => {
                    const clusterNodes = filteredNodes.filter(n => cluster.members.includes(n.character));
                    if (clusterNodes.length < 2) return null;
                    
                    const positions = clusterNodes.map(n => n.position).filter(Boolean);
                    if (positions.length === 0) return null;
                    
                    const centerX = positions.reduce((sum, p) => sum + p!.x, 0) / positions.length;
                    const centerY = positions.reduce((sum, p) => sum + p!.y, 0) / positions.length;
                    const maxDistance = Math.max(...positions.map(p => 
                      Math.sqrt((p!.x - centerX) ** 2 + (p!.y - centerY) ** 2)
                    ));
                    
                    return (
                      <circle
                        key={cluster.id}
                        cx={centerX}
                        cy={centerY}
                        r={maxDistance + 30}
                        fill="rgba(59, 130, 246, 0.1)"
                        stroke="rgba(59, 130, 246, 0.2)"
                        strokeWidth="2"
                        strokeDasharray="5,5"
                      />
                    );
                  })}

                  {/* Edges */}
                  {filteredEdges.map(edge => {
                    const sourceNode = filteredNodes.find(n => n.character === edge.source);
                    const targetNode = filteredNodes.find(n => n.character === edge.target);
                    
                    if (!sourceNode?.position || !targetNode?.position) return null;
                    
                    return (
                      <line
                        key={edge.id}
                        x1={sourceNode.position.x}
                        y1={sourceNode.position.y}
                        x2={targetNode.position.x}
                        y2={targetNode.position.y}
                        stroke={getEdgeColor(edge)}
                        strokeWidth={getEdgeWidth(edge)}
                        strokeDasharray={getEdgeStyle(edge) === 'dashed' ? '5,5' : 
                                       getEdgeStyle(edge) === 'dotted' ? '2,2' : '0'}
                        className="cursor-pointer"
                        onClick={() => handleEdgeClick(edge.id)}
                      />
                    );
                  })}

                  {/* Nodes */}
                  {filteredNodes.map(node => {
                    if (!node.position) return null;
                    
                    return (
                      <g key={node.id}>
                        <circle
                          cx={node.position.x}
                          cy={node.position.y}
                          r={getNodeSize(node)}
                          fill={getNodeColor(node)}
                          stroke="white"
                          strokeWidth="2"
                          className="cursor-pointer hover:opacity-80"
                          onClick={() => handleNodeClick(node.character)}
                        />
                        <text
                          x={node.position.x}
                          y={node.position.y + getNodeSize(node) + 15}
                          textAnchor="middle"
                          className="text-xs font-medium fill-gray-700"
                        >
                          {node.character}
                        </text>
                      </g>
                    );
                  })}
                </svg>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Details Panel */}
        <div className="space-y-4">
          {/* Node Details */}
          {selectedNodeData && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>{selectedNodeData.character}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Importance:</span>
                    <Badge variant="outline">
                      {Math.round(selectedNodeData.importance * 100)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Centrality:</span>
                    <Badge variant="outline">
                      {Math.round(selectedNodeData.centrality * 100)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Connections:</span>
                    <Badge variant="outline">
                      {selectedNodeData.connectionCount}
                    </Badge>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Relationships</h4>
                  <div className="space-y-1">
                    {filteredEdges
                      .filter(e => e.source === selectedNodeData.character || e.target === selectedNodeData.character)
                      .map(edge => {
                        const other = edge.source === selectedNodeData.character ? edge.target : edge.source;
                        return (
                          <div key={edge.id} className="flex items-center justify-between text-sm">
                            <span>{other}</span>
                            <div className="flex items-center space-x-1">
                              {getRelationshipIcon(edge.relationship.type.primary)}
                              {getTrendIcon(edge)}
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Edge Details */}
          {selectedEdgeData && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {getRelationshipIcon(selectedEdgeData.relationship.type.primary)}
                  <span>Relationship</span>
                </CardTitle>
                <CardDescription>
                  {selectedEdgeData.source} � {selectedEdgeData.target}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Type:</span>
                    <Badge variant="outline">
                      {selectedEdgeData.relationship.type.primary}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Intensity:</span>
                    <Badge variant="outline">
                      {Math.round(selectedEdgeData.relationship.intensity * 100)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Sentiment:</span>
                    <Badge 
                      variant={selectedEdgeData.relationship.sentiment === 'positive' ? 'default' : 
                              selectedEdgeData.relationship.sentiment === 'negative' ? 'destructive' : 'outline'}
                    >
                      {selectedEdgeData.relationship.sentiment}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Status:</span>
                    <Badge variant="outline">
                      {selectedEdgeData.relationship.status}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Tension:</span>
                    <Badge variant={selectedEdgeData.tension > 0.6 ? 'destructive' : 'outline'}>
                      {Math.round(selectedEdgeData.tension * 100)}%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Legend */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="w-5 h-5" />
                <span>Legend</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-semibold mb-2">Node Colors</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span>Major characters (80%+ importance)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span>Important characters (60-80%)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Supporting characters (40-60%)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span>Minor characters (&lt;40%)</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Edge Colors</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-0.5 bg-green-500"></div>
                    <span>Positive relationship</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-0.5 bg-red-500"></div>
                    <span>Negative relationship</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-0.5 bg-purple-500"></div>
                    <span>Complex relationship</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-0.5 bg-gray-500"></div>
                    <span>Neutral relationship</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Edge Styles</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-0.5 bg-gray-500"></div>
                    <span>Normal relationship</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-0.5 bg-gray-500" style={{ backgroundImage: 'repeating-linear-gradient(to right, transparent, transparent 2px, #6b7280 2px, #6b7280 4px)' }}></div>
                    <span>Conflict/rivalry</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-0.5 bg-gray-500" style={{ backgroundImage: 'repeating-linear-gradient(to right, transparent, transparent 1px, #6b7280 1px, #6b7280 2px)' }}></div>
                    <span>Romantic relationship</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}