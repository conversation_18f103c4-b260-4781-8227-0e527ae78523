import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { orchestratorInstances } from '@/lib/agents/orchestrator-instances';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    const orchestrator = orchestratorInstances.get(projectId);
    
    if (!orchestrator) {
      return NextResponse.json(
        { error: 'No active orchestration found for this project' },
        { status: 404 }
      );
    }

    const progress = orchestrator.getOrchestrationStatus();
    
    return NextResponse.json({
      success: true,
      data: progress
    });

  } catch (error) {
    console.error('Error getting orchestration progress:', error);
    return NextResponse.json(
      { error: 'Failed to get orchestration progress' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { projectId, action } = body;
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    const orchestrator = orchestratorInstances.get(projectId);
    
    if (!orchestrator) {
      return NextResponse.json(
        { error: 'No active orchestration found for this project' },
        { status: 404 }
      );
    }

    switch (action) {
      case 'cancel':
        await orchestrator.cancelOrchestration();
        orchestratorInstances.delete(projectId);
        return NextResponse.json({
          success: true,
          message: 'Orchestration cancelled'
        });
        
      case 'pause':
        orchestrator.pauseOrchestration();
        return NextResponse.json({
          success: true,
          message: 'Orchestration paused'
        });
        
      case 'resume':
        orchestrator.resumeOrchestration();
        return NextResponse.json({
          success: true,
          message: 'Orchestration resumed'
        });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error controlling orchestration:', error);
    return NextResponse.json(
      { error: 'Failed to control orchestration' },
      { status: 500 }
    );
  }
}

