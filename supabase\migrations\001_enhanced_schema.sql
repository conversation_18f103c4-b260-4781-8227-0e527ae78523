-- Enhanced BookScribe database schema with comprehensive project settings

-- Drop existing tables if they exist
DROP TABLE IF EXISTS agent_logs;
DROP TABLE IF EXISTS chapters;
DROP TABLE IF EXISTS characters;
DROP TABLE IF EXISTS story_arcs;
DROP TABLE IF EXISTS selection_profiles;
DROP TABLE IF EXISTS reference_materials;
DROP TABLE IF EXISTS selection_analytics;
DROP TABLE IF EXISTS projects;

-- Projects table with comprehensive settings
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Genre & Style Selections
  primary_genre VARCHAR(100),
  subgenre VARCHAR(100),
  custom_genre TEXT,
  narrative_voice VARCHAR(50),
  tense VARCHAR(20),
  tone_options TEXT[], -- Array of selected tones
  writing_style VARCHAR(50),
  custom_style_description TEXT,
  
  -- Story Structure & Pacing
  structure_type VARCHAR(50),
  pacing_preference VARCHAR(50),
  chapter_structure VARCHAR(50),
  timeline_complexity VARCHAR(50),
  custom_structure_notes TEXT,
  
  -- Character & World Building
  protagonist_types TEXT[], -- Array of selected types
  antagonist_types TEXT[], -- Array of selected types
  character_complexity VARCHAR(50),
  character_arc_types TEXT[], -- Array of selected arc types
  custom_character_concepts TEXT,
  time_period VARCHAR(100),
  geographic_setting VARCHAR(100),
  world_type VARCHAR(100),
  magic_tech_level VARCHAR(50),
  custom_setting_description TEXT,
  
  -- Themes & Content
  major_themes TEXT[], -- Array of selected themes
  philosophical_themes TEXT[], -- Array of selected themes
  social_themes TEXT[], -- Array of selected themes
  custom_themes TEXT,
  target_audience VARCHAR(50),
  content_rating VARCHAR(20),
  content_warnings TEXT[], -- Array of warnings
  cultural_sensitivity_notes TEXT,
  
  -- Series & Scope
  project_scope VARCHAR(50),
  series_type VARCHAR(50),
  interconnection_level VARCHAR(50),
  custom_scope_description TEXT,
  
  -- Technical Specifications
  target_word_count INTEGER,
  current_word_count INTEGER DEFAULT 0,
  target_chapters INTEGER,
  chapter_count_type VARCHAR(20), -- 'fixed', 'flexible', 'scene_based'
  pov_character_count INTEGER,
  pov_character_type VARCHAR(50),
  
  -- Research & References
  research_needs TEXT[], -- Array of research requirements
  fact_checking_level VARCHAR(50),
  custom_research_notes TEXT,
  
  -- Initial story concept
  initial_concept TEXT,
  
  -- System Fields
  status TEXT DEFAULT 'planning',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Story structure and arcs
CREATE TABLE story_arcs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  structure_data JSONB NOT NULL, -- Complete story structure
  act_number INTEGER,
  description TEXT,
  key_events JSONB,
  plot_points JSONB,
  world_building JSONB,
  timeline JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Characters with comprehensive data
CREATE TABLE characters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  character_id VARCHAR(100) NOT NULL, -- Internal character ID from AI
  name TEXT NOT NULL,
  role TEXT NOT NULL, -- protagonist, antagonist, supporting, minor
  description TEXT,
  backstory TEXT,
  personality_traits JSONB,
  character_arc JSONB,
  relationships JSONB,
  voice_data JSONB, -- Speaking style, vocabulary, mannerisms
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(project_id, character_id)
);

-- Chapters with enhanced planning data
CREATE TABLE chapters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_number INTEGER NOT NULL,
  title TEXT,
  target_word_count INTEGER,
  actual_word_count INTEGER DEFAULT 0,
  outline TEXT,
  content TEXT,
  scenes_data JSONB, -- Scene breakdowns
  character_states JSONB, -- Character states at chapter start/end
  status TEXT DEFAULT 'planned', -- planned, writing, review, complete
  ai_notes JSONB, -- Objectives, conflicts, resolutions, etc.
  pov_character VARCHAR(100),
  plot_advancement JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(project_id, chapter_number)
);

-- AI Agent execution logs
CREATE TABLE agent_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  agent_type TEXT NOT NULL,
  input_data JSONB,
  output_data JSONB,
  execution_time INTEGER,
  status TEXT NOT NULL,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Selection profiles for reuse
CREATE TABLE selection_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  
  -- All the same selection fields as projects table
  primary_genre VARCHAR(100),
  subgenre VARCHAR(100),
  custom_genre TEXT,
  narrative_voice VARCHAR(50),
  tense VARCHAR(20),
  tone_options TEXT[],
  writing_style VARCHAR(50),
  custom_style_description TEXT,
  structure_type VARCHAR(50),
  pacing_preference VARCHAR(50),
  chapter_structure VARCHAR(50),
  timeline_complexity VARCHAR(50),
  custom_structure_notes TEXT,
  protagonist_types TEXT[],
  antagonist_types TEXT[],
  character_complexity VARCHAR(50),
  character_arc_types TEXT[],
  custom_character_concepts TEXT,
  time_period VARCHAR(100),
  geographic_setting VARCHAR(100),
  world_type VARCHAR(100),
  magic_tech_level VARCHAR(50),
  custom_setting_description TEXT,
  major_themes TEXT[],
  philosophical_themes TEXT[],
  social_themes TEXT[],
  custom_themes TEXT,
  target_audience VARCHAR(50),
  content_rating VARCHAR(20),
  content_warnings TEXT[],
  cultural_sensitivity_notes TEXT,
  project_scope VARCHAR(50),
  series_type VARCHAR(50),
  interconnection_level VARCHAR(50),
  custom_scope_description TEXT,
  target_word_count INTEGER,
  chapter_count_type VARCHAR(20),
  pov_character_count INTEGER,
  pov_character_type VARCHAR(50),
  research_needs TEXT[],
  fact_checking_level VARCHAR(50),
  custom_research_notes TEXT,
  
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Reference materials
CREATE TABLE reference_materials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  file_type VARCHAR(50), -- 'document', 'image', 'map', 'character_sheet', 'research'
  file_url TEXT, -- Supabase storage URL
  file_size INTEGER,
  mime_type VARCHAR(100),
  tags TEXT[], -- Array of tags for organization
  is_processed BOOLEAN DEFAULT false, -- For AI processing/embedding
  processing_status VARCHAR(50),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Selection analytics
CREATE TABLE selection_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  selection_profile_id UUID REFERENCES selection_profiles(id) ON DELETE SET NULL,
  event_type VARCHAR(50), -- 'profile_used', 'project_completed', 'project_abandoned'
  selection_data JSONB, -- Snapshot of selections used
  outcome_data JSONB, -- Success metrics, completion rate, etc.
  created_at TIMESTAMP DEFAULT NOW()
);

-- Story Bible table for context management
CREATE TABLE story_bibles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  structure_data JSONB, -- Complete story structure
  character_data JSONB, -- All character profiles and relationships
  world_data JSONB, -- World building elements
  timeline_data JSONB, -- Complete timeline
  theme_tracking JSONB, -- Theme development across chapters
  continuity_data JSONB, -- Continuity tracking
  style_guide JSONB, -- Writing style consistency guide
  last_updated TIMESTAMP DEFAULT NOW(),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Chapter versions for revision tracking
CREATE TABLE chapter_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  content TEXT NOT NULL,
  word_count INTEGER,
  changes_summary TEXT,
  quality_score JSONB, -- Editorial review scores
  created_by VARCHAR(50), -- 'user', 'ai_writer', 'ai_editor'
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(chapter_id, version_number)
);

-- Writing sessions for analytics
CREATE TABLE writing_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  session_type VARCHAR(50), -- 'writing', 'editing', 'planning'
  words_written INTEGER DEFAULT 0,
  words_edited INTEGER DEFAULT 0,
  duration_minutes INTEGER,
  ai_assistance_used BOOLEAN DEFAULT false,
  ai_suggestions_accepted INTEGER DEFAULT 0,
  ai_suggestions_rejected INTEGER DEFAULT 0,
  session_notes TEXT,
  started_at TIMESTAMP DEFAULT NOW(),
  ended_at TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_chapters_project_id ON chapters(project_id);
CREATE INDEX idx_chapters_status ON chapters(status);
CREATE INDEX idx_characters_project_id ON characters(project_id);
CREATE INDEX idx_agent_logs_project_id ON agent_logs(project_id);
CREATE INDEX idx_agent_logs_status ON agent_logs(status);
CREATE INDEX idx_selection_profiles_user_id ON selection_profiles(user_id);
CREATE INDEX idx_selection_profiles_public ON selection_profiles(is_public);
CREATE INDEX idx_reference_materials_project_id ON reference_materials(project_id);
CREATE INDEX idx_story_bibles_project_id ON story_bibles(project_id);
CREATE INDEX idx_writing_sessions_project_id ON writing_sessions(project_id);
CREATE INDEX idx_writing_sessions_user_id ON writing_sessions(user_id);

-- Enable Row Level Security
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_arcs ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE selection_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE reference_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE selection_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_bibles ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own projects" ON projects FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own projects" ON projects FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own projects" ON projects FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own projects" ON projects FOR DELETE USING (auth.uid() = user_id);

-- Similar policies for other tables (abbreviated for space)
CREATE POLICY "Users can access own project data" ON story_arcs FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own project characters" ON characters FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own project chapters" ON chapters FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own agent logs" ON agent_logs FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own profiles" ON selection_profiles FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view public profiles" ON selection_profiles FOR SELECT USING (is_public = true);

CREATE POLICY "Users can access own reference materials" ON reference_materials FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access own analytics" ON selection_analytics FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access own story bibles" ON story_bibles FOR ALL USING (
  project_id IN (SELECT id FROM projects WHERE user_id = auth.uid())
);

CREATE POLICY "Users can access own chapter versions" ON chapter_versions FOR ALL USING (
  chapter_id IN (
    SELECT id FROM chapters WHERE project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
    )
  )
);

CREATE POLICY "Users can access own writing sessions" ON writing_sessions FOR ALL USING (auth.uid() = user_id);