import { createClient } from '../supabase/client';
import { BaseService } from './base-service';
import { ServiceResponse } from './types';
import { PlanAdjustment, AdaptivePlanningResult } from '../agents/adaptive-planning-agent';

export interface PlanAdjustmentContext {
  projectId: string;
  userId: string;
  chapterId: string;
  adjustmentData: AdaptivePlanningResult;
}

export interface AdjustmentSummary {
  chaptersModified: number[];
  charactersUpdated: string[];
  plotThreadsModified: string[];
  worldRulesChanged: string[];
  totalChanges: number;
  impactLevel: 'low' | 'medium' | 'high';
  adjustmentTimestamp: string;
}

export class PlanAdjustmentService extends BaseService {
  private supabase = createClient();

  constructor() {
    super({
      name: 'plan-adjustment',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/plan/adjust'],
      dependencies: ['database'],
      healthCheck: '/api/plan/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    return this.createResponse(true, {
      status: 'Plan adjustment service ready',
      uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now())
    });
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async applyPlanAdjustments(context: PlanAdjustmentContext): Promise<ServiceResponse<AdjustmentSummary>> {
    return this.withErrorHandling(async () => {
      const { projectId, userId, chapterId, adjustmentData } = context;

      if (!adjustmentData.needsAdjustment || adjustmentData.adjustments.length === 0) {
        return {
          chaptersModified: [],
          charactersUpdated: [],
          plotThreadsModified: [],
          worldRulesChanged: [],
          totalChanges: 0,
          impactLevel: 'low',
          adjustmentTimestamp: new Date().toISOString()
        };
      }

      const summary: AdjustmentSummary = {
        chaptersModified: [],
        charactersUpdated: [],
        plotThreadsModified: [],
        worldRulesChanged: [],
        totalChanges: 0,
        impactLevel: this.calculateOverallImpact(adjustmentData.adjustments),
        adjustmentTimestamp: new Date().toISOString()
      };

      // Process each adjustment
      for (const adjustment of adjustmentData.adjustments) {
        await this.processAdjustment(projectId, userId, adjustment, summary);
      }

      // Log the adjustment for audit trail
      await this.logAdjustment(projectId, userId, chapterId, adjustmentData, summary);

      // Update project metadata
      await this.updateProjectMetadata(projectId, summary);

      return summary;
    });
  }

  private async processAdjustment(
    projectId: string,
    _userId: string,
    adjustment: PlanAdjustment,
    summary: AdjustmentSummary
  ): Promise<void> {
    // Apply chapter-specific changes
    if (adjustment.specificChanges && adjustment.specificChanges.length > 0) {
      await this.applyChapterChanges(projectId, adjustment.specificChanges, summary);
    }

    // Update character states
    if (adjustment.characterStateUpdates && adjustment.characterStateUpdates.length > 0) {
      await this.updateCharacterStates(projectId, adjustment.characterStateUpdates, summary);
    }

    // Update plot threads
    if (adjustment.plotThreadUpdates && adjustment.plotThreadUpdates.length > 0) {
      await this.updatePlotThreads(projectId, adjustment.plotThreadUpdates, summary);
    }

    // Update world rules
    if (adjustment.worldRuleUpdates && adjustment.worldRuleUpdates.length > 0) {
      await this.updateWorldRules(projectId, adjustment.worldRuleUpdates, summary);
    }

    summary.totalChanges++;
  }

  private async applyChapterChanges(
    projectId: string,
    changes: PlanAdjustment['specificChanges'],
    summary: AdjustmentSummary
  ): Promise<void> {
    for (const change of changes) {
      const { chapterNumber, adjustments } = change;

      // Get current chapter outline
      const { data: chapter, error } = await this.supabase
        .from('chapters')
        .select('*')
        .eq('project_id', projectId)
        .eq('chapter_number', chapterNumber)
        .single();

      if (error || !chapter) {
        console.warn(`Chapter ${chapterNumber} not found for adjustment`);
        continue;
      }

      const outline = chapter.outline ? JSON.parse(chapter.outline) : {};
      let hasChanges = false;

      // Apply each field adjustment
      for (const adjustment of adjustments) {
        const { field, newValue, reason } = adjustment;
        
        if (this.shouldApplyFieldChange(outline, field, newValue)) {
          this.setNestedValue(outline, field, newValue);
          hasChanges = true;
          
          // Log the change reason
          if (!outline.adjustmentLog) outline.adjustmentLog = [];
          outline.adjustmentLog.push({
            timestamp: new Date().toISOString(),
            field,
            change: newValue,
            reason,
            source: 'adaptive_planning'
          });
        }
      }

      // Update chapter if changes were made
      if (hasChanges) {
        const { error: updateError } = await this.supabase
          .from('chapters')
          .update({
            outline: JSON.stringify(outline),
            updated_at: new Date().toISOString()
          })
          .eq('id', chapter.id);

        if (!updateError) {
          summary.chaptersModified.push(chapterNumber);
        }
      }
    }
  }

  private async updateCharacterStates(
    projectId: string,
    updates: PlanAdjustment['characterStateUpdates'],
    summary: AdjustmentSummary
  ): Promise<void> {
    for (const update of updates) {
      const { characterId, updates: stateUpdates } = update;

      // Get current character data
      const { data: character, error } = await this.supabase
        .from('characters')
        .select('*')
        .eq('id', characterId)
        .eq('project_id', projectId)
        .single();

      if (error || !character) {
        console.warn(`Character ${characterId} not found for state update`);
        continue;
      }

      const updateData: Record<string, unknown> = {
        updated_at: new Date().toISOString()
      };

      // Update emotional state
      if (stateUpdates.emotionalState) {
        updateData.personality_traits = {
          ...character.personality_traits,
          currentEmotionalState: stateUpdates.emotionalState,
          stateHistory: [
            ...(character.personality_traits?.stateHistory || []),
            {
              state: stateUpdates.emotionalState,
              timestamp: new Date().toISOString(),
              source: 'adaptive_planning'
            }
          ]
        };
      }

      // Update knowledge
      if (stateUpdates.knowledge) {
        updateData.character_arc = {
          ...character.character_arc,
          currentKnowledge: stateUpdates.knowledge,
          knowledgeHistory: [
            ...(character.character_arc?.knowledgeHistory || []),
            {
              knowledge: stateUpdates.knowledge,
              timestamp: new Date().toISOString(),
              source: 'adaptive_planning'
            }
          ]
        };
      }

      // Update relationships
      if (stateUpdates.relationships) {
        updateData.relationships = {
          ...character.relationships,
          currentState: stateUpdates.relationships,
          relationshipHistory: [
            ...(character.relationships?.relationshipHistory || []),
            {
              state: stateUpdates.relationships,
              timestamp: new Date().toISOString(),
              source: 'adaptive_planning'
            }
          ]
        };
      }

      // Update arc progression
      if (stateUpdates.arcProgression) {
        updateData.character_arc = {
          ...updateData.character_arc || character.character_arc,
          currentStage: stateUpdates.arcProgression,
          progressionHistory: [
            ...(character.character_arc?.progressionHistory || []),
            {
              stage: stateUpdates.arcProgression,
              timestamp: new Date().toISOString(),
              source: 'adaptive_planning'
            }
          ]
        };
      }

      // Apply updates
      const { error: updateError } = await this.supabase
        .from('characters')
        .update(updateData)
        .eq('id', characterId);

      if (!updateError) {
        summary.charactersUpdated.push(character.name);
      }
    }
  }

  private async updatePlotThreads(
    projectId: string,
    updates: PlanAdjustment['plotThreadUpdates'],
    summary: AdjustmentSummary
  ): Promise<void> {
    for (const update of updates) {
      const { threadId, updates: threadUpdates } = update;

      // Get current plot thread data
      const { data: thread, error } = await this.supabase
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)
        .eq('entry_type', 'plot_thread')
        .eq('entry_key', threadId)
        .single();

      if (error || !thread) {
        console.warn(`Plot thread ${threadId} not found for update`);
        continue;
      }

      const currentData = thread.entry_data || {};
      const updateData: Record<string, unknown> = {
        entry_data: {
          ...currentData,
          ...threadUpdates,
          updateHistory: [
            ...(currentData.updateHistory || []),
            {
              updates: threadUpdates,
              timestamp: new Date().toISOString(),
              source: 'adaptive_planning'
            }
          ]
        },
        updated_at: new Date().toISOString()
      };

      // Apply updates
      const { error: updateError } = await this.supabase
        .from('story_bible')
        .update(updateData)
        .eq('id', thread.id);

      if (!updateError) {
        summary.plotThreadsModified.push(threadId);
      }
    }
  }

  private async updateWorldRules(
    projectId: string,
    updates: PlanAdjustment['worldRuleUpdates'],
    summary: AdjustmentSummary
  ): Promise<void> {
    for (const update of updates) {
      const { rule, newValue, reason } = update;

      // Check if world rule exists
      const { data: existing } = await this.supabase
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)
        .eq('entry_type', 'world_rule')
        .eq('entry_key', rule)
        .single();

      if (existing) {
        // Update existing rule
        const { error: updateError } = await this.supabase
          .from('story_bible')
          .update({
            entry_data: {
              value: newValue,
              updateHistory: [
                ...(existing.entry_data?.updateHistory || []),
                {
                  oldValue: existing.entry_data?.value,
                  newValue,
                  reason,
                  timestamp: new Date().toISOString(),
                  source: 'adaptive_planning'
                }
              ]
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', existing.id);

        if (!updateError) {
          summary.worldRulesChanged.push(rule);
        }
      } else {
        // Create new rule
        const { error: insertError } = await this.supabase
          .from('story_bible')
          .insert({
            project_id: projectId,
            entry_type: 'world_rule',
            entry_key: rule,
            entry_data: {
              value: newValue,
              creationReason: reason,
              createdBy: 'adaptive_planning',
              timestamp: new Date().toISOString()
            }
          });

        if (!insertError) {
          summary.worldRulesChanged.push(rule);
        }
      }
    }
  }

  private async logAdjustment(
    projectId: string,
    userId: string,
    chapterId: string,
    adjustmentData: AdaptivePlanningResult,
    summary: AdjustmentSummary
  ): Promise<void> {
    try {
      await this.supabase
        .from('story_bible')
        .insert({
          project_id: projectId,
          entry_type: 'plan_adjustment_log',
          entry_key: `adjustment_${Date.now()}`,
          entry_data: {
            chapterId,
            userId,
            adjustmentData,
            summary,
            timestamp: new Date().toISOString()
          }
        });
    } catch (error) {
      console.warn('Failed to log plan adjustment:', error);
    }
  }

  private async updateProjectMetadata(projectId: string, summary: AdjustmentSummary): Promise<void> {
    try {
      // Update project's last modification time and adjustment count
      const { data: project } = await this.supabase
        .from('projects')
        .select('metadata')
        .eq('id', projectId)
        .single();

      const currentMetadata = project?.metadata || {};
      const adjustmentCount = (currentMetadata.planAdjustmentCount || 0) + 1;

      await this.supabase
        .from('projects')
        .update({
          updated_at: new Date().toISOString(),
          metadata: {
            ...currentMetadata,
            planAdjustmentCount: adjustmentCount,
            lastPlanAdjustment: summary.adjustmentTimestamp,
            lastAdjustmentImpact: summary.impactLevel
          }
        })
        .eq('id', projectId);
    } catch (error) {
      console.warn('Failed to update project metadata:', error);
    }
  }

  private calculateOverallImpact(adjustments: PlanAdjustment[]): 'low' | 'medium' | 'high' {
    if (adjustments.some(adj => adj.severity === 'major')) {
      return 'high';
    } else if (adjustments.some(adj => adj.severity === 'moderate')) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  private shouldApplyFieldChange(outline: Record<string, unknown>, field: string, newValue: unknown): boolean {
    const currentValue = this.getNestedValue(outline, field);
    
    // Don't apply if values are the same
    if (JSON.stringify(currentValue) === JSON.stringify(newValue)) {
      return false;
    }

    // Apply change if it's a meaningful update
    return true;
  }

  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current, key) => {
      if (current && typeof current === 'object' && key in current) {
        return (current as Record<string, unknown>)[key];
      }
      return undefined;
    }, obj as unknown);
  }

  private setNestedValue(obj: Record<string, unknown>, path: string, value: unknown): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current: Record<string, unknown>, key) => {
      if (!current[key]) current[key] = {};
      return current[key] as Record<string, unknown>;
    }, obj);
    
    target[lastKey] = value;
  }
}