# Serverless Deployment Notes

## Simplicity-First Approach

BookScribe is deployed to Vercel using the standard Node.js runtime for all routes. This approach prioritizes simplicity and maintainability.

## Changes Made for Deployment

### 1. Disabled CollaborationHub Service
- **Why**: WebSockets are not supported in serverless environments
- **Impact**: Real-time collaboration features are temporarily disabled
- **Future**: Can be replaced with Supabase Realtime when needed

### 2. No Edge Runtime
- **Decision**: All routes use Node.js runtime (default)
- **Benefits**: 
  - All npm packages work without compatibility checks
  - No code refactoring required
  - Easier debugging and maintenance
  - Can use any Node.js features

## Everything Else Works As-Is

- ✅ AI content generation
- ✅ File uploads
- ✅ Database operations
- ✅ Authentication
- ✅ Search functionality
- ✅ Export features
- ✅ All other microservices

## Future Optimizations (If Needed)

If performance becomes an issue, consider:

1. **Edge-Compatible Routes** (simple, stateless operations):
   - `/api/health`
   - `/api/test-auth`
   - Simple GET endpoints

2. **Supabase Realtime** for collaboration:
   - Replace WebSocket-based CollaborationHub
   - No infrastructure to manage
   - Works perfectly with serverless

## Deployment Checklist

1. ✅ CollaborationHub disabled in ServiceManager
2. ✅ vercel.json configured with appropriate timeouts
3. ✅ All environment variables set in Vercel dashboard
4. ✅ No hardcoded localhost references
5. ✅ No file system operations

## Notes

- The app is fully functional without real-time collaboration
- All AI features work with extended timeouts (up to 5 minutes on Pro plan)
- Database connections are handled automatically by Vercel
- No special configuration needed beyond standard Next.js deployment