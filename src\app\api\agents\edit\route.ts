import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { openai } from '@/lib/openai'
import { getClientIP } from '@/lib/rate-limiter'
import { AI_RATE_LIMITS, createAIRateLimitResponse } from '@/lib/rate-limiter-ai'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting check
    const clientIP = getClientIP(request)
    const { limiter, requests } = AI_RATE_LIMITS.generation
    const rateLimitResult = limiter.check(requests, clientIP)
    
    if (!rateLimitResult.success) {
      return createAIRateLimitResponse(rateLimitResult.reset)
    }
    
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, selectedText, prompt } = body

    if (!selectedText) {
      return NextResponse.json({ error: 'No text selected' }, { status: 400 })
    }

    let systemPrompt = ''
    let userPrompt = ''

    switch (action) {
      case 'improve':
        systemPrompt = 'You are an expert writing editor. Improve the given text by enhancing clarity, flow, and readability while maintaining the original meaning and voice.'
        userPrompt = `Please improve this text:\n\n"${selectedText}"\n\nProvide only the improved version without explanations.`
        break
        
      case 'expand':
        systemPrompt = 'You are an expert writing editor. Expand the given text by adding more detail, description, or development while maintaining the original style and voice.'
        userPrompt = `Please expand this text with more detail:\n\n"${selectedText}"\n\nProvide only the expanded version without explanations.`
        break
        
      case 'rewrite':
        systemPrompt = 'You are an expert writing editor. Completely rewrite the given text while maintaining the core meaning and improving the style and flow.'
        userPrompt = `Please rewrite this text:\n\n"${selectedText}"\n\nProvide only the rewritten version without explanations.`
        break
        
      case 'suggest':
        systemPrompt = 'You are an expert writing editor. Provide 2-3 alternative ways to write the given text, each with a different style or approach.'
        userPrompt = `Please provide alternative versions of this text:\n\n"${selectedText}"\n\nFormat as: Option 1: [text]\nOption 2: [text]\nOption 3: [text]`
        break
        
      case 'custom':
        if (!prompt) {
          return NextResponse.json({ error: 'Custom prompt required' }, { status: 400 })
        }
        systemPrompt = 'You are an expert writing editor. Follow the user\'s instructions to edit the given text.'
        userPrompt = `Original text: "${selectedText}"\n\nInstructions: ${prompt}\n\nProvide only the edited text without explanations.`
        break
        
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      temperature: 0.7,
      max_tokens: 2000,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ]
    })

    const editedText = response.choices[0]?.message?.content?.trim()

    if (!editedText) {
      return NextResponse.json({ error: 'No response from AI' }, { status: 500 })
    }

    // Log the editing session
    await supabase.from('editing_sessions').insert({
      user_id: user.id,
      selected_text: selectedText,
      ai_prompt: prompt || action,
      ai_response: editedText,
      action_type: action
    })

    return NextResponse.json({
      success: true,
      editedText,
      action,
      tokensUsed: response.usage?.total_tokens
    })

  } catch (error) {
    console.error('AI editing error:', error)
    return NextResponse.json(
      { error: 'Failed to process text' },
      { status: 500 }
    )
  }
}