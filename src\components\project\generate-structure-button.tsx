'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'

interface GenerateStructureButtonProps {
  projectId: string
}

export function GenerateStructureButton({ projectId }: GenerateStructureButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const router = useRouter()

  const handleGenerate = async () => {
    setIsGenerating(true)
    
    try {
      const response = await fetch('/api/agents/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          action: 'generate_structure'
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate structure')
      }

      // Refresh the page to show updated content
      router.refresh()
      
      // Optionally show success message
      alert('Project structure generated successfully!')
      
    } catch (error) {
      console.error('Generation error:', error)
      alert(error instanceof Error ? error.message : 'Failed to generate structure')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <Button 
      onClick={handleGenerate} 
      disabled={isGenerating}
      className="w-full"
    >
      {isGenerating ? 'Generating Story...' : 'Generate Story Structure'}
    </Button>
  )
}