import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'

const updateCollaboratorSchema = z.object({
  role: z.enum(['viewer', 'commenter', 'editor', 'admin']).optional(),
  status: z.enum(['pending', 'accepted', 'declined']).optional(),
})

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: collaboration, error } = await supabase
      .from('project_collaborators')
      .select('*, projects(title, description), users!project_collaborators_user_id_fkey(email, full_name)')
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Collaboration not found' }, { status: 404 })
      }
      console.error('Error fetching collaboration:', error)
      return NextResponse.json({ error: 'Failed to fetch collaboration' }, { status: 500 })
    }

    // Check if user has access to this collaboration
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', collaboration.project_id)
      .single()

    const isOwner = project?.user_id === user.id
    const isCollaborator = collaboration.user_email === user.email

    if (!isOwner && !isCollaborator) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    return NextResponse.json({ collaboration })
  } catch (error) {
    console.error('Error in collaboration GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const updateData = updateCollaboratorSchema.parse(body)

    // Get the collaboration details
    const { data: collaboration, error: fetchError } = await supabase
      .from('project_collaborators')
      .select('*, projects(user_id, title)')
      .eq('id', params.id)
      .single()

    if (fetchError || !collaboration) {
      return NextResponse.json({ error: 'Collaboration not found' }, { status: 404 })
    }

    // Determine permissions
    const isOwner = collaboration.projects?.user_id === user.id
    const isSelf = collaboration.user_email === user.email

    // Only project owner can change roles
    if (updateData.role && !isOwner) {
      return NextResponse.json({ error: 'Only project owner can change roles' }, { status: 403 })
    }

    // Only the invited user can accept/decline invitations
    if (updateData.status && !isSelf) {
      return NextResponse.json({ error: 'Only the invited user can respond to invitations' }, { status: 403 })
    }

    // Update the collaboration
    const { data: updated, error: updateError } = await supabase
      .from('project_collaborators')
      .update({
        ...updateData,
        ...(updateData.status === 'accepted' && { user_id: user.id }),
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating collaboration:', updateError)
      return NextResponse.json({ error: 'Failed to update collaboration' }, { status: 500 })
    }

    // Create notification for status changes
    if (updateData.status === 'accepted') {
      await supabase
        .from('notifications')
        .insert({
          user_id: collaboration.invited_by,
          type: 'collaboration_accepted',
          title: 'Invitation Accepted',
          message: `${user.email} has accepted your invitation to collaborate on "${collaboration.projects?.title}"`,
          data: { 
            project_id: collaboration.project_id,
            collaborator_email: user.email 
          },
        })
    } else if (updateData.status === 'declined') {
      await supabase
        .from('notifications')
        .insert({
          user_id: collaboration.invited_by,
          type: 'collaboration_declined',
          title: 'Invitation Declined',
          message: `${user.email} has declined your invitation to collaborate on "${collaboration.projects?.title}"`,
          data: { 
            project_id: collaboration.project_id,
            collaborator_email: user.email 
          },
        })
    }

    return NextResponse.json({ collaboration: updated })
  } catch (error) {
    console.error('Error in collaboration PATCH:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 })
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the collaboration details
    const { data: collaboration, error: fetchError } = await supabase
      .from('project_collaborators')
      .select('project_id, user_email, projects(user_id)')
      .eq('id', params.id)
      .single()

    if (fetchError || !collaboration) {
      return NextResponse.json({ error: 'Collaboration not found' }, { status: 404 })
    }

    // Check if user is the project owner or the collaborator themselves
    const projectData = collaboration.projects as { user_id: string } | { user_id: string }[]
    const isOwner = Array.isArray(projectData) ? projectData[0]?.user_id === user.id : projectData?.user_id === user.id
    const isSelf = collaboration.user_email === user.email

    if (!isOwner && !isSelf) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Delete the collaboration
    const { error: deleteError } = await supabase
      .from('project_collaborators')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting collaboration:', deleteError)
      return NextResponse.json({ error: 'Failed to delete collaboration' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: isSelf ? 'Left project successfully' : 'Collaborator removed' 
    })
  } catch (error) {
    console.error('Error in collaboration DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}