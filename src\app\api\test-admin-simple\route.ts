import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { config } from '@/lib/config'

export async function GET() {
  // Block access in production
  if (config.isProduction) {
    return NextResponse.json(
      { error: 'Not found' },
      { status: 404 }
    );
  }
  try {
    const supabaseUrl = config.supabase.url
    const serviceRoleKey = config.supabase.serviceRoleKey

    // Create admin client
    const supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Simple test - just try to connect
    const { data, error } = await supabase.auth.admin.listUsers()

    return NextResponse.json({
      success: true,
      message: 'Admin client created successfully',
      userCount: data?.users?.length || 0,
      error: error?.message || null
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Simple admin test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
