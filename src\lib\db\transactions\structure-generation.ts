import { createClient } from '@/lib/supabase/server'
import { CompensatingTransaction, batchInsert, batchDelete } from '../transactions'
import type { Database } from '../types'

interface StructureGenerationData {
  projectId: string
  storyStructure?: {
    acts: Array<{
      number: number
      description: string
      keyEvents: string[]
    }>
  }
  characters?: {
    protagonists?: Array<{
      name: string
      role: string
      description: string
      backstory: string
      personality: any
      arc: any
      relationships: any[]
    }>
    antagonists?: Array<{
      name: string
      role: string
      description: string
      backstory: string
      personality: any
      arc: any
      relationships: any[]
    }>
    supporting?: Array<{
      name: string
      role: string
      description: string
      backstory: string
      personality: any
      arc: any
      relationships: any[]
    }>
  }
  storyBible?: {
    world?: {
      rules?: string[]
    }
    continuity?: {
      plotThreads?: Array<{
        id: string
        description: string
        status: string
      }>
    }
    timeline?: Array<{
      id: string
      title: string
      description: string
      timestamp: string
      actNumber: number
      chapterNumber: number
    }>
  }
  chapterOutlines?: {
    chapters?: Array<{
      number: number
      title: string
      wordCountTarget: number
      [key: string]: any
    }>
  }
}

export async function generateStructureWithTransaction(
  data: StructureGenerationData
): Promise<{ success: boolean; error?: string }> {
  const { projectId } = data
  const supabase = await createClient()
  const compensation = new CompensatingTransaction()
  
  try {
    // Step 1: Backup existing data before deletion
    const backups = await backupExistingData(projectId)
    
    // Add rollback to restore data if something fails
    compensation.addRollback(async () => {
      await restoreBackupData(projectId, backups)
    })
    
    // Step 2: Delete existing data
    await Promise.all([
      data.storyStructure && supabase.from('story_arcs').delete().eq('project_id', projectId),
      data.characters && supabase.from('characters').delete().eq('project_id', projectId),
      data.storyBible && supabase.from('story_bible').delete().eq('project_id', projectId),
      data.chapterOutlines && supabase.from('chapters').delete().eq('project_id', projectId)
    ])
    
    // Step 3: Insert new story arcs
    if (data.storyStructure?.acts) {
      const arcs = data.storyStructure.acts.map(act => ({
        project_id: projectId,
        act_number: act.number,
        description: act.description,
        key_events: act.keyEvents
      }))
      
      const result = await batchInsert('story_arcs', arcs)
      if (!result.success) throw new Error(result.error)
    }
    
    // Step 4: Insert new characters
    if (data.characters) {
      const allCharacters = [
        ...(data.characters.protagonists || []),
        ...(data.characters.antagonists || []),
        ...(data.characters.supporting || [])
      ]
      
      if (allCharacters.length > 0) {
        const characters = allCharacters.map(character => ({
          project_id: projectId,
          name: character.name,
          role: character.role,
          description: character.description,
          backstory: character.backstory,
          personality_traits: character.personality,
          character_arc: character.arc,
          relationships: character.relationships
        }))
        
        const result = await batchInsert('characters', characters)
        if (!result.success) throw new Error(result.error)
      }
    }
    
    // Step 5: Insert story bible entries
    if (data.storyBible) {
      const entries: any[] = []
      
      // World rules
      if (data.storyBible.world?.rules) {
        data.storyBible.world.rules.forEach((rule, index) => {
          entries.push({
            project_id: projectId,
            entry_type: 'world_rule',
            entry_key: `rule_${index}`,
            entry_data: { value: rule }
          })
        })
      }
      
      // Plot threads
      if (data.storyBible.continuity?.plotThreads) {
        data.storyBible.continuity.plotThreads.forEach(thread => {
          entries.push({
            project_id: projectId,
            entry_type: 'plot_thread',
            entry_key: thread.id,
            entry_data: { description: thread.description, status: thread.status }
          })
        })
      }
      
      // Timeline events
      if (data.storyBible.timeline) {
        data.storyBible.timeline.forEach(event => {
          entries.push({
            project_id: projectId,
            entry_type: 'timeline_event',
            entry_key: event.id,
            entry_data: {
              title: event.title,
              description: event.description,
              timestamp: event.timestamp,
              actNumber: event.actNumber,
              chapterNumber: event.chapterNumber
            },
            chapter_introduced: event.chapterNumber
          })
        })
      }
      
      if (entries.length > 0) {
        const result = await batchInsert('story_bible', entries)
        if (!result.success) throw new Error(result.error)
      }
    }
    
    // Step 6: Insert chapter outlines
    if (data.chapterOutlines?.chapters) {
      const chapters = data.chapterOutlines.chapters.map(outline => ({
        project_id: projectId,
        chapter_number: outline.number,
        title: outline.title,
        target_word_count: outline.wordCountTarget,
        outline: JSON.stringify(outline),
        status: 'planned'
      }))
      
      const result = await batchInsert('chapters', chapters)
      if (!result.success) throw new Error(result.error)
    }
    
    // Step 7: Update project status
    const { error: updateError } = await supabase
      .from('projects')
      .update({ status: 'writing' })
      .eq('id', projectId)
    
    if (updateError) throw updateError
    
    return { success: true }
    
  } catch (error) {
    console.error('Structure generation transaction failed:', error)
    
    // Attempt rollback
    await compensation.rollback()
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Transaction failed'
    }
  }
}

// Helper functions for backup and restore
async function backupExistingData(projectId: string) {
  const supabase = await createClient()
  
  const [arcs, characters, bible, chapters] = await Promise.all([
    supabase.from('story_arcs').select('*').eq('project_id', projectId),
    supabase.from('characters').select('*').eq('project_id', projectId),
    supabase.from('story_bible').select('*').eq('project_id', projectId),
    supabase.from('chapters').select('*').eq('project_id', projectId)
  ])
  
  return {
    story_arcs: arcs.data || [],
    characters: characters.data || [],
    story_bible: bible.data || [],
    chapters: chapters.data || []
  }
}

async function restoreBackupData(projectId: string, backups: any) {
  const tasks = []
  
  if (backups.story_arcs.length > 0) {
    tasks.push(batchInsert('story_arcs', backups.story_arcs))
  }
  
  if (backups.characters.length > 0) {
    tasks.push(batchInsert('characters', backups.characters))
  }
  
  if (backups.story_bible.length > 0) {
    tasks.push(batchInsert('story_bible', backups.story_bible))
  }
  
  if (backups.chapters.length > 0) {
    tasks.push(batchInsert('chapters', backups.chapters))
  }
  
  await Promise.all(tasks)
}