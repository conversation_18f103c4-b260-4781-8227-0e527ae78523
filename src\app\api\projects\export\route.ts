import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { 
  generatePDFExport, 
  generateDOCXExport, 
  generateEPUBExport, 
  generateEnhancedTextExport,
  type ExportData,
  type ExportOptions 
} from '@/lib/export-utils'
import { trackUsage } from '@/lib/usage-tracker'
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for export operations (10 exports per hour)
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(10, clientIP);
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { projectId, format, options = {} } = body

    if (!projectId || !format) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Validate format
    const validFormats = ['txt', 'pdf', 'docx', 'epub']
    if (!validFormats.includes(format)) {
      return NextResponse.json({ error: 'Unsupported format' }, { status: 400 })
    }

    // Get project data
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Get chapters (include both complete and planned for outline exports)
    const { data: chapters } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')

    if (!chapters || chapters.length === 0) {
      return NextResponse.json({ error: 'No chapters found to export' }, { status: 400 })
    }

    // Filter to only completed chapters for content export (unless outline requested)
    const completedChapters = chapters.filter(ch => ch.status === 'complete' || ch.content)
    if (!options.includeOutline && completedChapters.length === 0) {
      return NextResponse.json({ error: 'No completed chapters to export' }, { status: 400 })
    }

    // Get characters for metadata
    const { data: characters } = await supabase
      .from('characters')
      .select('*')
      .eq('project_id', projectId)

    // Prepare export data
    const exportData: ExportData = {
      project,
      chapters: options.includeOutline ? chapters : completedChapters,
      characters: characters || []
    }

    const exportOptions: ExportOptions = {
      includeCharacters: options.includeCharacters !== false,
      includeOutline: options.includeOutline === true,
      pageBreakBetweenChapters: options.pageBreakBetweenChapters !== false,
      fontSize: options.fontSize || 12,
      fontFamily: options.fontFamily || 'Times'
    }

    // Generate content based on format
    let buffer: Buffer
    let mimeType: string
    let filename: string

    switch (format) {
      case 'txt':
        const textContent = generateEnhancedTextExport(exportData, exportOptions)
        buffer = Buffer.from(textContent, 'utf-8')
        mimeType = 'text/plain'
        filename = `${project.title.replace(/[^a-zA-Z0-9]/g, '_')}.txt`
        break
        
      case 'pdf':
        buffer = await generatePDFExport(exportData, exportOptions)
        mimeType = 'application/pdf'
        filename = `${project.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`
        break
        
      case 'docx':
        buffer = await generateDOCXExport(exportData, exportOptions)
        mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        filename = `${project.title.replace(/[^a-zA-Z0-9]/g, '_')}.docx`
        break
        
      case 'epub':
        buffer = await generateEPUBExport(exportData, exportOptions)
        mimeType = 'application/epub+zip'
        filename = `${project.title.replace(/[^a-zA-Z0-9]/g, '_')}.epub`
        break
        
      default:
        return NextResponse.json({ error: 'Unsupported format' }, { status: 400 })
    }

    // Track export usage
    await trackUsage({
      userId: user.id,
      eventType: 'export',
      metadata: { projectId, format, optionsUsed: Object.keys(options) }
    })
    
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': mimeType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': buffer.length.toString(),
      },
    })

  } catch (error) {
    console.error('Export error:', error)
    return NextResponse.json(
      { error: 'Failed to export project' },
      { status: 500 }
    )
  }
}