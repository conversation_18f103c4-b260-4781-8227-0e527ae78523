import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { suggestionId, action } = body

    if (!suggestionId || !action) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Store feedback in database for learning
    // This could be used to improve future suggestions
    const { error } = await supabase
      .from('ai_suggestion_feedback')
      .insert({
        suggestion_id: suggestionId,
        user_id: user.id,
        action,
        created_at: new Date().toISOString()
      })

    if (error) {
      console.log('Feedback storage failed (table may not exist):', error.message)
      // Don't fail the request if feedback storage fails
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Feedback error:', error)
    return NextResponse.json(
      { error: 'Failed to record feedback' },
      { status: 500 }
    )
  }
}