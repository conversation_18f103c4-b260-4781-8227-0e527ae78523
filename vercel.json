{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "functions": {"src/app/api/agents/*/route.ts": {"maxDuration": 300}, "src/app/api/content-analysis/route.ts": {"maxDuration": 300}, "src/app/api/intelligent-analysis/*/route.ts": {"maxDuration": 300}, "src/app/api/voice-analysis/*/route.ts": {"maxDuration": 300}, "src/app/api/orchestration/*/route.ts": {"maxDuration": 300}, "src/app/api/services/*/route.ts": {"maxDuration": 60}, "src/app/api/search/*/route.ts": {"maxDuration": 60}, "src/app/api/projects/*/export/route.ts": {"maxDuration": 120}, "src/app/api/story-bible/bulk/route.ts": {"maxDuration": 120}, "src/app/api/analysis/*/route.ts": {"maxDuration": 120}, "src/app/api/collaboration/*/route.ts": {"maxDuration": 30}}, "env": {"NODE_OPTIONS": "--max-old-space-size=4096"}, "regions": ["iad1"], "git": {"deploymentEnabled": true}}