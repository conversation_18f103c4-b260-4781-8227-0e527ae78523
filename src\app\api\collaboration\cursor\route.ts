import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { ServiceManager } from '@/lib/services/service-manager'

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const body = await request.json()
    const { sessionId, position } = body

    if (!sessionId || !position) {
      return NextResponse.json(
        { error: 'Session ID and position are required' },
        { status: 400 }
      )
    }

    if (typeof position.line !== 'number' || typeof position.column !== 'number') {
      return NextResponse.json(
        { error: 'Invalid position format' },
        { status: 400 }
      )
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationHub = await serviceManager.getCollaborationHub()
    if (!collaborationHub) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationHub.updateCursor(
      sessionId,
      authResult.user.id,
      position
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to update cursor' },
        { status: 400 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Collaboration cursor error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}