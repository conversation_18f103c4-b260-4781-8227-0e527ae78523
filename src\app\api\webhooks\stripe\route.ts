import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'
import { config } from '@/lib/config'
import type Stripe from 'stripe'

const webhookSecret = config.stripe.webhookSecret

// Validation schemas for webhook payloads
const checkoutSessionMetadataSchema = z.object({
  userId: z.string().uuid(),
  tierId: z.string()
})

const subscriptionSchema = z.object({
  id: z.string(),
  customer: z.string(),
  status: z.enum(['active', 'canceled', 'past_due', 'unpaid', 'incomplete', 'trialing']),
  current_period_start: z.number(),
  current_period_end: z.number(),
  cancel_at_period_end: z.boolean()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = await headers()
    const signature = headersList.get('stripe-signature')!

    let event
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }

    const supabase = await createClient()

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object
        
        // Validate metadata
        try {
          const metadata = checkoutSessionMetadataSchema.parse({
            userId: session.metadata?.userId,
            tierId: session.metadata?.tierId
          })
          
          const userId = metadata.userId
          const tierId = metadata.tierId

          // Create subscription record
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
          
          // Validate subscription data
          const validatedSub = subscriptionSchema.parse(subscription)
          
          await supabase.from('user_subscriptions').insert({
            user_id: userId,
            tier_id: tierId,
            status: 'active',
            stripe_subscription_id: validatedSub.id,
            stripe_customer_id: validatedSub.customer,
            current_period_start: new Date(validatedSub.current_period_start * 1000),
            current_period_end: new Date(validatedSub.current_period_end * 1000),
            cancel_at_period_end: validatedSub.cancel_at_period_end
          })

          // Subscription created successfully
        } catch (error) {
          // Invalid checkout session data
        }
        break
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        
        await supabase
          .from('user_subscriptions')
          .update({
            status: subscription.status,
            current_period_start: new Date((subscription as any).current_period_start * 1000),
            current_period_end: new Date((subscription as any).current_period_end * 1000),
            cancel_at_period_end: subscription.cancel_at_period_end
          })
          .eq('stripe_subscription_id', subscription.id)

        // Subscription updated
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        
        await supabase
          .from('user_subscriptions')
          .update({ status: 'canceled' })
          .eq('stripe_subscription_id', subscription.id)

        // Subscription canceled
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        
        // Reset usage for new billing period
        if (invoice.billing_reason === 'subscription_cycle') {
          const subscription = await stripe.subscriptions.retrieve((invoice as any).subscription as string)
          const { data: userSub } = await supabase
            .from('user_subscriptions')
            .select('user_id')
            .eq('stripe_subscription_id', subscription.id)
            .single()

          if (userSub) {
            const periodStart = new Date().toISOString().slice(0, 7)
            await supabase
              .from('usage_tracking')
              .upsert({
                user_id: userSub.user_id,
                period_start: periodStart,
                ai_generations: 0,
                projects: 0,
                exports: 0,
                storage_used: 0
              })
          }
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        
        await supabase
          .from('user_subscriptions')
          .update({ status: 'past_due' })
          .eq('stripe_subscription_id', (invoice as any).subscription as string)

        // Payment failed
        break
      }

      default:
        // Unhandled event type
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}