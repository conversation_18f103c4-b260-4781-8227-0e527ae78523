'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useWizardStore } from '@/stores/wizard-store'
import { StepBasics } from '@/components/project-wizard/step-basics'
import { StepGenreStyle } from '@/components/project-wizard/step-genre-style'
import { StepStructurePacing } from '@/components/project-wizard/step-structure-pacing'
import { StepCharactersWorld } from '@/components/project-wizard/step-characters-world'
import { StepThemesContent } from '@/components/project-wizard/step-themes-content'
import { StepTechnical } from '@/components/project-wizard/step-technical'
import { StepPayment } from '@/components/project-wizard/step-payment'
import { GuidedProjectCreation } from '@/components/onboarding/guided-project-creation'

export default function NewProjectPage() {
  const router = useRouter()
  const { currentStep, setCurrentStep, selections, resetWizard } = useWizardStore()
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const supabase = createClient()
  
  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return title.trim().length > 0
      case 2:
        return !!(selections.primaryGenre && selections.narrativeVoice && selections.tense)
      case 3:
        return !!(selections.structureType && selections.pacingPreference)
      case 4:
        return !!(selections.characterComplexity || selections.timePeriod)
      case 5:
        return !!(selections.targetAudience && selections.contentRating)
      case 6:
        return !!(selections.targetWordCount && selections.targetChapters)
      case 7:
        return true // Payment step always allows proceeding (handled internally)
      default:
        return false
    }
  }
  
  const handleNext = () => {
    if (canProceed() && currentStep < 7) {
      setCurrentStep(currentStep + 1)
    }
  }
  
  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }
  
  const handleSubmit = async () => {
    if (!canProceed()) return
    
    // If we're on the payment step, the payment component handles submission
    if (currentStep === 7) return
    
    setIsSubmitting(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')
      
      const projectData = {
        user_id: user.id,
        title,
        description,
        ...selections
      }
      
      const { data, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single()
      
      if (error) throw error
      
      resetWizard()
      router.push(`/projects/${data.id}`)
    } catch (error) {
      console.error('Error creating project:', error)
      alert('Error creating project. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const handlePaymentComplete = async () => {
    // Process the actual project creation after payment
    await handleSubmit()
  }
  
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <StepBasics
            title={title}
            description={description}
            onTitleChange={setTitle}
            onDescriptionChange={setDescription}
          />
        )
      case 2:
        return <StepGenreStyle />
      case 3:
        return <StepStructurePacing />
      case 4:
        return <StepCharactersWorld />
      case 5:
        return <StepThemesContent />
      case 6:
        return <StepTechnical />
      case 7:
        return <StepPayment onPaymentComplete={handlePaymentComplete} />
      default:
        return null
    }
  }
  
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between">
          <h1 className="text-2xl font-bold">Create New Project</h1>
          <button
            onClick={() => router.push('/dashboard')}
            className="text-muted-foreground hover:text-foreground"
          >
            Cancel
          </button>
        </div>
      </header>
      
      <main className="container py-8">
        <div className="mx-auto max-w-4xl">
          <GuidedProjectCreation
            currentStep={currentStep}
            totalSteps={7}
            onNext={handleNext}
            onPrevious={handlePrevious}
            onSubmit={handleSubmit}
            canProceed={canProceed()}
            isSubmitting={isSubmitting}
          >
            {renderCurrentStep()}
          </GuidedProjectCreation>
        </div>
      </main>
    </div>
  )
}