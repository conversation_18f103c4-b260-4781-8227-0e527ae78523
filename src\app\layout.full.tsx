import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Providers } from "./providers";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "BookScribe AI - AI-Powered Novel Writing IDE",
    template: "%s | BookScribe AI"
  },
  description: "Create epic novels with AI agents that maintain context across hundreds of thousands of words",
  keywords: ["AI writing", "novel writing", "book writing software", "AI author tools"],
  authors: [{ name: "BookScribe AI" }],
  creator: "BookScribe AI",
  publisher: "BookScribe AI",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://bookscribe.ai",
    siteName: "BookScribe AI",
    title: "BookScribe AI - AI-Powered Novel Writing IDE",
    description: "Create epic novels with AI agents that maintain context across hundreds of thousands of words",
    images: [{
      url: "/og-image.png",
      width: 1200,
      height: 630,
      alt: "BookScribe AI"
    }]
  },
  twitter: {
    card: "summary_large_image",
    title: "BookScribe AI - AI-Powered Novel Writing IDE",
    description: "Create epic novels with AI agents that maintain context across hundreds of thousands of words",
    images: ["/og-image.png"],
    creator: "@bookscribeai"
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/site.webmanifest",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}