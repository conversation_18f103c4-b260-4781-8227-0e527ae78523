import { RelationshipAnalyzer } from './relationship-analyzer';

// Shared relationship analyzer instances across routes
const relationshipAnalyzers = new Map<string, RelationshipAnalyzer>();

export function getRelationshipAnalyzer(projectId: string): RelationshipAnalyzer {
  if (!relationshipAnalyzers.has(projectId)) {
    relationshipAnalyzers.set(projectId, new RelationshipAnalyzer(projectId));
  }
  return relationshipAnalyzers.get(projectId)!;
}

export function removeRelationshipAnalyzer(projectId: string): boolean {
  return relationshipAnalyzers.delete(projectId);
}

export function hasRelationshipAnalyzer(projectId: string): boolean {
  return relationshipAnalyzers.has(projectId);
}