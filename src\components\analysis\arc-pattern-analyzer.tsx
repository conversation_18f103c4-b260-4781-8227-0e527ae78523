'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Brain,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Lightbulb,
  RefreshCw,
  Info
} from 'lucide-react';
import { ARC_PATTERNS } from '@/lib/types/character-development';
import type { ArcPattern } from '@/lib/types/character-development';

interface ArcPatternAnalyzerProps {
  characterId: string;
  projectId: string;
  onAnalysisUpdate?: (pattern: ArcPattern) => void;
}

export function ArcPatternAnalyzer({ 
  characterId, 
  projectId, 
  onAnalysisUpdate 
}: ArcPatternAnalyzerProps) {
  const [pattern, setPattern] = useState<ArcPattern | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const analyzePattern = useCallback(async () => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await fetch(`/api/analysis/character-arc-patterns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          characterId,
          projectId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to analyze character arc pattern');
      }

      const analysisResult = await response.json();
      setPattern(analysisResult);
      onAnalysisUpdate?.(analysisResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed');
    } finally {
      setIsAnalyzing(false);
    }
  }, [characterId, projectId, onAnalysisUpdate]);

  useEffect(() => {
    if (characterId && projectId) {
      analyzePattern();
    }
  }, [characterId, projectId, analyzePattern]);

  const getPatternInfo = useCallback((patternType: string) => {
    return ARC_PATTERNS.find(p => p.id === patternType);
  }, []);

  const getSeverityColor = useCallback((severity: string) => {
    switch (severity) {
      case 'minor': return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-950/20';
      case 'moderate': return 'text-orange-600 bg-orange-50 dark:bg-orange-950/20';
      case 'major': return 'text-red-600 bg-red-50 dark:bg-red-950/20';
      default: return 'text-gray-600 bg-gray-50 dark:bg-gray-950/20';
    }
  }, []);

  const getConfidenceLevel = useCallback((confidence: number) => {
    if (confidence >= 90) return { label: 'Very High', color: 'text-green-600' };
    if (confidence >= 75) return { label: 'High', color: 'text-blue-600' };
    if (confidence >= 60) return { label: 'Moderate', color: 'text-yellow-600' };
    if (confidence >= 40) return { label: 'Low', color: 'text-orange-600' };
    return { label: 'Very Low', color: 'text-red-600' };
  }, []);

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-red-600">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Analysis Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            onClick={analyzePattern} 
            className="mt-3"
            variant="outline"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry Analysis
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Analysis Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Brain className="w-5 h-5 mr-2" />
                Arc Pattern Analysis
              </CardTitle>
              <CardDescription>
                AI-powered character development pattern recognition
              </CardDescription>
            </div>
            <Button 
              onClick={analyzePattern} 
              disabled={isAnalyzing}
              variant="outline"
              size="sm"
            >
              {isAnalyzing ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              {isAnalyzing ? 'Analyzing...' : 'Re-analyze'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isAnalyzing ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                <span className="text-sm">Analyzing character development patterns...</span>
              </div>
              <Progress value={33} className="w-full" />
              <p className="text-xs text-gray-500">
                This may take a few moments as we analyze character progression across chapters.
              </p>
            </div>
          ) : pattern ? (
            <div className="space-y-6">
              {/* Pattern Match */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Detected Pattern</h4>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="text-sm">
                      {pattern.name}
                    </Badge>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="w-4 h-4 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="max-w-xs">
                            <p className="font-medium">{pattern.name}</p>
                            <p className="text-sm mt-1">
                              {getPatternInfo(pattern.type)?.phases.join(' → ')}
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Confidence Level</h4>
                  <div className="flex items-center space-x-2">
                    <Progress value={pattern.confidence} className="flex-1" />
                    <span className={`text-sm font-medium ${getConfidenceLevel(pattern.confidence).color}`}>
                      {pattern.confidence}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">
                    {getConfidenceLevel(pattern.confidence).label} confidence
                  </p>
                </div>
              </div>

              {/* Current Phase & Prediction */}
              {pattern.currentPhase && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      Current Phase
                    </h4>
                    <Badge variant="outline">
                      {pattern.currentPhase}
                    </Badge>
                  </div>

                  {pattern.predictedCompletion && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium flex items-center">
                        <Target className="w-4 h-4 mr-1" />
                        Predicted Completion
                      </h4>
                      <Badge variant="outline">
                        Chapters {pattern.predictedCompletion.minChapter}-{pattern.predictedCompletion.maxChapter}
                      </Badge>
                    </div>
                  )}
                </div>
              )}

              {/* Matched Segments */}
              {pattern.matchedSegments.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium flex items-center">
                    <CheckCircle className="w-4 h-4 mr-1 text-green-600" />
                    Pattern Matches
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {pattern.matchedSegments.map((segment, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        Ch. {segment.start}-{segment.end}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Brain className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No analysis available yet</p>
              <Button onClick={analyzePattern} className="mt-3" variant="outline">
                Start Analysis
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Deviations and Warnings */}
      {pattern && pattern.deviations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-yellow-600" />
              Arc Deviations
            </CardTitle>
            <CardDescription>
              Areas where the character strays from the expected arc pattern
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {pattern.deviations.map((deviation, index) => (
                <Alert key={index} className={getSeverityColor(deviation.severity)}>
                  <AlertTriangle className="h-4 w-4" />
                  <div className="ml-2">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        Chapter {deviation.chapter}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {deviation.dimension}
                      </Badge>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getSeverityColor(deviation.severity)}`}
                      >
                        {deviation.severity}
                      </Badge>
                    </div>
                    <AlertDescription className="text-sm">
                      <strong>Issue:</strong> {deviation.description}
                    </AlertDescription>
                    <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-950/20 rounded-sm">
                      <div className="flex items-start space-x-2">
                        <Lightbulb className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          <strong>Suggestion:</strong> {deviation.suggestion}
                        </p>
                      </div>
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pattern Progress */}
      {pattern && getPatternInfo(pattern.type) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Arc Progress
            </CardTitle>
            <CardDescription>
              Progress through the {pattern.name} pattern phases
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {getPatternInfo(pattern.type)?.phases.map((phase, index) => {
                const isCompleted = pattern.matchedSegments.some(
                  segment => index >= segment.start - 1 && index <= segment.end - 1
                );
                const isCurrent = pattern.currentPhase === phase;

                return (
                  <div 
                    key={index} 
                    className={`flex items-center space-x-3 p-2 rounded-lg ${
                      isCurrent ? 'bg-blue-50 dark:bg-blue-950/20' : 
                      isCompleted ? 'bg-green-50 dark:bg-green-950/20' : 
                      'bg-gray-50 dark:bg-gray-950/20'
                    }`}
                  >
                    <div className={`w-3 h-3 rounded-full ${
                      isCurrent ? 'bg-blue-500' :
                      isCompleted ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                    <span className={`text-sm ${
                      isCurrent ? 'font-medium text-blue-700 dark:text-blue-300' :
                      isCompleted ? 'text-green-700 dark:text-green-300' :
                      'text-gray-600'
                    }`}>
                      {phase}
                    </span>
                    {isCurrent && (
                      <Badge variant="secondary" className="text-xs">
                        Current
                      </Badge>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}