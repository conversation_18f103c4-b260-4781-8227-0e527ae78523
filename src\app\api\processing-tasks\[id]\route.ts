import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: task, error } = await supabase
      .from('processing_tasks')
      .select('*, projects(title, description)')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Task not found' }, { status: 404 })
      }
      console.error('Error fetching task:', error)
      return NextResponse.json({ error: 'Failed to fetch task' }, { status: 500 })
    }

    // Get task progress details if available
    let progress = null
    if (task.status === 'processing' && task.metadata?.progressKey) {
      const { data: progressData } = await supabase
        .from('task_progress')
        .select('*')
        .eq('task_id', params.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()
      
      progress = progressData
    }

    return NextResponse.json({ 
      task,
      progress 
    })
  } catch (error) {
    console.error('Error in processing task GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    if (action === 'cancel') {
      // Get the task first
      const { data: task } = await supabase
        .from('processing_tasks')
        .select('status, metadata')
        .eq('id', params.id)
        .eq('user_id', user.id)
        .single()

      if (!task) {
        return NextResponse.json({ error: 'Task not found' }, { status: 404 })
      }

      if (!['pending', 'processing'].includes(task.status)) {
        return NextResponse.json({ error: 'Task cannot be cancelled' }, { status: 400 })
      }

      // Cancel the task
      const { data: updated, error: updateError } = await supabase
        .from('processing_tasks')
        .update({ 
          status: 'cancelled',
          updated_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
          metadata: {
            ...(task.metadata || {}),
            cancelled_by: user.id,
            cancelled_at: new Date().toISOString()
          }
        })
        .eq('id', params.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error cancelling task:', updateError)
        return NextResponse.json({ error: 'Failed to cancel task' }, { status: 500 })
      }

      // TODO: Notify the processing service to stop the task

      return NextResponse.json({ 
        task: updated,
        message: 'Task cancelled successfully' 
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  } catch (error) {
    console.error('Error in processing task PATCH:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the task first
    const { data: task } = await supabase
      .from('processing_tasks')
      .select('status')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 })
    }

    if (!['completed', 'failed', 'cancelled'].includes(task.status)) {
      return NextResponse.json({ error: 'Cannot delete active tasks' }, { status: 400 })
    }

    // Delete associated progress records first
    await supabase
      .from('task_progress')
      .delete()
      .eq('task_id', params.id)

    // Delete the task
    const { error: deleteError } = await supabase
      .from('processing_tasks')
      .delete()
      .eq('id', params.id)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting task:', deleteError)
      return NextResponse.json({ error: 'Failed to delete task' }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: 'Task deleted' })
  } catch (error) {
    console.error('Error in processing task DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}