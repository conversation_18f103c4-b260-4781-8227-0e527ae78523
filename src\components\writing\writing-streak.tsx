'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Flame, Trophy, Target, TrendingUp, Calendar } from 'lucide-react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface WritingStreakData {
  currentStreak: number
  longestStreak: number
  todayWordCount: number
  dailyGoal: number
  weeklyStats: {
    day: string
    wordCount: number
    goalMet: boolean
  }[]
}

interface WritingStreakProps {
  userId: string
  className?: string
}

export function WritingStreak({ userId: _userId, className }: WritingStreakProps) {
  const [streakData] = useState<WritingStreakData>({
    currentStreak: 7,
    longestStreak: 15,
    todayWordCount: 1250,
    dailyGoal: 1000,
    weeklyStats: [
      { day: 'Mon', wordCount: 1200, goalMet: true },
      { day: 'Tue', wordCount: 850, goalMet: false },
      { day: 'Wed', wordCount: 1500, goalMet: true },
      { day: 'Thu', wordCount: 1100, goalMet: true },
      { day: 'Fri', wordCount: 1300, goalMet: true },
      { day: 'Sat', wordCount: 900, goalMet: false },
      { day: 'Sun', wordCount: 1250, goalMet: true },
    ]
  })

  const progressPercentage = Math.min(
    (streakData.todayWordCount / streakData.dailyGoal) * 100,
    100
  )

  const streakStatus = streakData.currentStreak >= 7 ? 'on-fire' : 
                      streakData.currentStreak >= 3 ? 'warming-up' : 
                      'just-started'

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Writing Streak</CardTitle>
          <div className="flex items-center gap-2">
            <motion.div
              animate={streakStatus === 'on-fire' ? {
                scale: [1, 1.2, 1],
                rotate: [0, 5, -5, 0],
              } : {}}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              <Flame className={cn(
                "h-5 w-5",
                streakStatus === 'on-fire' && "text-orange-500",
                streakStatus === 'warming-up' && "text-yellow-500",
                streakStatus === 'just-started' && "text-gray-400"
              )} />
            </motion.div>
            <span className="text-2xl font-bold">{streakData.currentStreak}</span>
            <span className="text-sm text-muted-foreground">days</span>
          </div>
        </div>
        <CardDescription>
          Keep your streak alive by writing {streakData.dailyGoal} words daily
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Today's Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              Today's Progress
            </span>
            <span className="font-medium">
              {streakData.todayWordCount} / {streakData.dailyGoal} words
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          {progressPercentage >= 100 && (
            <Badge variant="default" className="text-xs">
              <Trophy className="h-3 w-3 mr-1" />
              Goal achieved!
            </Badge>
          )}
        </div>

        {/* Weekly Overview */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            This Week
          </h4>
          <div className="flex gap-1">
            {streakData.weeklyStats.map((day, index) => (
              <div
                key={index}
                className="flex-1 text-center"
              >
                <div className="text-xs text-muted-foreground mb-1">
                  {day.day}
                </div>
                <div
                  className={cn(
                    "h-8 rounded-sm flex items-end justify-center relative overflow-hidden",
                    day.goalMet ? "bg-primary/20" : "bg-muted"
                  )}
                >
                  <div
                    className={cn(
                      "absolute bottom-0 left-0 right-0 transition-all",
                      day.goalMet ? "bg-primary" : "bg-muted-foreground/50"
                    )}
                    style={{
                      height: `${Math.min((day.wordCount / streakData.dailyGoal) * 100, 100)}%`
                    }}
                  />
                  {day.goalMet && (
                    <div className="relative z-10 mb-1">
                      <div className="w-1 h-1 bg-primary-foreground rounded-full" />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-3 pt-2 border-t">
          <div className="text-center">
            <p className="text-2xl font-bold">{streakData.longestStreak}</p>
            <p className="text-xs text-muted-foreground">Longest streak</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold flex items-center justify-center gap-1">
              <TrendingUp className="h-4 w-4 text-green-500" />
              85%
            </p>
            <p className="text-xs text-muted-foreground">Success rate</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}