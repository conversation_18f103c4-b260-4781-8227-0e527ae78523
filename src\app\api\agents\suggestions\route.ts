import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { openai } from '@/lib/openai'
import { getClientIP } from '@/lib/rate-limiter'
import { AI_RATE_LIMITS, createAIRateLimitResponse } from '@/lib/rate-limiter-ai'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting check
    const clientIP = getClientIP(request)
    const { limiter, requests } = AI_RATE_LIMITS.chat
    const rateLimitResult = limiter.check(requests, clientIP)
    
    if (!rateLimitResult.success) {
      return createAIRateLimitResponse(rateLimitResult.reset)
    }
    
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { type, beforeCursor } = body

    if (!type || !beforeCursor) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const suggestions = await generateSuggestions(type, beforeCursor)

    return NextResponse.json({ 
      success: true, 
      suggestions
    })

  } catch (error) {
    console.error('Error generating suggestions:', error)
    return NextResponse.json(
      { error: 'Failed to generate suggestions' },
      { status: 500 }
    )
  }
}

async function generateSuggestions(
  type: string, 
  beforeCursor: string, 
) {
  // const suggestions: string[] = []

  try {
    let prompt = ''
    const systemPrompt = 'You are a creative writing assistant helping authors improve their work.'

    switch (type) {
      case 'completion':
        prompt = `
Given this text context:
"${beforeCursor}"

Generate 3 different ways to continue this sentence or paragraph. Each completion should be 1-3 sentences long and maintain the style and tone. Return only the completion text, not the original context.

The completions should:
1. Be natural continuations of the existing text
2. Maintain consistent character voice and style
3. Advance the story or develop the current scene
4. Be between 10-50 words each

Return as a JSON array of objects with: text, confidence (0-1), context (brief explanation)
`
        break

      case 'improvement':
        const lastSentence = beforeCursor.split('.').slice(-2).join('.').trim()
        prompt = `
Analyze this text and suggest improvements:
"${lastSentence}"

Generate 2-3 alternative ways to write this text that improve:
- Clarity and readability
- Emotional impact
- Prose quality
- Show vs tell

Return as a JSON array of objects with: text, confidence (0-1), context (what was improved)
`
        break

      case 'continuation':
        prompt = `
Given this story context:
"${beforeCursor.slice(-200)}"

Suggest 2-3 ways the story could continue next. Each suggestion should be 1-2 paragraphs that:
1. Advance the plot naturally
2. Maintain character consistency
3. Create intrigue or develop conflict
4. Match the existing tone and style

Return as a JSON array of objects with: text, confidence (0-1), context (brief description of the direction)
`
        break

      default:
        throw new Error('Invalid suggestion type')
    }

    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      max_tokens: 800,
      temperature: 0.8,
    })

    const content = response.choices[0]?.message?.content
    if (content) {
      try {
        const parsedSuggestions = JSON.parse(content)
        return parsedSuggestions.map((suggestion: { text: string; context?: string; confidence?: number }, index: number) => ({
          id: `${type}-${Date.now()}-${index}`,
          type,
          text: suggestion.text,
          context: suggestion.context || 'AI-generated suggestion',
          confidence: suggestion.confidence || 0.8
        }))
      } catch {
        // Fallback: treat as single suggestion
        return [{
          id: `${type}-${Date.now()}`,
          type,
          text: content,
          context: 'AI-generated suggestion',
          confidence: 0.7
        }]
      }
    }

    return []

  } catch (error) {
    console.error('Error generating suggestions:', error)
    return []
  }
}