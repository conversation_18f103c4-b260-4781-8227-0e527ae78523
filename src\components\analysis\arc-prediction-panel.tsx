'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Sparkles,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Lightbulb,
  RefreshCw,
  Calendar,
  BarChart3,
  Zap,
  Info
} from 'lucide-react';
import { 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts';
import type { ArcPrediction } from '@/lib/types/character-development';
import type { ChartTooltipProps } from '@/lib/types/charts';

interface ArcPredictionPanelProps {
  characterId: string;
  projectId: string;
  currentChapter?: number;
  onPredictionUpdate?: (prediction: ArcPrediction) => void;
}

export function ArcPredictionPanel({ 
  characterId, 
  projectId, 
  currentChapter = 1,
  onPredictionUpdate 
}: ArcPredictionPanelProps) {
  const [prediction, setPrediction] = useState<ArcPrediction | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'short' | 'medium' | 'long'>('medium');

  const analyzePredictions = useCallback(async () => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await fetch(`/api/analysis/arc-predictions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          characterId,
          projectId,
          currentChapter,
          timeframe: selectedTimeframe
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate arc predictions');
      }

      const predictionResult = await response.json();
      setPrediction(predictionResult);
      onPredictionUpdate?.(predictionResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Prediction failed');
    } finally {
      setIsAnalyzing(false);
    }
  }, [characterId, projectId, currentChapter, selectedTimeframe, onPredictionUpdate]);

  useEffect(() => {
    if (characterId && projectId) {
      analyzePredictions();
    }
  }, [characterId, projectId, selectedTimeframe, analyzePredictions]);

  const getTrajectoryColor = (trajectory: string) => {
    switch (trajectory) {
      case 'positive': return 'text-green-600 bg-green-50 dark:bg-green-950/20';
      case 'negative': return 'text-red-600 bg-red-50 dark:bg-red-950/20';
      case 'stagnant': return 'text-gray-600 bg-gray-50 dark:bg-gray-950/20';
      case 'volatile': return 'text-orange-600 bg-orange-50 dark:bg-orange-950/20';
      default: return 'text-blue-600 bg-blue-50 dark:bg-blue-950/20';
    }
  };

  const getTrajectoryIcon = (trajectory: string) => {
    switch (trajectory) {
      case 'positive': return <TrendingUp className="w-4 h-4" />;
      case 'negative': return <TrendingDown className="w-4 h-4" />;
      case 'stagnant': return <BarChart3 className="w-4 h-4" />;
      case 'volatile': return <Zap className="w-4 h-4" />;
      default: return <BarChart3 className="w-4 h-4" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getTimeframeLabel = (timeframe: string) => {
    switch (timeframe) {
      case 'short': return 'Next 3-5 Chapters';
      case 'medium': return 'Next 10-15 Chapters';
      case 'long': return 'Rest of Story';
      default: return 'Medium Term';
    }
  };

  // Generate prediction chart data
  const generatePredictionData = () => {
    if (!prediction) return [];
    
    const data = [];
    const chapters = selectedTimeframe === 'short' ? 5 : selectedTimeframe === 'medium' ? 15 : 25;
    
    for (let i = 0; i <= chapters; i++) {
      const chapter = currentChapter + i;
      let value = 50; // baseline
      
      // Simulate prediction curve based on trajectory
      if (prediction.currentTrajectory === 'positive') {
        value = 50 + (i / chapters) * 40 + Math.sin(i * 0.3) * 5;
      } else if (prediction.currentTrajectory === 'negative') {
        value = 50 - (i / chapters) * 30 + Math.sin(i * 0.3) * 5;
      } else if (prediction.currentTrajectory === 'volatile') {
        value = 50 + Math.sin(i * 0.8) * 20 + Math.cos(i * 0.4) * 10;
      } else {
        value = 50 + Math.sin(i * 0.2) * 5;
      }
      
      data.push({
        chapter,
        predicted: Math.max(0, Math.min(100, value)),
        confidence: Math.max(20, 100 - i * 3) // Confidence decreases over time
      });
    }
    
    return data;
  };

  const predictionData = generatePredictionData();

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-red-600">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Prediction Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            onClick={analyzePredictions} 
            className="mt-3"
            variant="outline"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry Prediction
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Prediction Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Sparkles className="w-5 h-5 mr-2" />
                Arc Trajectory Predictions
              </CardTitle>
              <CardDescription>
                AI-powered forecasting of character development progression
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <select 
                value={selectedTimeframe}
                onChange={(e) => setSelectedTimeframe(e.target.value as 'short' | 'medium' | 'long')}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="short">Short Term</option>
                <option value="medium">Medium Term</option>
                <option value="long">Long Term</option>
              </select>
              <Button 
                onClick={analyzePredictions} 
                disabled={isAnalyzing}
                variant="outline"
                size="sm"
              >
                {isAnalyzing ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4 mr-2" />
                )}
                Update
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isAnalyzing ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Sparkles className="w-4 h-4 animate-pulse text-blue-600" />
                <span className="text-sm">Analyzing trajectory patterns...</span>
              </div>
              <Progress value={66} className="w-full" />
              <p className="text-xs text-gray-500">
                Generating predictions for {getTimeframeLabel(selectedTimeframe).toLowerCase()}
              </p>
            </div>
          ) : prediction ? (
            <div className="space-y-6">
              {/* Current Trajectory */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Current Trajectory</h4>
                  <div className={`flex items-center space-x-2 p-2 rounded-lg ${getTrajectoryColor(prediction.currentTrajectory)}`}>
                    {getTrajectoryIcon(prediction.currentTrajectory)}
                    <span className="text-sm font-medium capitalize">
                      {prediction.currentTrajectory}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Completion Likelihood</h4>
                  <div className="flex items-center space-x-2">
                    <Progress value={prediction.completionLikelihood} className="flex-1" />
                    <span className="text-sm font-medium">
                      {prediction.completionLikelihood}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">
                    Probability of successful arc completion
                  </p>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Prediction Range</h4>
                  <Badge variant="outline" className="text-sm">
                    {getTimeframeLabel(selectedTimeframe)}
                  </Badge>
                  <p className="text-xs text-gray-500">
                    From Chapter {currentChapter}
                  </p>
                </div>
              </div>

              {/* Prediction Chart */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Development Forecast</h4>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={predictionData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="chapter" 
                        label={{ value: 'Chapter', position: 'insideBottom', offset: -10 }}
                      />
                      <YAxis 
                        label={{ value: 'Development Level', angle: -90, position: 'insideLeft' }}
                        domain={[0, 100]}
                      />
                      <RechartsTooltip
                        content={(props: ChartTooltipProps) => {
                          const { active, payload, label } = props;
                          if (active && payload && payload.length && payload[0]) {
                            const data = payload[0].payload as any;
                            if (!data) return null;
                            return (
                              <div className="bg-white dark:bg-slate-800 p-3 border rounded shadow">
                                <p className="font-medium">Chapter {label}</p>
                                <p className="text-blue-600">Predicted Level: {Math.round(data.predicted || 0)}%</p>
                                <p className="text-green-600">Confidence: {Math.round(data.confidence || 0)}%</p>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="predicted"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.2}
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="confidence"
                        stroke="#10b981"
                        strokeWidth={1}
                        strokeDasharray="5 5"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Risk Factors */}
              {prediction.riskFactors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium flex items-center">
                    <AlertTriangle className="w-4 h-4 mr-1 text-orange-600" />
                    Risk Factors
                  </h4>
                  <div className="space-y-2">
                    {prediction.riskFactors.map((risk, index) => (
                      <Alert key={index} className="bg-orange-50 dark:bg-orange-950/20">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                          {risk}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Sparkles className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No predictions available yet</p>
              <Button onClick={analyzePredictions} className="mt-3" variant="outline">
                Generate Predictions
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Suggested Interventions */}
      {prediction && prediction.suggestedInterventions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Lightbulb className="w-5 h-5 mr-2 text-yellow-600" />
              Suggested Interventions
            </CardTitle>
            <CardDescription>
              Recommended actions to improve character arc development
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {prediction.suggestedInterventions.map((intervention, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        {intervention.dimension}
                      </Badge>
                      <Badge 
                        variant="outline" 
                        className={`${getImpactColor(intervention.impact)} text-white`}
                      >
                        {intervention.impact} impact
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">
                        Ch. {intervention.chapters.join(', ')}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm">{intervention.action}</p>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Target chapters: {intervention.chapters.length}</span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="w-3 h-3" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Implementation in these chapters will have {intervention.impact} impact on {intervention.dimension}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}