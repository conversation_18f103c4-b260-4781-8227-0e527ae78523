'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, Circle, Lightbulb, Target, TrendingUp, Award } from 'lucide-react'
import Link from 'next/link'

interface ProgressStep {
  id: string
  title: string
  description: string
  isCompleted: boolean
  isActive: boolean
  tips: string[]
  actionUrl?: string
  actionText?: string
}

interface ProgressTrackerProps {
  project?: Record<string, unknown>
  hasProjects: boolean
  isNewUser: boolean
}

export function ProgressTracker({ project, hasProjects, isNewUser }: ProgressTrackerProps) {
  const [currentTip, setCurrentTip] = useState(0)
  const [showTips, setShowTips] = useState(true)

  // Define the onboarding journey steps
  const getProgressSteps = (): ProgressStep[] => {
    const hasProject = !!project || hasProjects
    const hasStructure = project?.status !== 'planning'
    const hasChapters = project && hasStructure
    const hasContent = false // Would check if any chapters have content
    
    return [
      {
        id: 'welcome',
        title: 'Welcome to BookScribe AI',
        description: 'Get familiar with the platform',
        isCompleted: !isNewUser,
        isActive: isNewUser,
        tips: [
          'Take the welcome tour to understand key features',
          'Try a sample project to see the AI in action',
          'Explore the dashboard to familiarize yourself with the interface'
        ]
      },
      {
        id: 'first-project',
        title: 'Create Your First Project',
        description: 'Set up your story concept and preferences',
        isCompleted: hasProject,
        isActive: isNewUser && !hasProject,
        tips: [
          'Don\'t worry about perfection - you can always edit later',
          'Choose genres and styles that excite you',
          'The guided mode provides helpful explanations for each choice'
        ],
        actionUrl: hasProject ? undefined : '/projects/new?guided=true',
        actionText: 'Start Guided Project Creation'
      },
      {
        id: 'generate-structure',
        title: 'Generate Story Structure',
        description: 'Let AI create characters, plot, and chapter outlines',
        isCompleted: hasStructure,
        isActive: hasProject && !hasStructure,
        tips: [
          'AI generates a complete story bible with characters and world-building',
          'Review and edit the generated content to match your vision',
          'The structure provides a roadmap for your entire novel'
        ],
        actionUrl: hasProject && !hasStructure ? `/projects/${project.id}` : undefined,
        actionText: 'Generate Story Structure'
      },
      {
        id: 'write-chapters',
        title: 'Start Writing Chapters',
        description: 'Use AI assistance to write your first chapters',
        isCompleted: hasContent,
        isActive: hasStructure && !hasContent,
        tips: [
          'AI maintains character consistency across chapters',
          'Review and edit generated content to match your voice',
          'Use the chapter outlines as your guide'
        ],
        actionUrl: hasChapters ? `/projects/${project.id}` : undefined,
        actionText: 'Write First Chapter'
      },
      {
        id: 'complete-novel',
        title: 'Complete Your Novel',
        description: 'Finish writing and export your completed work',
        isCompleted: false, // Would check if all chapters are written
        isActive: hasContent,
        tips: [
          'Maintain regular writing sessions for best results',
          'Use AI to help with transitions and plot consistency',
          'Export options include PDF, DOCX, and EPUB formats'
        ]
      }
    ]
  }

  const steps = getProgressSteps()
  const completedSteps = steps.filter(step => step.isCompleted).length
  const totalSteps = steps.length
  const progressPercentage = (completedSteps / totalSteps) * 100

  const activeStep = steps.find(step => step.isActive)
  const activeTips = activeStep?.tips || []

  useEffect(() => {
    if (activeTips.length > 1) {
      const interval = setInterval(() => {
        setCurrentTip((prev) => (prev + 1) % activeTips.length)
      }, 5000) // Change tip every 5 seconds

      return () => clearInterval(interval)
    }
    return undefined
  }, [activeTips.length])

  if (!isNewUser && hasProjects && !project) {
    return null // Don't show for experienced users on dashboard
  }

  return (
    <div className="space-y-6">
      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                Your Writing Journey
              </CardTitle>
              <CardDescription>
                Track your progress from concept to completed novel
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">{completedSteps}/{totalSteps}</div>
              <div className="text-sm text-muted-foreground">Steps Complete</div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={progressPercentage} className="h-3" />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Getting Started</span>
              <span>{Math.round(progressPercentage)}% Complete</span>
              <span>Published Author</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Step Tips */}
      {activeStep && showTips && (
        <Alert>
          <Lightbulb className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <strong>Next Step: {activeStep.title}</strong>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setShowTips(false)}
                  className="h-6 w-6 p-0"
                >
                  ×
                </Button>
              </div>
              {activeTips.length > 0 && (
                <div className="space-y-1">
                  <p className="text-sm">{activeTips[currentTip]}</p>
                  {activeTips.length > 1 && (
                    <div className="flex gap-1">
                      {activeTips.map((_, index) => (
                        <div
                          key={index}
                          className={`h-1 w-4 rounded-full transition-colors ${
                            index === currentTip ? 'bg-primary' : 'bg-muted'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </div>
              )}
              {activeStep.actionUrl && (
                <Link href={activeStep.actionUrl}>
                  <Button size="sm" className="mt-2">
                    {activeStep.actionText}
                  </Button>
                </Link>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Detailed Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Progress Steps
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {steps.map((step) => (
              <div key={step.id} className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-1">
                  {step.isCompleted ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : step.isActive ? (
                    <div className="h-5 w-5 rounded-full border-2 border-primary bg-primary/20" />
                  ) : (
                    <Circle className="h-5 w-5 text-muted-foreground" />
                  )}
                </div>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center justify-between">
                    <h4 className={`font-medium ${
                      step.isCompleted ? 'text-green-700' : 
                      step.isActive ? 'text-primary' : 
                      'text-muted-foreground'
                    }`}>
                      {step.title}
                    </h4>
                    {step.isCompleted && (
                      <Badge variant="secondary" className="text-xs">
                        Complete
                      </Badge>
                    )}
                    {step.isActive && (
                      <Badge variant="default" className="text-xs">
                        Current
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {step.description}
                  </p>
                  {step.isActive && step.actionUrl && (
                    <Link href={step.actionUrl}>
                      <Button size="sm" variant="outline" className="mt-2">
                        {step.actionText}
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Achievement Badge */}
      {completedSteps > 0 && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <Award className="h-8 w-8 text-primary" />
              <div>
                <h4 className="font-medium">Great Progress!</h4>
                <p className="text-sm text-muted-foreground">
                  You&apos;ve completed {completedSteps} step{completedSteps !== 1 ? 's' : ''} of your writing journey.
                  {completedSteps >= 2 && " You&apos;re well on your way to becoming a published author!"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}