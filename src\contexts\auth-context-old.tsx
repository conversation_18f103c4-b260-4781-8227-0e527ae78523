'use client'

import { createContext, useContext, useEffect, useState, useMemo, useCallback, useRef } from 'react'
import type { ReactNode } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { User, AuthChangeEvent, Session } from '@supabase/supabase-js'
import { useRouter } from 'next/navigation'
import { config } from '@/lib/config'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  isInitialized: boolean
  lastActivity: Date | null
  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<{ error: unknown }>
  signUp: (email: string, password: string) => Promise<{ error: unknown }>
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
  updateActivity: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Session refresh interval (5 minutes before expiry)
const SESSION_REFRESH_INTERVAL = 5 * 60 * 1000
// Activity tracking interval
const ACTIVITY_CHECK_INTERVAL = 60 * 1000
// Inactivity timeout (30 minutes)
const INACTIVITY_TIMEOUT = 30 * 60 * 1000

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [isInitialized, setIsInitialized] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [lastActivity, setLastActivity] = useState<Date | null>(null)
  const router = useRouter()
  const refreshTimer = useRef<NodeJS.Timeout | undefined>(undefined)
  const activityTimer = useRef<NodeJS.Timeout | undefined>(undefined)
  const broadcastChannel = useRef<BroadcastChannel | null>(null)

  // Only create client after component mounts
  const supabase = useMemo(() => {
    if (typeof window === 'undefined') return null
    return createClient()
  }, [])

  // Development bypass check
  const isDevelopment = config.isDevelopment
  const bypassAuth = isDevelopment && config.dev.bypassAuth

  useEffect(() => {
    setMounted(true)
    
    // Initialize broadcast channel for cross-tab communication
    if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
      broadcastChannel.current = new BroadcastChannel('auth_channel')
      
      broadcastChannel.current.onmessage = (event) => {
        if (event.data.type === 'SIGN_OUT') {
          // Another tab signed out, sync this tab
          setUser(null)
          setSession(null)
          router.push('/login')
        } else if (event.data.type === 'SIGN_IN' && event.data.session) {
          // Another tab signed in, sync this tab
          setUser(event.data.session.user)
          setSession(event.data.session)
        }
      }
    }
    
    return () => {
      broadcastChannel.current?.close()
    }
  }, [router])

  // Update last activity timestamp
  const updateActivity = useCallback(() => {
    setLastActivity(new Date())
  }, [])

  // Define signOut early to avoid circular deps
  const signOutBase = useCallback(async () => {
    // Development bypass - just clear the user and session
    if (bypassAuth) {
      setUser(null)
      setSession(null)
      router.push('/login')
      return
    }

    if (supabase) {
      setLoading(true)
      try {
        const { error } = await supabase.auth.signOut()
        if (error) {
          console.error('Sign out error:', error)
        }
        router.push('/login')
      } finally {
        setLoading(false)
      }
    }
  }, [bypassAuth, supabase, router])

  // Session refresh logic
  const refreshSession = useCallback(async () => {
    if (!supabase || bypassAuth) return
    
    try {
      const { data: { session: newSession }, error } = await supabase.auth.refreshSession()
      if (error) {
        console.error('Session refresh failed:', error)
        // If refresh fails, sign out user
        await signOutBase()
      } else if (newSession) {
        setSession(newSession)
        setUser(newSession.user)
        // Schedule next refresh
        if (newSession.expires_at && refreshTimer.current !== undefined) {
          clearTimeout(refreshTimer.current)
          const expiresAt = new Date(newSession.expires_at * 1000)
          const refreshAt = new Date(expiresAt.getTime() - SESSION_REFRESH_INTERVAL)
          const timeout = Math.max(0, refreshAt.getTime() - Date.now())
          
          refreshTimer.current = setTimeout(() => {
            refreshSession()
          }, timeout)
        }
      }
    } catch (error) {
      console.error('Session refresh error:', error)
    }
  }, [supabase, bypassAuth, signOutBase])

  // Schedule session refresh before expiry
  const scheduleSessionRefresh = useCallback((session: Session) => {
    if (refreshTimer.current) {
      clearTimeout(refreshTimer.current)
    }
    
    if (session.expires_at) {
      const expiresAt = new Date(session.expires_at * 1000)
      const refreshAt = new Date(expiresAt.getTime() - SESSION_REFRESH_INTERVAL)
      const timeout = Math.max(0, refreshAt.getTime() - Date.now())
      
      refreshTimer.current = setTimeout(() => {
        refreshSession()
      }, timeout)
    }
  }, [refreshSession])

  // Check for inactivity
  const checkInactivity = useCallback(() => {
    if (!lastActivity || bypassAuth) return
    
    const now = new Date()
    const timeSinceActivity = now.getTime() - lastActivity.getTime()
    
    if (timeSinceActivity > INACTIVITY_TIMEOUT) {
      // User has been inactive, sign them out
      signOutBase()
    }
  }, [lastActivity, bypassAuth, signOutBase])

  // Track user activity
  useEffect(() => {
    if (!mounted) return
    
    const handleActivity = () => updateActivity()
    
    // Track various user activities
    window.addEventListener('mousedown', handleActivity)
    window.addEventListener('keydown', handleActivity)
    window.addEventListener('touchstart', handleActivity)
    window.addEventListener('scroll', handleActivity)
    
    // Start activity check timer
    activityTimer.current = setInterval(checkInactivity, ACTIVITY_CHECK_INTERVAL)
    
    return () => {
      window.removeEventListener('mousedown', handleActivity)
      window.removeEventListener('keydown', handleActivity)
      window.removeEventListener('touchstart', handleActivity)
      window.removeEventListener('scroll', handleActivity)
      
      if (activityTimer.current) {
        clearInterval(activityTimer.current)
      }
    }
  }, [mounted, updateActivity, checkInactivity])

  useEffect(() => {
    if (!mounted || !supabase) return

    // Development bypass - create mock user and session
    if (bypassAuth) {
      const mockUser: User = {
        id: config.dev.userId || 'dev-user-123',
        email: config.dev.userEmail || '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        aud: 'authenticated',
        role: 'authenticated',
        app_metadata: {},
        user_metadata: {
          name: 'Development User',
          avatar_url: null
        },
        identities: [],
        factors: []
      }
      const mockSession: Session = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        expires_at: Math.floor(Date.now() / 1000) + 3600,
        token_type: 'bearer',
        user: mockUser
      }
      setUser(mockUser)
      setSession(mockSession)
      setLoading(false)
      setIsInitialized(true)
      updateActivity()
      return
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        setLoading(true)
        const { data: { session: initialSession }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting initial session:', error)
        }
        
        if (initialSession) {
          setUser(initialSession.user)
          setSession(initialSession)
          scheduleSessionRefresh(initialSession)
          updateActivity()
        } else {
          setUser(null)
          setSession(null)
        }
      } catch (error) {
        console.error('Failed to get initial session:', error)
      } finally {
        setLoading(false)
        setIsInitialized(true)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event: AuthChangeEvent, newSession: Session | null) => {
        console.log('Auth state changed:', event)
        
        switch (event) {
          case 'SIGNED_IN':
            if (newSession) {
              setUser(newSession.user)
              setSession(newSession)
              scheduleSessionRefresh(newSession)
              updateActivity()
              
              // Broadcast to other tabs
              broadcastChannel.current?.postMessage({
                type: 'SIGN_IN',
                session: newSession
              })
            }
            break
            
          case 'SIGNED_OUT':
            setUser(null)
            setSession(null)
            if (refreshTimer.current) {
              clearTimeout(refreshTimer.current)
            }
            
            // Broadcast to other tabs
            broadcastChannel.current?.postMessage({ type: 'SIGN_OUT' })
            break
            
          case 'TOKEN_REFRESHED':
            if (newSession) {
              setSession(newSession)
              scheduleSessionRefresh(newSession)
              updateActivity()
            }
            break
            
          case 'USER_UPDATED':
            if (newSession) {
              setUser(newSession.user)
              updateActivity()
            }
            break
        }
        
        setLoading(false)
      }
    )

    return () => {
      subscription.unsubscribe()
      if (refreshTimer.current) {
        clearTimeout(refreshTimer.current)
      }
    }
  }, [mounted, supabase, bypassAuth, scheduleSessionRefresh, updateActivity])

  const signIn = useCallback(async (email: string, password: string, rememberMe = false) => {
    if (!supabase) return { error: new Error('Supabase client not initialized') }

    // Development bypass - always succeed
    if (bypassAuth) {
      updateActivity()
      return { error: null }
    }

    setLoading(true)
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (!error && data.session) {
        updateActivity()
        // Handle remember me preference (stored in localStorage)
        if (typeof window !== 'undefined') {
          if (rememberMe) {
            localStorage.setItem('rememberMe', 'true')
          } else {
            localStorage.removeItem('rememberMe')
          }
        }
      }
      
      return { error }
    } finally {
      setLoading(false)
    }
  }, [bypassAuth, supabase, updateActivity])

  const signUp = useCallback(async (email: string, password: string) => {
    if (!supabase) return { error: new Error('Supabase client not initialized') }

    // Development bypass - always succeed
    if (bypassAuth) {
      updateActivity()
      return { error: null }
    }

    setLoading(true)
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: typeof window !== 'undefined' ? `${window.location.origin}/auth/callback` : undefined,
        },
      })
      
      if (!error && data.session) {
        updateActivity()
      }
      
      return { error }
    } finally {
      setLoading(false)
    }
  }, [bypassAuth, supabase, updateActivity])

  const signOut = signOutBase

  const value = useMemo(() => ({
    user,
    session,
    loading,
    isInitialized,
    lastActivity,
    signIn,
    signUp,
    signOut,
    refreshSession,
    updateActivity,
  }), [user, session, loading, isInitialized, lastActivity, signIn, signUp, signOut, refreshSession, updateActivity])

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}