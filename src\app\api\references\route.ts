import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import { authenticateUser, validateProjectOwnership, handleRouteError } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const { user } = authResult;
    
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const type = searchParams.get('type');
    const tags = searchParams.get('tags');

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }
    
    // Validate project ownership
    const ownershipResult = await validateProjectOwnership(
      await import('@/lib/supabase/server').then(m => m.createClient()), 
      user.id, 
      projectId
    );
    if (!ownershipResult.success) {
      return ownershipResult.response!;
    }

    let query = supabase
      .from('reference_materials')
      .select('*')
      .eq('project_id', projectId)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (type && type !== 'all') {
      query = query.eq('type', type);
    }

    if (tags) {
      const tagList = tags.split(',').map(t => t.trim());
      query = query.contains('tags', tagList);
    }

    const { data: materials, error } = await query;

    if (error) throw error;

    // Transform data to match frontend interface
    const formattedMaterials = materials?.map(material => ({
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    })) || [];

    return NextResponse.json({ materials: formattedMaterials });
  } catch (error) {
    return handleRouteError(error, 'References GET');
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const { user } = authResult;
    
    const body = await request.json();
    const { 
      projectId, 
      type, 
      title, 
      description, 
      content, 
      tags 
    } = body;

    if (!projectId || !title || !type) {
      return NextResponse.json({ error: 'Required fields missing' }, { status: 400 });
    }
    
    // Validate project ownership
    const ownershipResult = await validateProjectOwnership(
      await import('@/lib/supabase/server').then(m => m.createClient()), 
      user.id, 
      projectId
    );
    if (!ownershipResult.success) {
      return ownershipResult.response!;
    }

    // Validate type
    const validTypes = ['document', 'image', 'url', 'note', 'research'];
    if (!validTypes.includes(type)) {
      return NextResponse.json({ error: 'Invalid material type' }, { status: 400 });
    }

    const materialData = {
      project_id: projectId,
      user_id: user.id,
      type,
      title,
      description: description || null,
      content: content || null,
      tags: tags || [],
    };

    const { data: material, error } = await supabase
      .from('reference_materials')
      .insert(materialData)
      .select()
      .single();

    if (error) throw error;

    // Transform response to match frontend interface
    const formattedMaterial = {
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    };

    return NextResponse.json({ material: formattedMaterial });
  } catch (error) {
    return handleRouteError(error, 'References POST');
  }
}