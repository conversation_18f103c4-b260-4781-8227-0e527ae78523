'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BookOpen, 
  Users, 
  Target, 
  Clock, 
  CheckCircle, 
  Sparkles,
  Filter
} from 'lucide-react'
import { projectTemplates, type ProjectTemplate, getAllGenres } from '@/lib/project-templates'
import { useWizardStore } from '@/stores/wizard-store'

interface StepTemplateSelectionProps {
  onTemplateSelect?: (template: ProjectTemplate | null) => void
}

const difficultyColors = {
  beginner: 'bg-green-500',
  intermediate: 'bg-yellow-500', 
  advanced: 'bg-red-500'
}

const difficultyLabels = {
  beginner: 'Beginner Friendly',
  intermediate: 'Intermediate',
  advanced: 'Advanced'
}

export function StepTemplateSelection({ onTemplateSelect }: StepTemplateSelectionProps) {
  const [selectedGenre, setSelectedGenre] = useState<string>('all')
  const [selectedTemplate, setSelectedTemplate] = useState<ProjectTemplate | null>(null)
  const { updateSelections } = useWizardStore()

  const filteredTemplates = projectTemplates.filter(template => {
    return selectedGenre === 'all' || template.genre === selectedGenre
  })

  const handleTemplateSelect = (template: ProjectTemplate | null) => {
    setSelectedTemplate(template)
    
    if (template) {
      // Apply template selections to wizard store
      updateSelections(template.selections)
    }
    
    onTemplateSelect?.(template)
  }

  const handleSkipTemplate = () => {
    setSelectedTemplate(null)
    onTemplateSelect?.(null)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Choose a Starting Template</h2>
        <p className="text-muted-foreground">
          Get a head start with a proven story structure, or skip to create from scratch.
        </p>
      </div>

      {/* Skip Option */}
      <Card className={`cursor-pointer transition-all border-2 ${
        selectedTemplate === null ? 'border-primary bg-primary/5' : 'border-border hover:shadow-md'
      }`} onClick={handleSkipTemplate}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-lg bg-muted flex items-center justify-center">
                <Sparkles className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-semibold">Start from Scratch</h3>
                <p className="text-sm text-muted-foreground">
                  Create your own unique story structure and settings
                </p>
              </div>
            </div>
            {selectedTemplate === null && (
              <CheckCircle className="h-5 w-5 text-primary" />
            )}
          </div>
        </CardContent>
      </Card>

      {/* Genre Filter */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select value={selectedGenre} onValueChange={setSelectedGenre}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Genres" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Genres</SelectItem>
              {getAllGenres().map(genre => (
                <SelectItem key={genre} value={genre}>{genre}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Badge variant="secondary" className="gap-1">
          <BookOpen className="h-3 w-3" />
          {filteredTemplates.length} Templates
        </Badge>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-4 md:grid-cols-2">
        {filteredTemplates.map((template) => (
          <Card 
            key={template.id} 
            className={`cursor-pointer transition-all border-2 ${
              selectedTemplate?.id === template.id 
                ? 'border-primary bg-primary/5' 
                : 'border-border hover:shadow-md'
            }`}
            onClick={() => handleTemplateSelect(template)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{template.genre}</Badge>
                    <div className={`h-2 w-2 rounded-full ${difficultyColors[template.difficulty]}`} />
                    <span className="text-xs text-muted-foreground">
                      {difficultyLabels[template.difficulty]}
                    </span>
                  </div>
                </div>
                {selectedTemplate?.id === template.id && (
                  <CheckCircle className="h-5 w-5 text-primary" />
                )}
              </div>
              <CardDescription className="text-sm">
                {template.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="pt-0 space-y-3">
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3 text-muted-foreground" />
                  <span>{template.estimatedWordCount.toLocaleString()} words</span>
                </div>
                <div className="flex items-center gap-1">
                  <BookOpen className="h-3 w-3 text-muted-foreground" />
                  <span>{template.estimatedChapters} chapters</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3 text-muted-foreground" />
                  <span>{template.characterArchetypes.length} archetypes</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span>{template.plotStructure.length} acts</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex flex-wrap gap-1">
                  {template.tags.slice(0, 4).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {template.tags.length > 4 && (
                    <Badge variant="secondary" className="text-xs">
                      +{template.tags.length - 4}
                    </Badge>
                  )}
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                <strong>Includes:</strong> {template.characterArchetypes.length} character archetypes, 
                {template.plotStructure.length}-act structure, {template.storyPrompts.length} story prompts
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Selected Template Summary */}
      {selectedTemplate && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-primary mt-0.5" />
              <div className="flex-1">
                <h4 className="font-semibold text-primary">Template Selected: {selectedTemplate.name}</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  Your project will be pre-configured with {selectedTemplate.genre} settings, 
                  character archetypes, and a {selectedTemplate.plotStructure.length}-act story structure.
                  You can customize everything in the next steps.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {filteredTemplates.length === 0 && (
        <div className="text-center py-8">
          <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="font-semibold mb-2">No templates found</h3>
          <p className="text-muted-foreground">
            Try selecting a different genre or start from scratch.
          </p>
        </div>
      )}
    </div>
  )
}