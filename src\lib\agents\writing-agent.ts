import OpenAI from 'openai';
import { BaseAgent } from './base-agent';
import { ChapterContent, ChapterOutline } from './types';
// import { ContextManager } from '../memory/context-manager'; // Module doesn't exist

type ChatCompletionMessageParam = OpenAI.Chat.Completions.ChatCompletionMessageParam;

export class WritingAgent extends BaseAgent {
  // private contextManager?: ContextManager; // Commented out - module doesn't exist

  constructor(context: import('./types').BookContext) {
    super(context);
    // this.contextManager = contextManager; // Commented out - module doesn't exist
  }

  async execute(): Promise<Record<string, unknown>> {
    // This method should be called with specific parameters, but the base class requires no-param execute
    throw new Error('Use writeChapter method instead');
  }

  async writeChapter(chapterOutline: ChapterOutline): Promise<ChapterContent> {
    if (!this.context.storyStructure || !this.context.characters || !this.context.chapterOutlines) {
      throw new Error('Writing Agent requires complete story context');
    }

    const systemPrompt = this.createSystemPrompt(
      'Writing Agent',
      `Your role is to write compelling chapter content based on the provided outline and story context.

RESPONSIBILITIES:
1. Generate full chapter content that follows the outline precisely
2. Maintain consistent character voices and personalities
3. Write in the established genre, style, and tone
4. Advance plot and character development as planned
5. Ensure scene transitions and pacing align with preferences

WRITING REQUIREMENTS:
- Target word count: ${chapterOutline.wordCountTarget} words
- POV Character: ${chapterOutline.povCharacter}
- Narrative Voice: ${this.context.settings?.narrativeVoice || 'Third Person'}
- Tense: ${this.context.settings?.tense || 'Past'}
- Writing Style: ${this.context.settings?.writingStyle || 'Standard'}
- Tone: ${this.context.settings?.tone?.join(', ') || 'Not specified'}

CONTENT GUIDELINES:
- Maintain consistency with established story bible
- Show character growth according to planned arcs
- Include sensory details and immersive description
- Write compelling dialogue that reveals character
- Build tension and maintain reader engagement
- End with the planned cliffhanger or chapter conclusion`
    );

    // Get intelligent context from memory manager if available
    const memoryContext = '';
    // ContextManager usage commented out - module doesn't exist
    /*
    if (this.contextManager) {
      const writingContext = await this.contextManager.getWritingContext(
        chapterOutline.number,
        `Chapter ${chapterOutline.number}: ${chapterOutline.title}. ${chapterOutline.summary}`
      );
      
      memoryContext = `
RELEVANT MEMORY CONTEXT:
${writingContext.contextSummary}

CHARACTER STATES:
${JSON.stringify(writingContext.characterStates, null, 2)}

RECENT EVENTS:
${writingContext.recentEvents.join('\n')}

ACTIVE PLOT THREADS:
${writingContext.plotThreads.map(thread => `- ${thread.description}`).join('\n')}
`;
    }
    */

    const contextData = this.buildChapterContext(chapterOutline);

    const messages: ChatCompletionMessageParam[] = [
      { role: 'system', content: systemPrompt },
      {
        role: 'user',
        content: `Write Chapter ${chapterOutline.number}: "${chapterOutline.title}"

CHAPTER OUTLINE:
${JSON.stringify(chapterOutline, null, 2)}

STORY CONTEXT:
${contextData}

${memoryContext}

Write the complete chapter content following these requirements:
1. Start with an engaging opening that draws readers in
2. Follow the scene structure outlined in the chapter plan
3. Maintain the established writing style and character voices
4. Include dialogue, action, and internal thoughts appropriate to the POV
5. Build toward the planned conflicts and resolutions
6. End with the specified cliffhanger or transition

Target approximately ${chapterOutline.wordCountTarget} words.`
      }
    ];

    const tools = [
      {
        type: 'function' as const,
        function: {
          name: 'write_chapter',
          description: 'Write complete chapter content',
          parameters: {
            type: 'object',
            properties: {
              chapterNumber: { type: 'number' },
              title: { type: 'string' },
              content: { type: 'string', description: 'Full chapter text content' },
              wordCount: { type: 'number' },
              scenes: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    content: { type: 'string' },
                    wordCount: { type: 'number' },
                    purpose: { type: 'string' }
                  }
                }
              },
              characterVoices: { type: 'array', items: { type: 'string' } },
              themes: { type: 'array', items: { type: 'string' } },
              continuityNotes: { type: 'array', items: { type: 'string' } }
            },
            required: ['chapterNumber', 'title', 'content', 'wordCount', 'scenes', 'characterVoices', 'themes']
          }
        }
      }
    ];

    const completion = await this.createCompletion(messages, tools, 'gpt-4');
    
    if (completion.choices[0]?.message.tool_calls) {
      const toolCall = completion.choices[0].message.tool_calls[0];
      if (toolCall) {
        const result = JSON.parse(toolCall.function.arguments);
        
        // Update memory with the new chapter content
        // ContextManager usage commented out - module doesn't exist
        /*
        if (this.contextManager) {
          await this.contextManager.addChapterContent(result);
          
          // Check for inconsistencies
          const inconsistencies = await this.contextManager.findInconsistencies(
            result.content,
            result.chapterNumber
          );
          
          if (inconsistencies.length > 0) {
            result.continuityWarnings = inconsistencies;
            console.warn(`Chapter ${result.chapterNumber} inconsistencies:`, inconsistencies);
          }
        }
        */
        
        return result;
      }
    }
    
    throw new Error('Writing Agent failed to generate chapter content');
  }

  private buildChapterContext(chapterOutline: ChapterOutline): string {
    const characters = this.context.characters!;
    const relevantCharacters = [
      ...characters.protagonists,
      ...characters.antagonists,
      ...characters.supporting
    ].filter(char => 
      chapterOutline.scenes.some(scene => 
        scene.characters.includes(char.name) || scene.characters.includes(char.id)
      )
    );

    const previousChapters = this.context.chapterOutlines!.chapters
      .filter(ch => ch.number < chapterOutline.number)
      .slice(-2); // Last 2 chapters for context

    return `
RELEVANT CHARACTERS:
${relevantCharacters.map(char => `
${char.name} (${char.role}):
- Personality: ${char.personality.traits.join(', ')}
- Current Arc Point: ${char.arc.startingPoint}
- Voice: ${char.voice.speakingStyle}
- Motivation: ${char.motivation}
`).join('\n')}

PREVIOUS CHAPTERS CONTEXT:
${previousChapters.map(ch => `
Chapter ${ch.number}: ${ch.title}
- Summary: ${ch.summary}
- Key resolutions: ${ch.resolutions.join('; ')}
`).join('\n')}

CHARACTER STATES AT CHAPTER START:
${chapterOutline.characterStates.map(state => `
${state.characterId}: ${state.emotionalState} | ${state.physicalState}
Knowledge: ${state.knowledge.join(', ')}
`).join('\n')}

WORLD SETTING:
- Time Period: ${this.context.settings?.timePeriod || 'Contemporary'}
- World Type: ${this.context.settings?.worldType || 'Real World'}
- Magic/Tech Level: ${this.context.settings?.magicTechLevel || 'None'}
    `.trim();
  }
}