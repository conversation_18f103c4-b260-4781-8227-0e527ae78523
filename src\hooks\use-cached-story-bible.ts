'use client'

import { useEffect } from 'react'
import { useCachedData, characterCache, cacheKeys } from '@/lib/cache/client'
import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/db/types'

type Character = Database['public']['Tables']['characters']['Row']
type WorldBuilding = Database['public']['Tables']['world_building']['Row']
type StoryArc = Database['public']['Tables']['story_arcs']['Row']

interface StoryBibleData {
  characters: Character[]
  worldBuilding: WorldBuilding[]
  storyArcs: StoryArc[]
}

interface UseCachedStoryBibleOptions {
  projectId: string
  enabled?: boolean
}

export function useCachedStoryBible({ projectId, enabled = true }: UseCachedStoryBibleOptions) {
  const supabase = createClient()
  
  const fetcher = async () => {
    const [charactersResult, worldBuildingResult, storyArcsResult] = await Promise.all([
      supabase.from('characters').select('*').eq('project_id', projectId).order('created_at'),
      supabase.from('world_building').select('*').eq('project_id', projectId).order('created_at'),
      supabase.from('story_arcs').select('*').eq('project_id', projectId).order('act_number')
    ])
    
    if (charactersResult.error) throw charactersResult.error
    if (worldBuildingResult.error) throw worldBuildingResult.error
    if (storyArcsResult.error) throw storyArcsResult.error
    
    return {
      characters: charactersResult.data || [],
      worldBuilding: worldBuildingResult.data || [],
      storyArcs: storyArcsResult.data || []
    } as StoryBibleData
  }
  
  const result = useCachedData<StoryBibleData>({
    cacheKey: cacheKeys.storyBible(projectId),
    cache: characterCache, // Using character cache for story bible
    fetcher,
    dependencies: [projectId],
    staleTime: 60000, // Consider data stale after 1 minute
  })

  // Subscribe to real-time updates
  useEffect(() => {
    if (!enabled) return

    const subscription = supabase
      .channel(`story-bible-${projectId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'characters',
          filter: `project_id=eq.${projectId}`,
        },
        () => result.refresh()
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'world_building',
          filter: `project_id=eq.${projectId}`,
        },
        () => result.refresh()
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'story_arcs',
          filter: `project_id=eq.${projectId}`,
        },
        () => result.refresh()
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [projectId, enabled, result, supabase])

  return result
}

export function useCachedCharacters({ projectId, enabled = true }: UseCachedStoryBibleOptions) {
  const supabase = createClient()
  
  const fetcher = async () => {
    const { data, error } = await supabase
      .from('characters')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at')
    
    if (error) throw error
    return data as Character[]
  }
  
  const result = useCachedData<Character[]>({
    cacheKey: cacheKeys.characterList(projectId),
    cache: characterCache,
    fetcher,
    dependencies: [projectId],
    staleTime: 60000,
  })

  // Subscribe to real-time updates
  useEffect(() => {
    if (!enabled) return

    const subscription = supabase
      .channel(`characters-${projectId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'characters',
          filter: `project_id=eq.${projectId}`,
        },
        () => result.refresh()
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [projectId, enabled, result, supabase])

  return result
}