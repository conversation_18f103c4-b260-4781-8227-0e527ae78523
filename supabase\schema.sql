-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Projects table
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Genre & Style Selections
  primary_genre VARCHAR(100),
  subgenre VARCHAR(100),
  custom_genre TEXT,
  narrative_voice VARCHAR(50),
  tense VARCHAR(20),
  tone_options TEXT[],
  writing_style VARCHAR(50),
  custom_style_description TEXT,
  
  -- Story Structure & Pacing
  structure_type VARCHAR(50),
  pacing_preference VARCHAR(50),
  chapter_structure VARCHAR(50),
  timeline_complexity VARCHAR(50),
  custom_structure_notes TEXT,
  
  -- Character & World Building
  protagonist_types TEXT[],
  antagonist_types TEXT[],
  character_complexity VARCHAR(50),
  character_arc_types TEXT[],
  custom_character_concepts TEXT,
  time_period VARCHAR(100),
  geographic_setting VARCHAR(100),
  world_type VARCHAR(100),
  magic_tech_level VARCHAR(50),
  custom_setting_description TEXT,
  
  -- Themes & Content
  major_themes TEXT[],
  philosophical_themes TEXT[],
  social_themes TEXT[],
  custom_themes TEXT,
  target_audience VARCHAR(50),
  content_rating VARCHAR(20),
  content_warnings TEXT[],
  cultural_sensitivity_notes TEXT,
  
  -- Series & Scope
  project_scope VARCHAR(50),
  series_type VARCHAR(50),
  interconnection_level VARCHAR(50),
  custom_scope_description TEXT,
  
  -- Technical Specifications
  target_word_count INTEGER,
  current_word_count INTEGER DEFAULT 0,
  target_chapters INTEGER,
  chapter_count_type VARCHAR(20),
  pov_character_count INTEGER,
  pov_character_type VARCHAR(50),
  
  -- Research & References
  research_needs TEXT[],
  fact_checking_level VARCHAR(50),
  custom_research_notes TEXT,
  
  -- System Fields
  status TEXT DEFAULT 'planning',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Story Elements
CREATE TABLE story_arcs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  act_number INTEGER,
  description TEXT,
  key_events JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Characters
CREATE TABLE characters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  role TEXT,
  description TEXT,
  backstory TEXT,
  personality_traits JSONB,
  character_arc JSONB,
  relationships JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chapters
CREATE TABLE chapters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_number INTEGER NOT NULL,
  title TEXT,
  target_word_count INTEGER,
  actual_word_count INTEGER DEFAULT 0,
  outline TEXT,
  content TEXT,
  status TEXT DEFAULT 'planned',
  ai_notes JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Agent Logs
CREATE TABLE agent_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  agent_type TEXT NOT NULL,
  input_data JSONB,
  output_data JSONB,
  execution_time INTEGER,
  status TEXT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Selection Profiles (Templates)
CREATE TABLE selection_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  
  -- All selection fields (same as projects)
  primary_genre VARCHAR(100),
  subgenre VARCHAR(100),
  custom_genre TEXT,
  narrative_voice VARCHAR(50),
  tense VARCHAR(20),
  tone_options TEXT[],
  writing_style VARCHAR(50),
  custom_style_description TEXT,
  structure_type VARCHAR(50),
  pacing_preference VARCHAR(50),
  chapter_structure VARCHAR(50),
  timeline_complexity VARCHAR(50),
  custom_structure_notes TEXT,
  protagonist_types TEXT[],
  antagonist_types TEXT[],
  character_complexity VARCHAR(50),
  character_arc_types TEXT[],
  custom_character_concepts TEXT,
  time_period VARCHAR(100),
  geographic_setting VARCHAR(100),
  world_type VARCHAR(100),
  magic_tech_level VARCHAR(50),
  custom_setting_description TEXT,
  major_themes TEXT[],
  philosophical_themes TEXT[],
  social_themes TEXT[],
  custom_themes TEXT,
  target_audience VARCHAR(50),
  content_rating VARCHAR(20),
  content_warnings TEXT[],
  cultural_sensitivity_notes TEXT,
  project_scope VARCHAR(50),
  series_type VARCHAR(50),
  interconnection_level VARCHAR(50),
  custom_scope_description TEXT,
  target_word_count INTEGER,
  chapter_count_type VARCHAR(20),
  pov_character_count INTEGER,
  pov_character_type VARCHAR(50),
  research_needs TEXT[],
  fact_checking_level VARCHAR(50),
  custom_research_notes TEXT,
  
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reference Materials
CREATE TABLE reference_materials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  file_type VARCHAR(50),
  file_url TEXT,
  file_size INTEGER,
  mime_type VARCHAR(100),
  tags TEXT[],
  is_processed BOOLEAN DEFAULT false,
  processing_status VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Selection Analytics
CREATE TABLE selection_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  selection_profile_id UUID REFERENCES selection_profiles(id) ON DELETE CASCADE,
  event_type VARCHAR(50),
  selection_data JSONB,
  outcome_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Story Bible (for context management)
CREATE TABLE story_bible (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  entry_type VARCHAR(50) NOT NULL, -- 'character_state', 'plot_point', 'world_rule', 'timeline_event'
  entry_key VARCHAR(255) NOT NULL,
  entry_data JSONB NOT NULL,
  chapter_introduced INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Editing Sessions (for IDE-like features)
CREATE TABLE editing_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  selection_start INTEGER,
  selection_end INTEGER,
  selected_text TEXT,
  ai_prompt TEXT,
  ai_response TEXT,
  action_type VARCHAR(50), -- 'edit', 'expand', 'rewrite', 'suggest'
  was_applied BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_chapters_project_id ON chapters(project_id);
CREATE INDEX idx_chapters_status ON chapters(status);
CREATE INDEX idx_characters_project_id ON characters(project_id);
CREATE INDEX idx_story_bible_project_id ON story_bible(project_id);
CREATE INDEX idx_story_bible_entry_type ON story_bible(entry_type);
CREATE INDEX idx_editing_sessions_chapter_id ON editing_sessions(chapter_id);

-- Enable Row Level Security
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_arcs ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE selection_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE reference_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE selection_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_bible ENABLE ROW LEVEL SECURITY;
ALTER TABLE editing_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Projects: Users can only see their own projects
CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own projects" ON projects
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own projects" ON projects
  FOR DELETE USING (auth.uid() = user_id);

-- Apply similar policies to all tables (simplified for brevity)
-- In production, you'd want more granular policies

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- User Profiles
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  stripe_customer_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Subscriptions
CREATE TABLE user_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tier_id TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'past_due', 'incomplete')),
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT,
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage Tracking
CREATE TABLE usage_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  period_start TEXT NOT NULL, -- YYYY-MM format
  ai_generations INTEGER DEFAULT 0,
  projects INTEGER DEFAULT 0,
  exports INTEGER DEFAULT 0,
  storage_used NUMERIC DEFAULT 0, -- in GB
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, period_start)
);

-- Usage Events Log
CREATE TABLE usage_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  amount INTEGER DEFAULT 1,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_profiles_stripe_customer ON profiles(stripe_customer_id);
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_usage_tracking_user_period ON usage_tracking(user_id, period_start);
CREATE INDEX idx_usage_events_user_id ON usage_events(user_id);
CREATE INDEX idx_usage_events_type ON usage_events(event_type);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;

-- RLS Policies for new tables
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own subscription" ON user_subscriptions
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own usage" ON usage_tracking
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own usage events" ON usage_events
  FOR SELECT USING (auth.uid() = user_id);

-- Create triggers for updated_at
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_characters_updated_at BEFORE UPDATE ON characters
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chapters_updated_at BEFORE UPDATE ON chapters
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_selection_profiles_updated_at BEFORE UPDATE ON selection_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reference_materials_updated_at BEFORE UPDATE ON reference_materials
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_story_bible_updated_at BEFORE UPDATE ON story_bible
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE ON user_subscriptions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_usage_tracking_updated_at BEFORE UPDATE ON usage_tracking
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();