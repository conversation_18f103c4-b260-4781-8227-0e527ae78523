'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { 
  Edit3, 
  Wand2, 
  RotateCcw, 
  Lightbulb, 
  MessageSquare,
  X 
} from 'lucide-react'

interface SelectionMenuProps {
  selectedText: string
  position: { x: number; y: number }
  onClose: () => void
  onApplyEdit: (newText: string) => void
}

export function SelectionMenu({ 
  selectedText, 
  position, 
  onClose, 
  onApplyEdit 
}: SelectionMenuProps) {
  const [mode, setMode] = useState<'menu' | 'custom' | 'loading'>('menu')
  const [customPrompt, setCustomPrompt] = useState('')
  const [result, setResult] = useState('')

  const handleQuickAction = async (action: string) => {
    setMode('loading')
    
    try {
      const response = await fetch('/api/agents/edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          selectedText,
          prompt: action === 'custom' ? customPrompt : undefined
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setResult(data.editedText)
        setMode('menu') // Show result in menu
      } else {
        alert('Error: ' + data.error)
        setMode('menu')
      }
    } catch {
      alert('Failed to process text')
      setMode('menu')
    }
  }

  const quickActions = [
    {
      icon: Edit3,
      label: 'Improve',
      action: 'improve',
      description: 'Enhance clarity and flow'
    },
    {
      icon: Wand2,
      label: 'Expand',
      action: 'expand',
      description: 'Add more detail'
    },
    {
      icon: RotateCcw,
      label: 'Rewrite',
      action: 'rewrite',
      description: 'Rephrase completely'
    },
    {
      icon: Lightbulb,
      label: 'Suggest',
      action: 'suggest',
      description: 'Get alternatives'
    }
  ]

  if (mode === 'loading') {
    return (
      <Card 
        className="absolute z-50 w-64 shadow-lg"
        style={{ 
          left: Math.min(position.x, window.innerWidth - 280),
          top: position.y + 20
        }}
      >
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span className="text-sm">Processing...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (mode === 'custom') {
    return (
      <Card 
        className="absolute z-50 w-80 shadow-lg"
        style={{ 
          left: Math.min(position.x, window.innerWidth - 340),
          top: position.y + 20
        }}
      >
        <CardContent className="p-4 space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Custom Edit</h4>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="space-y-2">
            <p className="text-xs text-muted-foreground">Selected text:</p>
            <div className="bg-muted p-2 rounded text-xs max-h-20 overflow-y-auto">
              {selectedText}
            </div>
          </div>
          
          <Textarea
            placeholder="Describe how you want to edit this text..."
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            rows={3}
          />
          
          <div className="flex space-x-2">
            <Button 
              onClick={() => handleQuickAction('custom')}
              disabled={!customPrompt.trim()}
              size="sm"
            >
              Apply Edit
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setMode('menu')}
              size="sm"
            >
              Back
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card 
      className="absolute z-50 w-72 shadow-lg"
      style={{ 
        left: Math.min(position.x, window.innerWidth - 300),
        top: position.y + 20
      }}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-sm">AI Edit</h4>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {result ? (
          <div className="space-y-3">
            <div className="space-y-2">
              <p className="text-xs text-muted-foreground">Original:</p>
              <div className="bg-muted p-2 rounded text-xs max-h-16 overflow-y-auto">
                {selectedText}
              </div>
            </div>
            
            <div className="space-y-2">
              <p className="text-xs text-muted-foreground">AI Suggestion:</p>
              <div className="bg-primary/5 p-2 rounded text-xs max-h-24 overflow-y-auto border border-primary/20">
                {result}
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Button 
                onClick={() => onApplyEdit(result)}
                size="sm"
              >
                Apply
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setResult('')}
                size="sm"
              >
                Try Again
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map(({ icon: Icon, label, action }) => (
                <Button
                  key={action}
                  variant="outline"
                  size="sm"
                  className="h-auto p-2 flex flex-col items-center space-y-1"
                  onClick={() => handleQuickAction(action)}
                >
                  <Icon className="h-4 w-4" />
                  <span className="text-xs">{label}</span>
                </Button>
              ))}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => setMode('custom')}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Custom Prompt
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}