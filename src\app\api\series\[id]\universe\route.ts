import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import type { Character, StoryBible } from '@/lib/db/types';

// Universe-related type definitions
interface UniverseBook {
  id: string;
  name: string;
  status: string;
  word_count: number;
  series_order: number;
  characters?: Character[];
  story_bible?: StoryBible[];
}


interface UniverseElement {
  name: string;
  description?: string;
  appearances: string[];
}

interface UniverseElements {
  characters: UniverseElement[];
  locations: UniverseElement[];
  concepts: UniverseElement[];
  summary: {
    totalCharacters: number;
    totalLocations: number;
    totalConcepts: number;
  };
}

interface UniverseUpdateData {
  shared_universe?: string | null;
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;

    // Get series with universe information
    const { data: series, error: seriesError } = await supabase
      .from('book_series')
      .select('*')
      .eq('id', seriesId)
      .single();

    if (seriesError) {
      if (seriesError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Series not found' }, { status: 404 });
      }
      throw seriesError;
    }

    if (!series.shared_universe) {
      return NextResponse.json({
        universe: null,
        message: 'This series is not part of a shared universe'
      });
    }

    // Get all series that share the same universe
    const { data: universeSeries, error: universeError } = await supabase
      .from('book_series')
      .select(`
        *,
        books:projects!series_id(
          id,
          name,
          status,
          word_count,
          series_order
        )
      `)
      .eq('shared_universe', series.shared_universe)
      .order('created_at', { ascending: true });

    if (universeError) throw universeError;

    // Get shared universe elements (characters, locations, etc.)
    const universeElements = await getUniverseElements(series.shared_universe);

    const totalBooks = universeSeries?.reduce((sum, s) => sum + (s.books?.length || 0), 0) || 0;
    const totalWordCount = universeSeries?.reduce((sum, s) => 
      sum + (s.books?.reduce((bookSum: number, book: UniverseBook) => bookSum + (book.word_count || 0), 0) || 0), 0
    ) || 0;

    return NextResponse.json({
      universe: {
        name: series.shared_universe,
        totalSeries: universeSeries?.length || 0,
        totalBooks,
        totalWordCount,
        series: universeSeries?.map(s => ({
          id: s.id,
          name: s.name,
          description: s.description,
          status: s.status,
          bookCount: s.books?.length || 0,
          createdAt: new Date(s.created_at),
          books: s.books?.map((book: UniverseBook) => ({
            id: book.id,
            name: book.name,
            status: book.status,
            wordCount: book.word_count,
            order: book.series_order
          })).sort((a: any, b: any) => a.order - b.order) || []
        })) || [],
        elements: universeElements
      }
    });
  } catch (error) {
    console.error('Universe API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const body = await request.json();
    const { universeName, action } = body; // action: 'join', 'create', 'leave'

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }

    const updateData: UniverseUpdateData = {};

    switch (action) {
      case 'create':
        if (!universeName) {
          return NextResponse.json({ error: 'Universe name is required' }, { status: 400 });
        }
        updateData.shared_universe = universeName;
        break;
        
      case 'join':
        if (!universeName) {
          return NextResponse.json({ error: 'Universe name is required' }, { status: 400 });
        }
        
        // Verify universe exists
        const { data: existingUniverse } = await supabase
          .from('book_series')
          .select('shared_universe')
          .eq('shared_universe', universeName)
          .limit(1);

        if (!existingUniverse || existingUniverse.length === 0) {
          return NextResponse.json({ error: 'Universe does not exist' }, { status: 400 });
        }
        
        updateData.shared_universe = universeName;
        break;
        
      case 'leave':
        updateData.shared_universe = null;
        break;
        
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    const { data: series, error: updateError } = await supabase
      .from('book_series')
      .update(updateData)
      .eq('id', seriesId)
      .select()
      .single();

    if (updateError) {
      if (updateError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Series not found' }, { status: 404 });
      }
      throw updateError;
    }

    // Track analytics
    try {
      await supabase
        .from('selection_analytics')
        .insert({
          user_id: series.user_id,
          event_type: `universe_${action}`,
          selection_data: {
            seriesId,
            seriesName: series.name,
            universeName: action === 'leave' ? 'none' : universeName,
            action
          }
        });
    } catch (analyticsError) {
      console.warn('Failed to track universe action analytics:', analyticsError);
    }

    return NextResponse.json({ 
      success: true,
      series: {
        id: series.id,
        name: series.name,
        sharedUniverse: series.shared_universe
      },
      action,
      message: getActionMessage(action, universeName)
    });
  } catch (error) {
    console.error('Universe management API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function getUniverseElements(universeName: string): Promise<UniverseElements> {
  try {
    // Get all series in the universe
    const { data: series } = await supabase
      .from('book_series')
      .select(`
        id,
        books:projects!series_id(
          id,
          characters(
            name,
            role,
            description
          ),
          story_bible(
            category,
            title,
            content
          )
        )
      `)
      .eq('shared_universe', universeName);

    const elements = {
      characters: new Map(),
      locations: new Map(),
      concepts: new Map(),
      timeline: []
    };

    series?.forEach(s => {
      s.books?.forEach((book: any) => {
        // Collect characters
        book.characters?.forEach((char: any) => {
          if (!elements.characters.has(char.name)) {
            elements.characters.set(char.name, {
              name: char.name,
              role: char.role,
              description: char.description,
              appearances: []
            });
          }
          elements.characters.get(char.name).appearances.push(book.id);
        });

        // Collect world-building elements
        book.story_bible?.forEach((entry: any) => {
          if (entry.category === 'locations' || entry.category === 'setting') {
            if (!elements.locations.has(entry.title)) {
              elements.locations.set(entry.title, {
                name: entry.title,
                description: entry.content,
                appearances: []
              });
            }
            elements.locations.get(entry.title).appearances.push(book.id);
          } else if (entry.category === 'concepts' || entry.category === 'world-building') {
            if (!elements.concepts.has(entry.title)) {
              elements.concepts.set(entry.title, {
                name: entry.title,
                description: entry.content,
                appearances: []
              });
            }
            elements.concepts.get(entry.title).appearances.push(book.id);
          }
        });
      });
    });

    return {
      characters: Array.from(elements.characters.values()).slice(0, 20), // Limit results
      locations: Array.from(elements.locations.values()).slice(0, 20),
      concepts: Array.from(elements.concepts.values()).slice(0, 20),
      summary: {
        totalCharacters: elements.characters.size,
        totalLocations: elements.locations.size,
        totalConcepts: elements.concepts.size
      }
    };
  } catch (error) {
    console.error('Error getting universe elements:', error);
    return {
      characters: [],
      locations: [],
      concepts: [],
      summary: { totalCharacters: 0, totalLocations: 0, totalConcepts: 0 }
    };
  }
}

function getActionMessage(action: string, universeName?: string) {
  switch (action) {
    case 'create':
      return `Created new shared universe: "${universeName}"`;
    case 'join':
      return `Joined shared universe: "${universeName}"`;
    case 'leave':
      return 'Left shared universe';
    default:
      return 'Universe action completed';
  }
}