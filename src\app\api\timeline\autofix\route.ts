import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { getTimelineValidator } from '@/lib/timeline/timeline-instances';

export async function POST(request: NextRequest) {
  try {
    const { projectId, conflictIds } = await request.json();
    
    if (!projectId || !conflictIds || !Array.isArray(conflictIds)) {
      return NextResponse.json(
        { error: 'Project ID and conflict IDs array are required' },
        { status: 400 }
      );
    }

    const validator = getTimelineValidator(projectId);

    const result = await validator.autoFixConflicts(conflictIds);
    
    // Re-validate timeline after fixes
    const validation = await validator.validateTimeline();
    
    return NextResponse.json({
      success: true,
      fixed: result.fixed,
      failed: result.failed,
      validation
    });

  } catch (error) {
    console.error('Error auto-fixing timeline conflicts:', error);
    return NextResponse.json(
      { error: 'Failed to auto-fix timeline conflicts' },
      { status: 500 }
    );
  }
}