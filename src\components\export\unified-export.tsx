'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { 
  Download, 
  FileText, 
  File, 
  BookOpen, 
  CheckCircle, 
  AlertCircle,
  FileCode,
  Eye,
  Loader2
} from 'lucide-react';
import type { ExportOptions } from '@/lib/export/export-service';

interface UnifiedExportProps {
  projectId: string;
  projectTitle: string;
  projectName?: string;
  hasCompletedChapters?: boolean;
  chapters?: Array<{ id: string; chapter_number: number; title?: string; word_count: number }>;
  children?: React.ReactNode;
  mode?: 'dialog' | 'page';
}

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  mimeType: string;
  features: string[];
  isPremium?: boolean;
  recommended?: boolean;
}

const exportFormats: ExportFormat[] = [
  {
    id: 'txt',
    name: 'Enhanced Text',
    description: 'Formatted text file with professional layout',
    icon: <FileText className="h-5 w-5" />,
    mimeType: 'text/plain',
    features: ['Universal compatibility', 'Character reference', 'Story outline', 'Metadata'],
  },
  {
    id: 'pdf',
    name: 'PDF Document',
    description: 'Professional PDF with formatting and layout',
    icon: <File className="h-5 w-5" />,
    mimeType: 'application/pdf',
    features: ['Professional formatting', 'Print-ready', 'Page breaks', 'Table of contents'],
    recommended: true
  },
  {
    id: 'docx',
    name: 'Word Document',
    description: 'Microsoft Word format for editing',
    icon: <FileText className="h-5 w-5" />,
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    features: ['Easy editing', 'Track changes', 'Comments', 'Professional layout'],
  },
  {
    id: 'epub',
    name: 'EPUB eBook',
    description: 'Standard eBook format for readers',
    icon: <BookOpen className="h-5 w-5" />,
    mimeType: 'application/epub+zip',
    features: ['E-reader compatible', 'Reflowable text', 'Chapter navigation', 'Metadata'],
    isPremium: true
  },
  {
    id: 'html',
    name: 'HTML Website',
    description: 'Complete website with navigation',
    icon: <FileCode className="h-5 w-5" />,
    mimeType: 'text/html',
    features: ['Browser viewable', 'Interactive navigation', 'Mobile responsive', 'Shareable link'],
    isPremium: true
  }
];

export function UnifiedExport({ 
  projectId, 
  projectTitle,
  projectName,
  hasCompletedChapters = true,
  chapters = [],
  children,
  mode = 'dialog'
}: UnifiedExportProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeMetadata: true,
    includeFrontMatter: true,
    includeChapterBreaks: true,
    includeTableOfContents: true,
    customStyling: {
      fontFamily: 'serif',
      fontSize: 12,
      lineSpacing: 1.5,
      margins: {
        top: 1,
        bottom: 1,
        left: 1,
        right: 1,
      },
    },
    pageFormat: 'letter',
  });
  const [selectedChapters, setSelectedChapters] = useState<string[]>(
    chapters.map(ch => ch.id)
  );
  const [, setPreviewVisible] = useState(false);

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      const response = await fetch(`/api/projects/${projectId}/export`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...exportOptions,
          format: selectedFormat,
          selectedChapters: mode === 'page' ? selectedChapters : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${projectTitle || projectName || 'project'}.${selectedFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export error:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const ExportContent = () => (
    <>
      <div className="grid gap-6">
        {/* Format Selection */}
        <div className="space-y-4">
          <Label className="text-base font-medium">Export Format</Label>
          <div className="grid gap-3">
            {exportFormats.map((format) => (
              <Card 
                key={format.id}
                className={`cursor-pointer transition-all ${
                  selectedFormat === format.id 
                    ? 'ring-2 ring-primary border-primary' 
                    : 'hover:border-primary/50'
                }`}
                onClick={() => setSelectedFormat(format.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    <div className={`p-2 rounded-lg ${
                      selectedFormat === format.id 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted'
                    }`}>
                      {format.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{format.name}</h3>
                        {format.recommended && (
                          <Badge variant="secondary" className="text-xs">
                            Recommended
                          </Badge>
                        )}
                        {format.isPremium && (
                          <Badge variant="outline" className="text-xs">
                            Premium
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {format.description}
                      </p>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {format.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center gap-1 text-xs text-muted-foreground">
                            <CheckCircle className="h-3 w-3" />
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {mode === 'page' && (
          <>
            <Separator />
            
            {/* Advanced Options */}
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="formatting">Formatting</TabsTrigger>
                <TabsTrigger value="preview">Preview</TabsTrigger>
              </TabsList>
              
              <TabsContent value="content" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="metadata">Include Metadata</Label>
                    <Switch
                      id="metadata"
                      checked={exportOptions.includeMetadata}
                      onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includeMetadata: checked }))
                      }
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="frontmatter">Include Front Matter</Label>
                    <Switch
                      id="frontmatter"
                      checked={exportOptions.includeFrontMatter}
                      onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includeFrontMatter: checked }))
                      }
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="toc">Table of Contents</Label>
                    <Switch
                      id="toc"
                      checked={exportOptions.includeTableOfContents}
                      onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includeTableOfContents: checked }))
                      }
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="breaks">Chapter Page Breaks</Label>
                    <Switch
                      id="breaks"
                      checked={exportOptions.includeChapterBreaks}
                      onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includeChapterBreaks: checked }))
                      }
                    />
                  </div>
                  
                  {chapters.length > 0 && (
                    <div className="space-y-2">
                      <Label>Select Chapters</Label>
                      <div className="border rounded-lg p-4 max-h-48 overflow-y-auto space-y-2">
                        {chapters.map((chapter) => (
                          <div key={chapter.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={chapter.id}
                              checked={selectedChapters.includes(chapter.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedChapters([...selectedChapters, chapter.id]);
                                } else {
                                  setSelectedChapters(
                                    selectedChapters.filter(id => id !== chapter.id)
                                  );
                                }
                              }}
                            />
                            <Label
                              htmlFor={chapter.id}
                              className="flex-1 cursor-pointer"
                            >
                              Chapter {chapter.chapter_number}
                              {chapter.title && `: ${chapter.title}`}
                              <span className="text-sm text-muted-foreground ml-2">
                                ({chapter.word_count} words)
                              </span>
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="formatting" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div>
                    <Label>Font Family</Label>
                    <Select
                      value={exportOptions.customStyling?.fontFamily}
                      onValueChange={(value) =>
                        setExportOptions(prev => ({
                          ...prev,
                          customStyling: {
                            ...prev.customStyling!,
                            fontFamily: value,
                          },
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="serif">Serif</SelectItem>
                        <SelectItem value="sans-serif">Sans Serif</SelectItem>
                        <SelectItem value="monospace">Monospace</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label>Font Size: {exportOptions.customStyling?.fontSize}pt</Label>
                    <Slider
                      value={[exportOptions.customStyling?.fontSize || 12]}
                      onValueChange={([value]) =>
                        setExportOptions(prev => ({
                          ...prev,
                          customStyling: {
                            ...prev.customStyling!,
                            fontSize: value,
                          },
                        }))
                      }
                      min={10}
                      max={16}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Line Spacing: {exportOptions.customStyling?.lineSpacing}</Label>
                    <Slider
                      value={[exportOptions.customStyling?.lineSpacing || 1.5]}
                      onValueChange={([value]) =>
                        setExportOptions(prev => ({
                          ...prev,
                          customStyling: {
                            ...prev.customStyling!,
                            lineSpacing: value,
                          },
                        }))
                      }
                      min={1}
                      max={3}
                      step={0.5}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Page Format</Label>
                    <Select
                      value={exportOptions.pageFormat}
                      onValueChange={(value) =>
                        setExportOptions(prev => ({ ...prev, pageFormat: value as 'letter' | 'a4' | 'custom' }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="letter">Letter (8.5&quot; × 11&quot;)</SelectItem>
                        <SelectItem value="a4">A4 (210mm × 297mm)</SelectItem>
                        <SelectItem value="trade">Trade (6&quot; × 9&quot;)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="preview" className="mt-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-center h-64 text-muted-foreground">
                      <div className="text-center space-y-2">
                        <Eye className="h-12 w-12 mx-auto opacity-20" />
                        <p className="text-sm">Preview will be available in a future update</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}

        {/* Export Options */}
        <div className="space-y-4">
          <Separator />
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="metadata">Include project metadata</Label>
              <Switch
                id="metadata"
                checked={exportOptions.includeMetadata}
                onCheckedChange={(checked) =>
                  setExportOptions(prev => ({ ...prev, includeMetadata: checked }))
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="frontmatter">Include title page & copyright</Label>
              <Switch
                id="frontmatter"
                checked={exportOptions.includeFrontMatter}
                onCheckedChange={(checked) =>
                  setExportOptions(prev => ({ ...prev, includeFrontMatter: checked }))
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="chapter-breaks">Page breaks between chapters</Label>
              <Switch
                id="chapter-breaks"
                checked={exportOptions.includeChapterBreaks}
                onCheckedChange={(checked) =>
                  setExportOptions(prev => ({ ...prev, includeChapterBreaks: checked }))
                }
              />
            </div>
          </div>
        </div>

        {!hasCompletedChapters && mode === 'dialog' && (
          <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
            <div className="flex gap-2">
              <AlertCircle className="h-5 w-5 text-yellow-600 shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-yellow-800">No completed chapters</p>
                <p className="text-yellow-700 mt-1">
                  Complete at least one chapter before exporting your project.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-end gap-3 mt-6">
        {mode === 'page' && (
          <Button
            variant="outline"
            onClick={() => setPreviewVisible(true)}
            disabled={!selectedFormat || selectedChapters.length === 0}
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
        )}
        <Button 
          onClick={handleExport}
          disabled={isExporting || !hasCompletedChapters || !selectedFormat || (mode === 'page' && selectedChapters.length === 0)}
        >
          {isExporting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Exporting...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Export {exportFormats.find(f => f.id === selectedFormat)?.name}
            </>
          )}
        </Button>
      </div>
    </>
  );

  if (mode === 'dialog') {
    return (
      <Dialog>
        <DialogTrigger asChild>
          {children || (
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Project
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Export Project</DialogTitle>
            <DialogDescription>
              Choose your export format and options for &quot;{projectTitle}&quot;
            </DialogDescription>
          </DialogHeader>
          <ExportContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Export Project</CardTitle>
        <CardDescription>
          Choose your export format and customize the output for &quot;{projectName || projectTitle}&quot;
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ExportContent />
      </CardContent>
    </Card>
  );
}