import OpenAI from 'openai';

interface VoiceProfile {
  id: string;
  projectId: string;
  patterns: {
    sentenceStructure: {
      averageLength: number;
      complexityScore: number;
      variationScore: number;
    };
    vocabulary: {
      uniqueWords: number;
      averageWordLength: number;
      formalityScore: number;
      commonPhrases: string[];
    };
    style: {
      descriptivenesss: number;
      dialogueRatio: number;
      actionRatio: number;
      introspectionRatio: number;
    };
    tone: {
      emotionalRange: string[];
      intensity: number;
      consistency: number;
    };
    rhythm: {
      punctuationPatterns: Record<string, number>;
      paragraphLengthVariation: number;
      transitionWords: string[];
    };
  };
  sampleTexts: string[];
  confidence: number;
  lastUpdated: Date;
}

interface VoiceMatchSuggestion {
  id: string;
  type: 'voice';
  severity: 'suggestion';
  message: string;
  range: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  replacement?: string;
  explanation?: string;
  voiceAspect: 'sentence_structure' | 'vocabulary' | 'tone' | 'rhythm' | 'style';
}

export class VoiceAnalyzer {
  private openai: OpenAI;

  constructor(openai: OpenAI) {
    this.openai = openai;
  }

  async analyzeUserVoice(texts: string[], projectId: string): Promise<VoiceProfile> {
    try {
      const combinedText = texts.join('\n\n');
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        temperature: 0.2,
        messages: [
          {
            role: 'system',
            content: `You are an expert writing style analyst. Analyze the provided text samples to create a comprehensive voice profile. 

            Analyze and return JSON with:
            1. Sentence structure patterns (length, complexity, variation)
            2. Vocabulary characteristics (formality, uniqueness, common phrases)
            3. Style ratios (descriptive vs dialogue vs action vs introspection)
            4. Tone patterns (emotional range, intensity, consistency)
            5. Rhythm patterns (punctuation, paragraph variation, transitions)
            
            Provide numerical scores (0-100) and specific examples where relevant.
            Be precise and quantitative in your analysis.`
          },
          {
            role: 'user',
            content: `Analyze these writing samples to create a voice profile:\n\n${combinedText}`
          }
        ],
        response_format: { type: 'json_object' }
      });

      const analysisResult = JSON.parse(response.choices[0]?.message?.content || '{}');
      
      // Calculate additional metrics
      const metrics = this.calculateTextMetrics(combinedText);
      
      const voiceProfile: VoiceProfile = {
        id: `voice_${projectId}_${Date.now()}`,
        projectId,
        patterns: {
          sentenceStructure: {
            averageLength: metrics.averageSentenceLength,
            complexityScore: analysisResult.sentenceComplexity || 50,
            variationScore: metrics.sentenceLengthVariation,
          },
          vocabulary: {
            uniqueWords: metrics.uniqueWordCount,
            averageWordLength: metrics.averageWordLength,
            formalityScore: analysisResult.formalityScore || 50,
            commonPhrases: analysisResult.commonPhrases || [],
          },
          style: {
            descriptivenesss: analysisResult.descriptiveness || 50,
            dialogueRatio: metrics.dialogueRatio,
            actionRatio: analysisResult.actionRatio || 25,
            introspectionRatio: analysisResult.introspectionRatio || 25,
          },
          tone: {
            emotionalRange: analysisResult.emotionalRange || ['neutral'],
            intensity: analysisResult.emotionalIntensity || 50,
            consistency: analysisResult.toneConsistency || 50,
          },
          rhythm: {
            punctuationPatterns: metrics.punctuationPatterns,
            paragraphLengthVariation: metrics.paragraphVariation,
            transitionWords: analysisResult.transitionWords || [],
          },
        },
        sampleTexts: texts,
        confidence: this.calculateConfidence(texts),
        lastUpdated: new Date(),
      };

      return voiceProfile;
    } catch (error) {
      console.error('Error analyzing user voice:', error);
      throw error;
    }
  }

  async analyzeVoiceMatch(content: string, voiceProfile: VoiceProfile): Promise<VoiceMatchSuggestion[]> {
    try {
      const contentMetrics = this.calculateTextMetrics(content);

      // Analyze different aspects of voice matching
      const analyses = await Promise.all([
        this.analyzeSentenceStructureMatch(content, voiceProfile, contentMetrics),
        this.analyzeVocabularyMatch(content, voiceProfile, contentMetrics),
        this.analyzeToneMatch(content, voiceProfile),
        this.analyzeRhythmMatch(content, voiceProfile, contentMetrics),
      ]);

      return analyses.flat();
    } catch (error) {
      console.error('Error analyzing voice match:', error);
      return [];
    }
  }

  private async analyzeSentenceStructureMatch(
    _content: string, 
    voiceProfile: VoiceProfile, 
    metrics: Record<string, unknown>
  ): Promise<VoiceMatchSuggestion[]> {
    const suggestions: VoiceMatchSuggestion[] = [];
    const userAvgLength = Number(voiceProfile.patterns.sentenceStructure.averageLength);
    const contentAvgLength = Number(metrics.averageSentenceLength);

    // Check sentence length consistency
    if (!isNaN(userAvgLength) && !isNaN(contentAvgLength) && Math.abs(userAvgLength - contentAvgLength) > 5) {
      const deviation = contentAvgLength > userAvgLength ? 'longer' : 'shorter';
      suggestions.push({
        id: `voice_sentence_${Date.now()}`,
        type: 'voice',
        severity: 'suggestion',
        message: `Sentences are ${deviation} than your typical style`,
        range: { startLineNumber: 1, startColumn: 1, endLineNumber: 1, endColumn: 50 },
        explanation: `Your average sentence length is ${userAvgLength.toFixed(1)} words, but this content averages ${contentAvgLength.toFixed(1)} words.`,
        voiceAspect: 'sentence_structure',
      });
    }

    // Check sentence variation
    const contentVariation = Number(metrics.sentenceLengthVariation);
    const userVariation = Number(voiceProfile.patterns.sentenceStructure.variationScore);
    if (!isNaN(contentVariation) && !isNaN(userVariation) && contentVariation < userVariation - 20) {
      suggestions.push({
        id: `voice_variation_${Date.now()}`,
        type: 'voice',
        severity: 'suggestion',
        message: 'Consider varying sentence lengths more to match your style',
        range: { startLineNumber: 1, startColumn: 1, endLineNumber: 1, endColumn: 50 },
        explanation: 'Your writing typically has more sentence length variation.',
        voiceAspect: 'sentence_structure',
      });
    }

    return suggestions;
  }

  private async analyzeVocabularyMatch(
    _content: string, 
    voiceProfile: VoiceProfile, 
    metrics: Record<string, unknown>
  ): Promise<VoiceMatchSuggestion[]> {
    const suggestions: VoiceMatchSuggestion[] = [];
    
    // Check word length patterns
    const userAvgWordLength = Number(voiceProfile.patterns.vocabulary.averageWordLength);
    const contentAvgWordLength = Number(metrics.averageWordLength);

    if (!isNaN(userAvgWordLength) && !isNaN(contentAvgWordLength) && Math.abs(userAvgWordLength - contentAvgWordLength) > 0.5) {
      const complexity = contentAvgWordLength > userAvgWordLength ? 'more complex' : 'simpler';
      suggestions.push({
        id: `voice_vocabulary_${Date.now()}`,
        type: 'voice',
        severity: 'suggestion',
        message: `Vocabulary is ${complexity} than your typical style`,
        range: { startLineNumber: 1, startColumn: 1, endLineNumber: 1, endColumn: 50 },
        explanation: `Your typical word complexity differs from this content.`,
        voiceAspect: 'vocabulary',
      });
    }

    return suggestions;
  }

  private async analyzeToneMatch(content: string, voiceProfile: VoiceProfile): Promise<VoiceMatchSuggestion[]> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        temperature: 0.3,
        messages: [
          {
            role: 'system',
            content: `Compare the tone of the provided content with the user's typical tone profile:
            
            User's typical emotional range: ${voiceProfile.patterns.tone.emotionalRange.join(', ')}
            User's typical intensity: ${voiceProfile.patterns.tone.intensity}/100
            User's typical consistency: ${voiceProfile.patterns.tone.consistency}/100
            
            Identify tone mismatches and return as JSON array of suggestions.`
          },
          {
            role: 'user',
            content: `Analyze tone consistency for: ${content.slice(0, 1000)}...`
          }
        ],
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0]?.message?.content || '{"suggestions": []}');
      return result.suggestions.map((s: Record<string, unknown>, i: number) => ({
        id: `voice_tone_${Date.now()}_${i}`,
        type: 'voice',
        severity: 'suggestion',
        message: s.message || 'Tone differs from your typical style',
        range: { startLineNumber: 1, startColumn: 1, endLineNumber: 1, endColumn: 50 },
        explanation: s.explanation,
        voiceAspect: 'tone',
      }));
    } catch (error) {
      console.error('Error in tone analysis:', error);
      return [];
    }
  }

  private async analyzeRhythmMatch(
    _content: string, 
    voiceProfile: VoiceProfile, 
    metrics: Record<string, unknown>
  ): Promise<VoiceMatchSuggestion[]> {
    const suggestions: VoiceMatchSuggestion[] = [];

    // Check paragraph variation
    const contentParagraphVariation = Number(metrics.paragraphVariation);
    const userParagraphVariation = Number(voiceProfile.patterns.rhythm.paragraphLengthVariation);
    if (!isNaN(contentParagraphVariation) && !isNaN(userParagraphVariation) && Math.abs(contentParagraphVariation - userParagraphVariation) > 20) {
      suggestions.push({
        id: `voice_rhythm_${Date.now()}`,
        type: 'voice',
        severity: 'suggestion',
        message: 'Paragraph length variation differs from your typical style',
        range: { startLineNumber: 1, startColumn: 1, endLineNumber: 1, endColumn: 50 },
        explanation: 'Your writing typically has different paragraph rhythm patterns.',
        voiceAspect: 'rhythm',
      });
    }

    return suggestions;
  }

  private calculateTextMetrics(text: string): {
    averageSentenceLength: number;
    sentenceLengthVariation: number;
    uniqueWordCount: number;
    averageWordLength: number;
    dialogueRatio: number;
    punctuationPatterns: Record<string, number>;
    paragraphVariation: number;
  } {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim());
    const words = text.split(/\s+/).filter(w => w.trim());
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim());
    
    // Calculate sentence lengths
    const sentenceLengths = sentences.map(s => s.split(/\s+/).length);
    const averageSentenceLength = sentenceLengths.reduce((a, b) => a + b, 0) / sentenceLengths.length || 0;
    
    // Calculate sentence length variation (coefficient of variation)
    const sentenceVariance = sentenceLengths.reduce((acc, len) => acc + Math.pow(len - averageSentenceLength, 2), 0) / sentenceLengths.length;
    const sentenceLengthVariation = Math.sqrt(sentenceVariance) / averageSentenceLength * 100;

    // Calculate word metrics
    const uniqueWords = new Set(words.map(w => w.toLowerCase())).size;
    const averageWordLength = words.reduce((acc, word) => acc + word.length, 0) / words.length || 0;

    // Calculate dialogue ratio
    const dialogueMatches = text.match(/"[^"]*"/g) || [];
    const dialogueWordCount = dialogueMatches.join(' ').split(/\s+/).length;
    const dialogueRatio = (dialogueWordCount / words.length) * 100;

    // Calculate punctuation patterns
    const punctuationPatterns: Record<string, number> = {
      periods: (text.match(/\./g) || []).length,
      commas: (text.match(/,/g) || []).length,
      semicolons: (text.match(/;/g) || []).length,
      colons: (text.match(/:/g) || []).length,
      dashes: (text.match(/—|--/g) || []).length,
      exclamations: (text.match(/!/g) || []).length,
      questions: (text.match(/\?/g) || []).length,
    };

    // Calculate paragraph variation
    const paragraphLengths = paragraphs.map(p => p.split(/\s+/).length);
    const avgParagraphLength = paragraphLengths.reduce((a, b) => a + b, 0) / paragraphLengths.length || 0;
    const paragraphVariance = paragraphLengths.reduce((acc, len) => acc + Math.pow(len - avgParagraphLength, 2), 0) / paragraphLengths.length;
    const paragraphVariation = Math.sqrt(paragraphVariance) / avgParagraphLength * 100;

    return {
      averageSentenceLength,
      sentenceLengthVariation,
      uniqueWordCount: uniqueWords,
      averageWordLength,
      dialogueRatio,
      punctuationPatterns,
      paragraphVariation,
    };
  }

  private calculateConfidence(texts: string[]): number {
    let confidence = 50; // Base confidence

    // More text samples = higher confidence
    confidence += Math.min(texts.length * 10, 30);

    // Longer samples = higher confidence
    const totalWords = texts.join(' ').split(/\s+/).length;
    confidence += Math.min(totalWords / 100, 20);

    // Return confidence capped at 100
    return Math.min(confidence, 100);
  }

  async updateVoiceProfile(voiceProfile: VoiceProfile, newText: string): Promise<VoiceProfile> {
    // Add new sample text
    const updatedSampleTexts = [...voiceProfile.sampleTexts.slice(-4), newText]; // Keep last 5 samples
    
    // Reanalyze with updated samples
    return this.analyzeUserVoice(updatedSampleTexts, voiceProfile.projectId);
  }
}