-- Series Management Tables
-- This migration adds comprehensive series management functionality

-- Series Table
CREATE TABLE series (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Series metadata
  genre VARCHAR(100),
  target_audience VARCHAR(50),
  overall_arc_description TEXT,
  planned_book_count INTEGER,
  current_book_count INTEGER DEFAULT 0,
  
  -- Series settings
  shared_universe_rules JSONB, -- World building rules that apply across all books
  character_continuity JSONB, -- Characters that appear across multiple books
  timeline_span VARCHAR(100), -- How much time the series covers
  
  -- Publishing information
  publication_status VARCHAR(50) DEFAULT 'planning', -- 'planning', 'active', 'completed', 'hiatus'
  first_published_date DATE,
  last_published_date DATE,
  
  -- System fields
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Series Books Junction Table (replaces simple project series reference)
CREATE TABLE series_books (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  series_id UUID REFERENCES series(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  book_number INTEGER NOT NULL,
  
  -- Book-specific series information
  book_role VARCHAR(50), -- 'main', 'prequel', 'sequel', 'spin-off', 'companion'
  chronological_order INTEGER, -- Order in story timeline (may differ from publication order)
  publication_order INTEGER, -- Order of publication
  
  -- Continuity tracking
  introduces_characters UUID[], -- Character IDs introduced in this book
  concludes_arcs TEXT[], -- Story arcs concluded in this book
  sets_up_future JSONB, -- Plot elements that set up future books
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure no duplicate books in series
  UNIQUE(series_id, project_id),
  UNIQUE(series_id, book_number)
);

-- Series Character Arcs (cross-book character development)
CREATE TABLE series_character_arcs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  series_id UUID REFERENCES series(id) ON DELETE CASCADE,
  character_name VARCHAR(255) NOT NULL, -- Name since characters might be across projects
  
  -- Arc information
  arc_title VARCHAR(255),
  arc_description TEXT,
  arc_type VARCHAR(50), -- 'main', 'romance', 'redemption', 'growth', etc.
  
  -- Timeline
  starts_in_book INTEGER, -- Book number where arc begins
  concludes_in_book INTEGER, -- Book number where arc concludes
  peak_moment_book INTEGER, -- Book where arc reaches climax
  
  -- Arc data
  character_growth_stages JSONB, -- Key development points
  relationship_changes JSONB, -- How relationships evolve
  skills_or_powers_gained JSONB, -- New abilities acquired
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Series Universe Rules (world building consistency)
CREATE TABLE series_universe_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  series_id UUID REFERENCES series(id) ON DELETE CASCADE,
  
  -- Rule categorization
  rule_category VARCHAR(100) NOT NULL, -- 'magic_system', 'technology', 'geography', 'culture', 'history'
  rule_title VARCHAR(255) NOT NULL,
  rule_description TEXT NOT NULL,
  
  -- Consistency tracking
  established_in_book INTEGER, -- Book number where rule was first established
  modified_in_books INTEGER[], -- Books where rule was modified or expanded
  exceptions JSONB, -- Any exceptions to the rule
  
  -- References and examples
  example_usage JSONB, -- Examples of how rule is applied
  related_rules UUID[], -- IDs of related universe rules
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Series Continuity Issues (tracking consistency problems)
CREATE TABLE series_continuity_issues (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  series_id UUID REFERENCES series(id) ON DELETE CASCADE,
  
  -- Issue identification
  issue_type VARCHAR(50) NOT NULL, -- 'character_inconsistency', 'timeline_conflict', 'world_rule_violation'
  issue_title VARCHAR(255) NOT NULL,
  issue_description TEXT NOT NULL,
  severity VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
  
  -- Location tracking
  occurs_in_books INTEGER[], -- Book numbers where issue appears
  specific_references JSONB, -- Specific chapters/pages where issue occurs
  
  -- Resolution tracking
  status VARCHAR(20) DEFAULT 'open', -- 'open', 'investigating', 'resolved', 'wont_fix'
  resolution_notes TEXT,
  resolved_in_book INTEGER, -- Book where issue was fixed
  resolved_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_series_user_id ON series(user_id);
CREATE INDEX idx_series_publication_status ON series(publication_status);
CREATE INDEX idx_series_books_series_id ON series_books(series_id);
CREATE INDEX idx_series_books_project_id ON series_books(project_id);
CREATE INDEX idx_series_books_book_number ON series_books(book_number);
CREATE INDEX idx_series_character_arcs_series_id ON series_character_arcs(series_id);
CREATE INDEX idx_series_universe_rules_series_id ON series_universe_rules(series_id);
CREATE INDEX idx_series_universe_rules_category ON series_universe_rules(rule_category);
CREATE INDEX idx_series_continuity_issues_series_id ON series_continuity_issues(series_id);
CREATE INDEX idx_series_continuity_issues_status ON series_continuity_issues(status);

-- Enable RLS on all tables
ALTER TABLE series ENABLE ROW LEVEL SECURITY;
ALTER TABLE series_books ENABLE ROW LEVEL SECURITY;
ALTER TABLE series_character_arcs ENABLE ROW LEVEL SECURITY;
ALTER TABLE series_universe_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE series_continuity_issues ENABLE ROW LEVEL SECURITY;

-- RLS Policies for series
CREATE POLICY "Users can view own series" ON series
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own series" ON series
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own series" ON series
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own series" ON series
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for series_books
CREATE POLICY "Users can view own series books" ON series_books
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM series s WHERE s.id = series_books.series_id AND s.user_id = auth.uid())
  );
CREATE POLICY "Users can manage own series books" ON series_books
  FOR ALL USING (
    EXISTS (SELECT 1 FROM series s WHERE s.id = series_books.series_id AND s.user_id = auth.uid())
  );

-- RLS Policies for series_character_arcs
CREATE POLICY "Users can view own series character arcs" ON series_character_arcs
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM series s WHERE s.id = series_character_arcs.series_id AND s.user_id = auth.uid())
  );
CREATE POLICY "Users can manage own series character arcs" ON series_character_arcs
  FOR ALL USING (
    EXISTS (SELECT 1 FROM series s WHERE s.id = series_character_arcs.series_id AND s.user_id = auth.uid())
  );

-- RLS Policies for series_universe_rules
CREATE POLICY "Users can view own series universe rules" ON series_universe_rules
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM series s WHERE s.id = series_universe_rules.series_id AND s.user_id = auth.uid())
  );
CREATE POLICY "Users can manage own series universe rules" ON series_universe_rules
  FOR ALL USING (
    EXISTS (SELECT 1 FROM series s WHERE s.id = series_universe_rules.series_id AND s.user_id = auth.uid())
  );

-- RLS Policies for series_continuity_issues
CREATE POLICY "Users can view own series continuity issues" ON series_continuity_issues
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM series s WHERE s.id = series_continuity_issues.series_id AND s.user_id = auth.uid())
  );
CREATE POLICY "Users can manage own series continuity issues" ON series_continuity_issues
  FOR ALL USING (
    EXISTS (SELECT 1 FROM series s WHERE s.id = series_continuity_issues.series_id AND s.user_id = auth.uid())
  );

-- Update triggers
CREATE TRIGGER update_series_updated_at
  BEFORE UPDATE ON series
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_series_books_updated_at
  BEFORE UPDATE ON series_books
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_series_character_arcs_updated_at
  BEFORE UPDATE ON series_character_arcs
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_series_universe_rules_updated_at
  BEFORE UPDATE ON series_universe_rules
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_series_continuity_issues_updated_at
  BEFORE UPDATE ON series_continuity_issues
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();