import { z } from 'zod';

const envSchema = z.object({
  // App Configuration
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1),
  SUPABASE_WEBHOOK_SECRET: z.string().optional(),
  SUPABASE_JWT_SECRET: z.string().optional(),
  
  // OpenAI Configuration
  OPENAI_API_KEY: z.string().min(1),
  
  // Google Gemini Configuration (optional)
  GENKIT_API_KEY: z.string().optional(),
  
  // Stripe Configuration
  STRIPE_SECRET_KEY: z.string().min(1),
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().min(1),
  STRIPE_WEBHOOK_SECRET: z.string().min(1),
  
  // Stripe Price IDs
  STRIPE_PRICE_ID_BASIC: z.string().min(1),
  STRIPE_PRICE_ID_PRO: z.string().min(1),
  STRIPE_PRICE_ID_ENTERPRISE: z.string().min(1),
  
  // Admin Configuration
  ADMIN_EMAILS: z.string().optional().default(''),
  
  // Development Configuration
  NEXT_PUBLIC_DEV_BYPASS_AUTH: z.string().optional().default('false'),
  DEV_USER_EMAIL: z.string().optional(),
  DEV_USER_ID: z.string().optional(),
  
  // Build Configuration
  npm_package_version: z.string().optional(),
  NEXT_PHASE: z.string().optional(),
});

type EnvConfig = z.infer<typeof envSchema>;

class Config {
  private static instance: Config;
  private config: EnvConfig;

  private constructor() {
    try {
      this.config = envSchema.parse(process.env);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const missingVars = error.errors.map(err => err.path.join('.')).join(', ');
        throw new Error(`Missing or invalid environment variables: ${missingVars}`);
      }
      throw error;
    }
  }

  static getInstance(): Config {
    if (!Config.instance) {
      Config.instance = new Config();
    }
    return Config.instance;
  }

  get env(): EnvConfig {
    return this.config;
  }

  get isProduction(): boolean {
    return this.config.NODE_ENV === 'production';
  }

  get isDevelopment(): boolean {
    return this.config.NODE_ENV === 'development';
  }

  get isTest(): boolean {
    return this.config.NODE_ENV === 'test';
  }

  get supabase() {
    return {
      url: this.config.NEXT_PUBLIC_SUPABASE_URL,
      anonKey: this.config.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      serviceRoleKey: this.config.SUPABASE_SERVICE_ROLE_KEY,
      webhookSecret: this.config.SUPABASE_WEBHOOK_SECRET,
      jwtSecret: this.config.SUPABASE_JWT_SECRET,
    };
  }

  get stripe() {
    return {
      secretKey: this.config.STRIPE_SECRET_KEY,
      publishableKey: this.config.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
      webhookSecret: this.config.STRIPE_WEBHOOK_SECRET,
      prices: {
        basic: this.config.STRIPE_PRICE_ID_BASIC,
        pro: this.config.STRIPE_PRICE_ID_PRO,
        enterprise: this.config.STRIPE_PRICE_ID_ENTERPRISE,
      },
    };
  }

  get openai() {
    return {
      apiKey: this.config.OPENAI_API_KEY,
    };
  }

  get app() {
    return {
      url: this.config.NEXT_PUBLIC_APP_URL,
    };
  }

  get adminEmails(): string[] {
    return this.config.ADMIN_EMAILS
      .split(',')
      .map(email => email.trim())
      .filter(email => email.length > 0);
  }

  get dev() {
    return {
      bypassAuth: this.config.NEXT_PUBLIC_DEV_BYPASS_AUTH === 'true',
      userEmail: this.config.DEV_USER_EMAIL,
      userId: this.config.DEV_USER_ID,
    };
  }

  get build() {
    return {
      version: this.config.npm_package_version,
      phase: this.config.NEXT_PHASE,
    };
  }
}

export const config = Config.getInstance();
export type { EnvConfig };