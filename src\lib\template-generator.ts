import { projectTemplates, type ProjectTemplate } from './project-templates';

export interface TemplateGenerationOptions {
  includeStoryArcs?: boolean;
  includeCharacters?: boolean;
  includeChapters?: boolean;
  includeStoryBible?: boolean;
  maxChapters?: number;
}

export interface GeneratedProjectData {
  project: Record<string, unknown>;
  storyArcs: Array<{
    act_number: number;
    description: string;
    key_events: string[];
  }>;
  characters: Array<{
    name: string;
    role: string;
    description: string;
    personality_traits: Record<string, unknown>;
    character_arc: Record<string, unknown>;
  }>;
  chapters: Array<{
    chapter_number: number;
    title: string;
    target_word_count: number;
    outline: string;
    status: string;
  }>;
  storyBible: Array<{
    category: string;
    title: string;
    content: string;
    tags: string[];
  }>;
}

export function generateProjectFromTemplate(
  template: ProjectTemplate,
  userId: string,
  customTitle?: string,
  customDescription?: string,
  options: TemplateGenerationOptions = {}
): GeneratedProjectData {
  const {
    includeStoryArcs = true,
    includeCharacters = true,
    includeChapters = true,
    includeStoryBible = true,
    maxChapters = 5
  } = options;

  // Generate project data
  const project = {
    user_id: userId,
    title: customTitle || template.name,
    description: customDescription || template.description,
    ...template.selections
  };

  // Generate story arcs
  const storyArcs = includeStoryArcs ? template.plotStructure.map(act => ({
    act_number: act.act,
    description: act.description,
    key_events: act.keyEvents
  })) : [];

  // Generate characters
  const characters = includeCharacters ? template.characterArchetypes.map(archetype => ({
    name: archetype.name,
    role: archetype.role,
    description: archetype.description,
    personality_traits: { traits: archetype.traits },
    character_arc: { 
      arc: `${archetype.role} character arc`,
      description: archetype.description 
    }
  })) : [];

  // Generate chapters
  const chaptersToCreate = includeChapters ? Math.min(maxChapters, template.estimatedChapters) : 0;
  const wordsPerChapter = Math.floor(template.estimatedWordCount / template.estimatedChapters);
  
  const chapters = [];
  for (let i = 1; i <= chaptersToCreate; i++) {
    chapters.push({
      chapter_number: i,
      title: `Chapter ${i}`,
      target_word_count: wordsPerChapter,
      outline: JSON.stringify({
        chapterNumber: i,
        title: `Chapter ${i}`,
        summary: `This chapter will advance the ${template.genre.toLowerCase()} narrative and develop key story elements.`,
        keyEvents: [`Opening scene for chapter ${i}`, `Character development moment`, `Plot advancement`],
        targetWordCount: wordsPerChapter,
        templateId: template.id,
        difficulty: template.difficulty
      }),
      status: 'planned'
    });
  }

  // Generate story bible entries
  const storyBible = includeStoryBible ? [
    {
      category: 'Setting',
      title: 'World Description',
      content: `This ${template.genre} story is set in ${template.selections.geographicSetting} during the ${template.selections.timePeriod}. The world features ${template.selections.magicTechLevel} and has a ${template.selections.worldType} structure.`,
      tags: [template.genre.toLowerCase(), 'setting', 'world-building']
    },
    {
      category: 'Themes',
      title: 'Core Themes',
      content: `Primary themes: ${template.selections.majorThemes.join(', ')}${template.selections.philosophicalThemes ? '. Philosophical elements: ' + template.selections.philosophicalThemes.join(', ') : ''}${template.selections.socialThemes ? '. Social themes: ' + template.selections.socialThemes.join(', ') : ''}.`,
      tags: ['themes', 'analysis', 'meaning']
    },
    {
      category: 'Structure',
      title: 'Narrative Structure',
      content: `This story follows a ${template.selections.structureType} with ${template.selections.pacingPreference} pacing. The narrative voice is ${template.selections.narrativeVoice} in ${template.selections.tense} tense, featuring ${template.selections.chapterStructure}.`,
      tags: ['structure', 'narrative', 'technique']
    },
    {
      category: 'Characters',
      title: 'Character Framework',
      content: `Protagonist types: ${template.selections.protagonistTypes.join(', ')}. Antagonist types: ${template.selections.antagonistTypes.join(', ')}. Character complexity: ${template.selections.characterComplexity} with ${template.selections.characterArcTypes.join(', ')} development arcs.`,
      tags: ['characters', 'development', 'archetypes']
    }
  ] : [];

  return {
    project,
    storyArcs,
    characters,
    chapters,
    storyBible
  };
}

export function getRandomTemplate(genre?: string, difficulty?: ProjectTemplate['difficulty']): ProjectTemplate {
  let filteredTemplates = projectTemplates;
  
  if (genre) {
    filteredTemplates = filteredTemplates.filter(t => t.genre.toLowerCase() === genre.toLowerCase());
  }
  
  if (difficulty) {
    filteredTemplates = filteredTemplates.filter(t => t.difficulty === difficulty);
  }
  
  if (filteredTemplates.length === 0) {
    // Fallback to all templates if no matches
    filteredTemplates = projectTemplates;
  }
  
  const randomIndex = Math.floor(Math.random() * filteredTemplates.length);
  const selectedTemplate = filteredTemplates[randomIndex];
  if (!selectedTemplate) {
    // Return first template as ultimate fallback
    return projectTemplates[0]!;
  }
  return selectedTemplate;
}

export function getTemplatesByTags(tags: string[]): ProjectTemplate[] {
  return projectTemplates.filter(template => 
    tags.some(tag => template.tags.some(templateTag => 
      templateTag.toLowerCase().includes(tag.toLowerCase())
    ))
  );
}

export function validateTemplateCompatibility(
  selections: Partial<ProjectTemplate['selections']>
): { isValid: boolean; conflicts: string[]; suggestions: string[] } {
  const conflicts: string[] = [];
  const suggestions: string[] = [];
  
  // Check for logical conflicts
  if (selections.timePeriod === 'Medieval Fantasy' && selections.magicTechLevel === 'High Tech') {
    conflicts.push('Medieval Fantasy setting conflicts with High Tech level');
    suggestions.push('Consider using "High Magic" instead of "High Tech" for medieval settings');
  }
  
  if (selections.narrativeVoice === 'First Person' && selections.povCharacterCount && selections.povCharacterCount > 1) {
    conflicts.push('First Person narrative typically works best with single POV');
    suggestions.push('Consider "Third Person Limited" for multiple POV characters');
  }
  
  if (selections.targetAudience === 'Children' && selections.contentRating === 'R') {
    conflicts.push('Children\'s content should not have R rating');
    suggestions.push('Consider PG or PG-13 rating for children\'s content');
  }
  
  if (selections.projectScope === 'Short Story' && selections.targetWordCount && selections.targetWordCount > 15000) {
    conflicts.push('Short stories typically under 15,000 words');
    suggestions.push('Consider "Novella" or "Novel" scope for longer works');
  }
  
  return {
    isValid: conflicts.length === 0,
    conflicts,
    suggestions
  };
}