import OpenAI from 'openai';
type ChatCompletionMessageParam = OpenAI.Chat.Completions.ChatCompletionMessageParam;
type ChatCompletionTool = OpenAI.Chat.Completions.ChatCompletionTool;
import { BookContext } from './types';
import { config } from '@/lib/config';

export abstract class BaseAgent {
  protected openai: OpenAI;
  protected context: BookContext;

  constructor(context: BookContext) {
    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });
    this.context = context;
  }

  protected async createCompletion(messages: ChatCompletionMessageParam[], tools?: ChatCompletionTool[], model: string = 'gpt-4') {
    try {
      const completion = await this.openai.chat.completions.create({
        model,
        messages,
        tools,
        tool_choice: tools ? 'auto' : undefined,
        temperature: 0.7,
      });

      return completion;
    } catch (error) {
      console.error(`Error in ${this.constructor.name}:`, error);
      throw error;
    }
  }

  protected createSystemPrompt(role: string, instructions: string): string {
    const settingsContext = this.buildSettingsContext();
    
    return `You are ${role}, an expert AI agent specializing in novel writing.

${instructions}

PROJECT SETTINGS CONTEXT:
${settingsContext}

Always maintain consistency with these project settings and previous agent outputs in the context.`;
  }

  private buildSettingsContext(): string {
    const settings = this.context.settings || this.context.projectSelections;
    
    if (!settings) {
      return 'No project settings available';
    }
    
    return `
GENRE & STYLE:
- Primary Genre: ${settings.primaryGenre}
- Subgenre: ${settings.subgenre || 'None specified'}
- Narrative Voice: ${settings.narrativeVoice}
- Tense: ${settings.tense}
- Writing Style: ${settings.writingStyle}
- Tone: ${settings.tone?.join(', ') || 'None specified'}

STORY STRUCTURE:
- Structure Type: ${settings.structureType}
- Pacing: ${settings.pacingPreference}
- Timeline Complexity: ${settings.timelineComplexity}

WORLD & CHARACTERS:
- Time Period: ${settings.timePeriod}
- World Type: ${settings.worldType}
- Character Complexity: ${settings.characterComplexity}
- Magic/Tech Level: ${settings.magicTechLevel}

THEMES:
- Major Themes: ${settings.majorThemes?.join(', ') || 'None specified'}
- Philosophical Themes: ${settings.philosophicalThemes?.join(', ') || 'None specified'}
- Social Themes: ${settings.socialThemes?.join(', ') || 'None specified'}

TECHNICAL SPECS:
- Target Word Count: ${settings.targetWordCount?.toLocaleString()}
- POV Type: ${settings.povCharacterType}
- Content Rating: ${settings.contentRating}

STORY CONCEPT:
${settings.initialConcept}
    `.trim();
  }

  abstract execute(): Promise<unknown>;
}