"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { SimpleTypewriter } from "@/components/ui/simple-typewriter";
import { SimpleParticles } from "@/components/ui/simple-particles";
import { PenTool, <PERSON><PERSON><PERSON>, <PERSON>rkles, Brain, Layers, Zap, Shield, Globe, Palette, Star, ChevronRight } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-black text-white overflow-hidden">
      {/* Animated gradient background */}
      <div className="fixed inset-0 bg-gradient-to-br from-neutral-950 via-neutral-900 to-amber-950/20" />
      <SimpleParticles />
      
      {/* Header */}
      <header className="relative z-50 border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-20 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-orange-600 blur-lg opacity-50" />
              <div className="relative w-10 h-10 bg-gradient-to-br from-amber-600 to-orange-600 rounded-lg flex items-center justify-center">
                <PenTool className="w-6 h-6 text-white" />
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight">
              BookScribe AI
            </h1>
          </div>
          
          <nav className="flex gap-2 sm:gap-4">
            <a href="/login">
              <Button 
                variant="ghost" 
                className="text-white/80 hover:text-white hover:bg-white/10 font-medium"
              >
                Sign In
              </Button>
            </a>
            <a href="/signup">
              <Button className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg font-medium px-6">
                Get Started
                <ChevronRight className="w-4 h-4 ml-1" />
              </Button>
            </a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main id="main-content" className="relative z-10">
        <section className="relative min-h-[90vh] flex items-center justify-center px-4 py-20">
          <div className="container max-w-6xl mx-auto text-center">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-amber-500/20 bg-amber-500/10 mb-8">
              <Star className="w-4 h-4 text-amber-500" />
              <span className="text-sm text-amber-200">Trusted by 10,000+ authors worldwide</span>
            </div>

            {/* Main heading with gradient */}
            <h2 className="text-4xl sm:text-5xl md:text-7xl lg:text-8xl font-bold mb-6">
              <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Write Your Next
              </span>
              <br />
              <span className="bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 bg-clip-text text-transparent animate-gradient bg-[length:200%_auto]">
                <SimpleTypewriter 
                  words={["Bestseller", "Masterpiece", "Epic Novel", "Story"]} 
                  typingSpeed={150}
                  deletingSpeed={75}
                  delay={2000}
                />
              </span>
            </h2>
            
            <p className="text-lg sm:text-xl md:text-2xl text-gray-400 max-w-3xl mx-auto leading-relaxed mb-8 px-4">
              The most advanced AI writing platform for novelists.
              <span className="block mt-2 text-amber-400">
                300,000+ words. Perfect consistency. Your voice.
              </span>
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <a href="/signup">
                <Button 
                  size="lg" 
                  className="group relative overflow-hidden bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-2xl font-medium px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg rounded-xl transition-all duration-300 w-full sm:w-auto"
                >
                  <span className="relative z-10 flex items-center">
                    <Sparkles className="w-5 h-5 mr-2" />
                    Start Writing Free
                  </span>
                  <div className="absolute inset-0 bg-white/20 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300" />
                </Button>
              </a>
              <a href="/demo">
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="border-white/20 text-white hover:bg-white/10 font-medium px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg rounded-xl backdrop-blur-sm w-full sm:w-auto"
                >
                  <BookOpen className="w-5 h-5 mr-2" />
                  See It In Action
                </Button>
              </a>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-amber-500 mb-2">2M+</div>
                <div className="text-sm text-gray-400">Words Written Daily</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-amber-500 mb-2">98%</div>
                <div className="text-sm text-gray-400">Author Satisfaction</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-amber-500 mb-2">50+</div>
                <div className="text-sm text-gray-400">Published Novels</div>
              </div>
            </div>
          </div>

          {/* Scroll indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-white/20 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-white/50 rounded-full mt-2" />
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="relative py-20 px-4 border-t border-white/10">
          <div className="container max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h3 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4">
                Everything You Need to 
                <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent"> Write Brilliantly</span>
              </h3>
              <p className="text-lg sm:text-xl text-gray-400 max-w-2xl mx-auto px-4">
                Advanced AI technology meets intuitive design to create the ultimate writing experience
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Feature cards */}
              <div className="group relative overflow-hidden rounded-2xl border border-white/10 backdrop-blur-sm bg-gradient-to-br from-neutral-900/90 to-neutral-800/90 p-6 hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 via-orange-500/20 to-red-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-12 h-12 rounded-xl bg-gradient-to-br from-amber-500/20 to-orange-500/20 flex items-center justify-center">
                    <Brain className="w-6 h-6 text-amber-500" />
                  </div>
                  <h4 className="text-xl font-semibold text-white mb-2">AI Agent Pipeline</h4>
                  <p className="text-gray-400 text-sm leading-relaxed">
                    Multiple specialized AI agents work together to help you craft compelling narratives and develop rich characters.
                  </p>
                </div>
              </div>

              <div className="group relative overflow-hidden rounded-2xl border border-white/10 backdrop-blur-sm bg-gradient-to-br from-neutral-900/90 to-neutral-800/90 p-6 hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 via-pink-500/20 to-rose-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center">
                    <Layers className="w-6 h-6 text-purple-500" />
                  </div>
                  <h4 className="text-xl font-semibold text-white mb-2">Intelligent Context Engine</h4>
                  <p className="text-gray-400 text-sm leading-relaxed">
                    Our advanced system remembers every detail across 300,000+ words, ensuring perfect consistency.
                  </p>
                </div>
              </div>

              <div className="group relative overflow-hidden rounded-2xl border border-white/10 backdrop-blur-sm bg-gradient-to-br from-neutral-900/90 to-neutral-800/90 p-6 hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-cyan-500/20 to-teal-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 flex items-center justify-center">
                    <Zap className="w-6 h-6 text-blue-500" />
                  </div>
                  <h4 className="text-xl font-semibold text-white mb-2">Real-time Collaboration</h4>
                  <p className="text-gray-400 text-sm leading-relaxed">
                    Work with AI in real-time, getting instant suggestions and improvements as you write.
                  </p>
                </div>
              </div>

              <div className="group relative overflow-hidden rounded-2xl border border-white/10 backdrop-blur-sm bg-gradient-to-br from-neutral-900/90 to-neutral-800/90 p-6 hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 via-emerald-500/20 to-teal-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-12 h-12 rounded-xl bg-gradient-to-br from-green-500/20 to-emerald-500/20 flex items-center justify-center">
                    <Palette className="w-6 h-6 text-green-500" />
                  </div>
                  <h4 className="text-xl font-semibold text-white mb-2">Style Adaptation</h4>
                  <p className="text-gray-400 text-sm leading-relaxed">
                    AI learns your unique writing style, ensuring your voice shines through every word.
                  </p>
                </div>
              </div>

              <div className="group relative overflow-hidden rounded-2xl border border-white/10 backdrop-blur-sm bg-gradient-to-br from-neutral-900/90 to-neutral-800/90 p-6 hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-12 h-12 rounded-xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center">
                    <Globe className="w-6 h-6 text-indigo-500" />
                  </div>
                  <h4 className="text-xl font-semibold text-white mb-2">World Building Suite</h4>
                  <p className="text-gray-400 text-sm leading-relaxed">
                    Create immersive worlds with AI-powered location and culture development tools.
                  </p>
                </div>
              </div>

              <div className="group relative overflow-hidden rounded-2xl border border-white/10 backdrop-blur-sm bg-gradient-to-br from-neutral-900/90 to-neutral-800/90 p-6 hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
                <div className="absolute inset-0 bg-gradient-to-br from-rose-500/20 via-pink-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-12 h-12 rounded-xl bg-gradient-to-br from-rose-500/20 to-pink-500/20 flex items-center justify-center">
                    <Shield className="w-6 h-6 text-rose-500" />
                  </div>
                  <h4 className="text-xl font-semibold text-white mb-2">Security First</h4>
                  <p className="text-gray-400 text-sm leading-relaxed">
                    Your work is encrypted and secure. We never train on your data.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="relative z-10 border-t border-white/10 py-12 bg-black/50 backdrop-blur-xl">
        <div className="container text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-amber-600 to-orange-600 rounded-lg flex items-center justify-center">
              <PenTool className="w-4 h-4 text-white" />
            </div>
            <span className="text-lg font-semibold">
              BookScribe AI
            </span>
          </div>
          <p className="text-gray-400 text-sm">
            © 2025 BookScribe AI. Empowering authors to write without limits.
          </p>
        </div>
      </footer>
    </div>
  );
}