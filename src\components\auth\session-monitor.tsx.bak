'use client'

import { useAuth } from '@/contexts/auth-context'
import { useEffect, useState } from 'react'
import { AlertCircle, RefreshCw } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'

const SESSION_WARNING_TIME = 5 * 60 * 1000 // 5 minutes
const INACTIVITY_WARNING_TIME = 25 * 60 * 1000 // 25 minutes

export function SessionMonitor() {
  const { session, lastActivity, refreshSession, updateActivity } = useAuth()
  const [showSessionWarning, setShowSessionWarning] = useState(false)
  const [showInactivityWarning, setShowInactivityWarning] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (!session?.expires_at) return

    const checkSession = () => {
      const now = Date.now()
      const expiresAt = new Date(session.expires_at! * 1000).getTime()
      const timeUntilExpiry = expiresAt - now

      // Show warning 5 minutes before session expires
      if (timeUntilExpiry <= SESSION_WARNING_TIME && timeUntilExpiry > 0) {
        setShowSessionWarning(true)
      } else {
        setShowSessionWarning(false)
      }
    }

    // Check immediately
    checkSession()

    // Check every minute
    const interval = setInterval(checkSession, 60 * 1000)

    return () => clearInterval(interval)
  }, [session])

  useEffect(() => {
    if (!lastActivity) return

    const checkInactivity = () => {
      const now = Date.now()
      const timeSinceActivity = now - lastActivity.getTime()

      // Show warning after 25 minutes of inactivity
      if (timeSinceActivity >= INACTIVITY_WARNING_TIME) {
        setShowInactivityWarning(true)
      } else {
        setShowInactivityWarning(false)
      }
    }

    // Check every minute
    const interval = setInterval(checkInactivity, 60 * 1000)

    return () => clearInterval(interval)
  }, [lastActivity])

  const handleRefreshSession = async () => {
    try {
      await refreshSession()
      toast({
        title: 'Session refreshed',
        description: 'Your session has been extended.',
      })
      setShowSessionWarning(false)
    } catch (_error) {
      toast({
        title: 'Session refresh failed',
        description: 'Please sign in again.',
        variant: 'destructive',
      })
    }
  }

  const handleContinueSession = () => {
    updateActivity()
    setShowInactivityWarning(false)
    toast({
      title: 'Session continued',
      description: 'Your session will remain active.',
    })
  }

  if (!session) return null

  return (
    <>
      {showSessionWarning && (
        <Alert className="fixed bottom-4 right-4 w-96 shadow-lg border-amber-600">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Session Expiring Soon</AlertTitle>
          <AlertDescription className="mt-2">
            Your session will expire in less than 5 minutes. Click below to stay signed in.
            <Button
              size="sm"
              className="mt-3 w-full"
              onClick={handleRefreshSession}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Extend Session
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {showInactivityWarning && (
        <Alert className="fixed bottom-4 right-4 w-96 shadow-lg border-amber-600">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Are you still there?</AlertTitle>
          <AlertDescription className="mt-2">
            You&apos;ve been inactive for a while. Your session will expire in 5 minutes due to inactivity.
            <Button
              size="sm"
              className="mt-3 w-full"
              onClick={handleContinueSession}
            >
              Continue Working
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </>
  )
}