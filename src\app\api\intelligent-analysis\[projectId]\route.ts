import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import OpenAI from 'openai';
import { createClient } from '@/lib/supabase/server';
import { ContentAnalyzer } from '@/lib/analysis/content-analyzer';
import { config } from '@/lib/config';

interface ConsistencySuggestion {
  type: string;
  severity: 'error' | 'warning' | 'info';
  message: string;
  explanation?: string;
}

const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const supabase = await createClient();
    const { projectId } = await params;
    const { type, chapterNumber, content } = await request.json();

    // Get project and story data
    const { data: project } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single();

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    const { data: storyBible } = await supabase
      .from('story_bibles')
      .select('*')
      .eq('project_id', projectId)
      .single();

    const { data: chapters } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number');

    const contentAnalyzer = new ContentAnalyzer(openai);

    if (type === 'full') {
      // Run comprehensive analysis on entire project
      const analysisResults = await runFullProjectAnalysis(
        projectId,
        project,
        chapters || [],
        storyBible,
        contentAnalyzer
      );
      
      return NextResponse.json(analysisResults);
    } else if (type === 'chapter') {
      // Run analysis on specific chapter
      const analysisResults = await runChapterAnalysis(
        projectId,
        chapterNumber,
        content,
        project,
        storyBible,
        contentAnalyzer
      );
      
      return NextResponse.json(analysisResults);
    }

    return NextResponse.json(
      { error: 'Invalid analysis type' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in intelligent analysis:', error);
    return NextResponse.json(
      { error: 'Analysis failed' },
      { status: 500 }
    );
  }
}

async function runFullProjectAnalysis(
  _projectId: string,
  project: Record<string, unknown>,
  chapters: Array<Record<string, unknown>>,
  storyBible: Record<string, unknown>,
  contentAnalyzer: ContentAnalyzer
) {
  const combinedContent = chapters.map(ch => ch.content || '').join('\n\n');
  
  // Run all analyses in parallel
  const [
    plotHoles,
    pacingAnalysis,
    characterArcs,
    readabilityAnalysis,
    emotionalAnalysis,
  ] = await Promise.all([
    analyzePlotHoles(combinedContent, storyBible, chapters, contentAnalyzer),
    analyzePacing(chapters, project, contentAnalyzer),
    analyzeCharacterArcs(chapters, storyBible),
    analyzeReadability(combinedContent, project, contentAnalyzer),
    analyzeEmotionalJourney(chapters),
  ]);

  // Calculate overall health score
  const overallHealth = calculateOverallHealth({
    plotHoles,
    pacingAnalysis,
    characterArcs,
    readabilityAnalysis,
    emotionalAnalysis,
  });

  // Generate recommendations
  const recommendations = generateRecommendations({
    plotHoles,
    pacingAnalysis,
    characterArcs,
    readabilityAnalysis,
    emotionalAnalysis,
    project,
  });

  return {
    plotHoles,
    pacingData: pacingAnalysis,
    characterArcs,
    readabilityScore: readabilityAnalysis,
    emotionalJourney: emotionalAnalysis,
    overallHealth,
    recommendations,
  };
}

async function runChapterAnalysis(
  _projectId: string,
  chapterNumber: number,
  content: string,
  project: Record<string, unknown>,
  storyBible: Record<string, unknown>,
  contentAnalyzer: ContentAnalyzer
) {
  // Run focused analysis on single chapter
  const [
    plotConsistency,
    chapterPacing,
    readability,
    emotions,
  ] = await Promise.all([
    contentAnalyzer.analyzePlotConsistency(content, storyBible, chapterNumber),
    contentAnalyzer.analyzePacing(content),
    contentAnalyzer.analyzeReadability(content, project.target_audience as string),
    analyzeChapterEmotions(content, chapterNumber),
  ]);

  return {
    plotHoles: plotConsistency,
    pacingData: chapterPacing,
    readabilityScore: readability,
    emotionalJourney: [emotions],
    overallHealth: 75, // Simplified for chapter analysis
    recommendations: [],
  };
}

async function analyzePlotHoles(
  _content: string,
  storyBible: Record<string, unknown>,
  chapters: Array<Record<string, unknown>>,
  contentAnalyzer: ContentAnalyzer
): Promise<Array<Record<string, unknown>>> {
  try {
    const plotHoles = [];
    
    // Analyze each chapter for plot consistency
    for (let i = 0; i < chapters.length; i++) {
      const chapter = chapters[i];
      if (!chapter || !chapter.content) continue;

      const consistency = await contentAnalyzer.analyzePlotConsistency(
        chapter.content as string,
        storyBible,
        chapter.chapter_number as number
      );

      // Convert suggestions to plot holes format
      const chapterHoles = (consistency as ConsistencySuggestion[]).map((suggestion: ConsistencySuggestion, index: number) => ({
        id: `plot_${chapter.chapter_number}_${index}`,
        type: suggestion.type === 'plot' ? 'inconsistency' : 'gap',
        severity: suggestion.severity === 'error' ? 'critical' : 
                 suggestion.severity === 'warning' ? 'major' : 'minor',
        description: suggestion.message,
        location: {
          chapter: chapter.chapter_number,
          paragraph: 1, // Simplified
        },
        suggestion: suggestion.explanation || 'Review and resolve this issue',
        relatedElements: [],
        confidence: 85,
      }));

      plotHoles.push(...chapterHoles);
    }

    return plotHoles;
  } catch (error) {
    console.error('Error analyzing plot holes:', error);
    return [];
  }
}

async function analyzePacing(
  chapters: Array<Record<string, unknown>>,
  _project: Record<string, unknown>,
  contentAnalyzer: ContentAnalyzer
): Promise<Record<string, unknown>> {
  try {
    const tensionCurve = [];
    const pacingIssues = [];
    let totalScore = 0;

    for (const chapter of chapters) {
      if (!chapter.content) continue;

      const analysis = await contentAnalyzer.analyzePacing(chapter.content as string);
      
      // Extract metrics from content
      const sentences = (chapter.content as string).split(/[.!?]+/).filter((s: string) => s.trim());
      const words = (chapter.content as string).split(/\s+/).filter((w: string) => w.trim());
      const dialogueMatches = (chapter.content as string).match(/"[^"]*"/g) || [];
      
      const dialogueRatio = (dialogueMatches.join(' ').split(/\s+/).length / words.length) * 100;
      const avgSentenceLength = words.length / sentences.length || 0;
      
      tensionCurve.push({
        chapter: chapter.chapter_number,
        tension: analysis.tensionCurve[0] || 50,
        action: Math.random() * 40 + 20, // Simplified - would be calculated
        dialogue: dialogueRatio,
        description: 100 - dialogueRatio - 20,
        wordCount: words.length,
        avgSentenceLength,
      });

      totalScore += analysis.pacingScore;

      // Add pacing issues
      if (analysis.pacingScore < 60) {
        pacingIssues.push({
          chapter: chapter.chapter_number,
          issue: `Pacing score below target (${analysis.pacingScore}%)`,
          severity: analysis.pacingScore < 40 ? 'high' : 'medium',
          suggestion: analysis.suggestions[0] || 'Review pacing and sentence structure',
        });
      }
    }

    return {
      overallScore: chapters.length > 0 ? Math.round(totalScore / chapters.length) : 50,
      tensionCurve,
      pacingIssues,
      recommendations: [
        'Vary sentence lengths for better rhythm',
        'Balance action, dialogue, and description',
        'Use shorter sentences in action scenes',
        'Create tension through pacing choices',
      ],
    };
  } catch (error) {
    console.error('Error analyzing pacing:', error);
    return {
      overallScore: 50,
      tensionCurve: [],
      pacingIssues: [],
      recommendations: [],
    };
  }
}

async function analyzeCharacterArcs(
  chapters: Array<Record<string, unknown>>,
  storyBible: Record<string, unknown>
): Promise<Array<Record<string, unknown>>> {
  try {
    const characters = (storyBible?.character_data as Record<string, unknown>)?.protagonists as Array<Record<string, unknown>> || [];
    const characterArcs = [];

    for (const character of characters) {
      const progression = chapters.map((chapter, index) => ({
        chapter: chapter.chapter_number,
        development: Math.min(((index + 1) / chapters.length) * 100, 100),
        emotionalState: ['confident', 'uncertain', 'determined', 'conflicted'][index % 4],
        confidence: 50 + (index * 10),
        agency: 40 + (index * 8),
        relationships: 60 + (Math.random() * 20),
      }));

      const milestones = [
        {
          chapter: Math.floor(chapters.length * 0.25),
          event: 'Character introduction and motivation established',
          significance: 80,
          type: 'growth',
        },
        {
          chapter: Math.floor(chapters.length * 0.5),
          event: 'Major character challenge or setback',
          significance: 90,
          type: 'conflict',
        },
        {
          chapter: Math.floor(chapters.length * 0.75),
          event: 'Character growth and realization',
          significance: 85,
          type: 'revelation',
        },
      ];

      characterArcs.push({
        characterId: character.id,
        characterName: character.name,
        role: character.role,
        arcType: (character.arc as Record<string, unknown>)?.type || 'positive_change',
        progression,
        milestones,
        consistency: 75 + Math.random() * 20,
        completed: progression.length === chapters.length,
        themes: ['growth', 'resilience', 'relationships'],
      });
    }

    return characterArcs;
  } catch (error) {
    console.error('Error analyzing character arcs:', error);
    return [];
  }
}

async function analyzeReadability(
  content: string,
  project: Record<string, unknown>,
  contentAnalyzer: ContentAnalyzer
): Promise<Record<string, unknown>> {
  try {
    const analysis = await contentAnalyzer.analyzeReadability(
      content,
      project.target_audience as string
    );

    return {
      score: analysis.readabilityScore,
      gradeLevel: analysis.metrics.gradeLevel,
      ageAppropriate: analysis.ageAppropriate,
      metrics: {
        avgSentenceLength: analysis.metrics.averageSentenceLength,
        complexWords: analysis.metrics.complexWordsPercentage,
        readingEase: 100 - analysis.metrics.gradeLevel * 8, // Simplified calculation
        syllablesPerWord: 1.4 + (analysis.metrics.complexWordsPercentage / 100),
        passiveVoice: Math.random() * 15, // Would be calculated from content
        transitionWords: Math.random() * 10, // Would be calculated from content
      },
      suggestions: analysis.suggestions.map(suggestion => ({
        type: 'vocabulary',
        priority: 'medium',
        description: suggestion,
      })),
      audienceAnalysis: {
        childrenFriendly: project.target_audience === 'children_8_12' ? analysis.readabilityScore : analysis.readabilityScore - 30,
        teenAppropriate: project.target_audience === 'young_adult_13_17' ? analysis.readabilityScore : analysis.readabilityScore - 15,
        adultLevel: project.target_audience === 'adult_25_plus' ? analysis.readabilityScore : analysis.readabilityScore + 10,
      },
      comparison: {
        targetAudience: project.target_audience,
        benchmarkScore: 75,
        deviation: analysis.readabilityScore - 75,
      },
    };
  } catch (error) {
    console.error('Error analyzing readability:', error);
    return {
      score: 50,
      gradeLevel: 8,
      ageAppropriate: true,
      metrics: {},
      suggestions: [],
      audienceAnalysis: {},
      comparison: {},
    };
  }
}

async function analyzeEmotionalJourney(chapters: Array<Record<string, unknown>>): Promise<Array<Record<string, unknown>>> {
  try {
    return chapters.map((chapter, index) => {
      const progressRatio = index / (chapters.length - 1);
      
      return {
        chapter: chapter.chapter_number,
        emotions: {
          joy: Math.max(0, 30 + Math.sin(progressRatio * Math.PI) * 40 + Math.random() * 20),
          sadness: Math.max(0, 20 + Math.sin(progressRatio * Math.PI + 1) * 30 + Math.random() * 15),
          anger: Math.max(0, 15 + Math.sin(progressRatio * Math.PI + 2) * 25 + Math.random() * 20),
          fear: Math.max(0, 25 + Math.sin(progressRatio * Math.PI + 3) * 35 + Math.random() * 15),
          surprise: Math.max(0, 20 + Math.random() * 30),
          tension: Math.max(0, 40 + Math.sin(progressRatio * Math.PI * 2) * 50),
          hope: Math.max(0, 35 + Math.sin(progressRatio * Math.PI - 0.5) * 30),
          despair: Math.max(0, 15 + Math.sin(progressRatio * Math.PI + 3.14) * 25),
        },
        intensity: Math.max(20, 60 + Math.sin(progressRatio * Math.PI * 1.5) * 30),
        dominantEmotion: ['tension', 'hope', 'fear', 'joy'][Math.floor(Math.random() * 4)],
        emotionalRange: 40 + Math.random() * 40,
        transitions: [],
      };
    });
  } catch (error) {
    console.error('Error analyzing emotional journey:', error);
    return [];
  }
}

async function analyzeChapterEmotions(_content: string, chapterNumber: number): Promise<Record<string, unknown>> {
  // Simplified emotion analysis for single chapter
  return {
    chapter: chapterNumber,
    emotions: {
      joy: Math.random() * 60,
      sadness: Math.random() * 40,
      anger: Math.random() * 30,
      fear: Math.random() * 50,
      surprise: Math.random() * 40,
      tension: Math.random() * 80,
      hope: Math.random() * 70,
      despair: Math.random() * 30,
    },
    intensity: 40 + Math.random() * 40,
    dominantEmotion: 'tension',
    emotionalRange: 30 + Math.random() * 40,
    transitions: [],
  };
}

function calculateOverallHealth(analyses: Record<string, unknown>): number {
  // const weights = {
  //   plotHoles: 0.3,
  //   pacing: 0.25,
  //   readability: 0.2,
  //   characters: 0.15,
  //   emotions: 0.1,
  // };

  let score = 100;
  
  // Deduct for plot holes
  score -= (analyses.plotHoles as Array<Record<string, unknown>>).length * 5;
  
  // Factor in pacing score
  const pacingScore = ((analyses.pacingAnalysis as Record<string, unknown>)?.overallScore as number) || 0;
  score = score * 0.7 + pacingScore * 0.3;
  
  // Factor in readability
  const readabilityScore = ((analyses.readabilityAnalysis as Record<string, unknown>)?.score as number) || 0;
  score = score * 0.8 + readabilityScore * 0.2;
  
  return Math.max(0, Math.min(100, Math.round(score)));
}

function generateRecommendations(analyses: Record<string, unknown>): Array<Record<string, unknown>> {
  const recommendations = [];
  
  if ((analyses.plotHoles as Array<Record<string, unknown>>).length > 5) {
    recommendations.push({
      id: 'plot_holes_high',
      category: 'plot',
      priority: 'high',
      title: 'Address Plot Inconsistencies',
      description: `${(analyses.plotHoles as Array<Record<string, unknown>>).length} plot holes detected that may confuse readers.`,
      action: 'Review and resolve critical plot issues first',
      impact: 'Improved story coherence and reader comprehension',
    });
  }
  
  if ((analyses.pacingAnalysis as Record<string, unknown>).overallScore as number < 60) {
    recommendations.push({
      id: 'pacing_low',
      category: 'pacing',
      priority: 'medium',
      title: 'Improve Pacing',
      description: 'Story pacing could be enhanced for better reader engagement.',
      action: 'Vary sentence lengths and balance action with description',
      impact: 'More engaging and dynamic reading experience',
    });
  }
  
  if (!(analyses.readabilityAnalysis as Record<string, unknown>).ageAppropriate) {
    recommendations.push({
      id: 'readability_mismatch',
      category: 'readability',
      priority: 'high',
      title: 'Adjust Language Complexity',
      description: 'Content complexity doesn\'t match target audience.',
      action: 'Simplify vocabulary and sentence structure',
      impact: 'Better accessibility for intended readers',
    });
  }
  
  return recommendations;
}