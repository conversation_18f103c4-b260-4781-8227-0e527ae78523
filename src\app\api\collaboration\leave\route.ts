import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { ServiceManager } from '@/lib/services/service-manager'
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for collaboration leave (30 leaves per hour)
    const clientIP = getClientIP(request)
    const rateLimitResult = generalLimiter.check(30, clientIP)
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset)
    }

    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const body = await request.json()
    const { sessionId } = body

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationHub = await serviceManager.getCollaborationHub()
    if (!collaborationHub) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationHub.leaveSession(
      sessionId,
      authResult.user.id
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to leave session' },
        { status: 400 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Collaboration leave error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}