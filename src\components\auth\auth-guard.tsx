'use client'

import { useAuth } from '@/contexts/auth-context'
import { useAuthLoading } from '@/hooks/use-auth-loading'
import { AuthLoading } from './auth-loading'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
}

export function AuthGuard({ 
  children, 
  requireAuth = true,
  redirectTo = '/login'
}: AuthGuardProps) {
  const { user, loading } = useAuth()
  const { isInitializing } = useAuthLoading()
  const router = useRouter()

  useEffect(() => {
    if (loading) return

    if (requireAuth && !user) {
      router.push(redirectTo)
    } else if (!requireAuth && user) {
      router.push('/dashboard')
    }
  }, [user, loading, requireAuth, redirectTo, router])

  // Show loading state while initializing
  if (isInitializing) {
    return <AuthLoading />
  }

  // If auth is required but no user, show loading (will redirect soon)
  if (requireAuth && !user) {
    return <AuthLoading />
  }

  // If auth is not required but user exists, show loading (will redirect soon)
  if (!requireAuth && user) {
    return <AuthLoading />
  }

  return <>{children}</>
}