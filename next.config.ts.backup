import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // React configuration
  reactStrictMode: true,
  
  // Build optimization
  experimental: {
    optimizePackageImports: [
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-popover',
      '@radix-ui/react-tabs',
      '@radix-ui/react-tooltip',
      'lucide-react',
      'framer-motion'
    ]
  },

  // Production optimizations
  compress: true,
  poweredByHeader: false,

  // Build configuration
  typescript: {
    ignoreBuildErrors: false
  },
  eslint: {
    ignoreDuringBuilds: false
  },

  // Simplified webpack configuration
  webpack: (config, { isServer }) => {
    // Only minimal necessary configurations
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    
    return config;
  }
};

export default nextConfig;