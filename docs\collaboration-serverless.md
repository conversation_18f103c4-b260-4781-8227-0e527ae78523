# Serverless Collaboration Implementation

## Overview

BookScribe's collaboration features have been redesigned to work seamlessly in serverless environments using Supabase's real-time capabilities instead of WebSockets.

## Architecture

### 1. **Database-Backed State**
Instead of in-memory state (Maps, Sets), everything is stored in Supabase:
- `collaboration_sessions` - Session metadata and document content
- `collaboration_participants` - Active users and their roles
- `collaboration_changes` - Change history for undo/redo
- `collaboration_locks` - Section-level locking

### 2. **Supabase Realtime**
Real-time features use Supabase's built-in capabilities:
- **Postgres Changes** - Listen to database updates
- **Presence** - Track online/offline users
- **Broadcast** - Send ephemeral messages (cursor positions)

### 3. **Serverless-Compatible Design**
- No persistent connections required on the server
- All state in database
- Client manages real-time subscriptions
- Server provides REST API for operations

## Key Differences from WebSocket Version

| Feature | WebSocket Version | Serverless Version |
|---------|------------------|-------------------|
| State Storage | In-memory Maps | Supabase Database |
| Real-time Updates | WebSocket broadcast | Supabase Realtime |
| User Presence | Manual tracking | Supabase Presence |
| Scalability | Limited by server | Unlimited |
| Persistence | Lost on restart | Always persisted |

## Usage Example

### Client-Side (React Component)

```typescript
import { useCollaboration } from '@/hooks/use-collaboration'

function CollaborativeEditor({ sessionId, userId }) {
  const {
    documentContent,
    participants,
    joinSession,
    applyChange,
    updateCursor,
    lockSection,
    unlockSection,
    isLocked,
    canEdit
  } = useCollaboration({
    sessionId,
    userId,
    onDocumentChange: (content, version) => {
      // Update your editor
    },
    onCursorMove: (userId, position) => {
      // Show other users' cursors
    }
  })

  useEffect(() => {
    joinSession('editor') // or 'viewer', 'commenter'
    
    return () => {
      // Cleanup happens automatically
    }
  }, [])

  const handleEdit = async (change) => {
    if (!canEdit()) return
    
    await applyChange({
      type: 'insert',
      position: { line: 1, column: 1 },
      content: 'Hello, collaborative world!'
    })
  }

  // ... rest of your editor
}
```

### Server-Side (API Routes)

All collaboration operations go through REST API:
- `POST /api/collaboration/join` - Join a session
- `POST /api/collaboration/leave` - Leave a session
- `POST /api/collaboration/change` - Apply a document change
- `POST /api/collaboration/cursor` - Update cursor position
- `POST /api/collaboration/lock` - Lock a section
- `POST /api/collaboration/unlock` - Unlock a section

## Database Schema

```sql
-- Sessions table
collaboration_sessions (
  id TEXT PRIMARY KEY,
  project_id UUID,
  owner_id UUID,
  document_content TEXT,
  document_version INTEGER,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)

-- Participants table
collaboration_participants (
  session_id TEXT,
  user_id UUID,
  role TEXT,
  status TEXT,
  cursor_position JSONB,
  joined_at TIMESTAMPTZ,
  last_seen TIMESTAMPTZ
)

-- Changes history
collaboration_changes (
  id TEXT PRIMARY KEY,
  session_id TEXT,
  user_id UUID,
  type TEXT,
  position JSONB,
  content TEXT,
  version INTEGER,
  created_at TIMESTAMPTZ
)

-- Section locks
collaboration_locks (
  session_id TEXT,
  section TEXT,
  user_id UUID,
  created_at TIMESTAMPTZ
)
```

## Benefits

1. **Serverless Compatible** - Works on Vercel, Netlify, AWS Lambda, etc.
2. **Automatic Scaling** - Supabase handles all real-time connections
3. **Built-in Persistence** - Never lose work, even on server restart
4. **Cost Effective** - No long-running servers needed
5. **Easy Deployment** - Just deploy your Next.js app as usual

## Migration from WebSocket Version

1. Run the database migration: `supabase migration up`
2. Replace `CollaborationHub` with `CollaborationHubServerless`
3. Use `useCollaboration` hook in client components
4. Remove any WebSocket server code

## Security

- Row Level Security (RLS) ensures users can only access their sessions
- Participants must be authenticated
- Role-based permissions (owner, editor, viewer, commenter)
- Automatic lock expiration (5 minutes)

## Performance Considerations

- Document content is stored as TEXT (up to 1GB in PostgreSQL)
- Changes are recorded incrementally for efficient syncing
- Cursor updates are throttled client-side
- Database indexes on frequently queried columns

## Future Enhancements

1. **Conflict Resolution** - Operational Transformation (OT) or CRDTs
2. **Offline Support** - Local-first with sync
3. **Version Control** - Branch/merge capabilities
4. **Large Documents** - Chunked storage for better performance
5. **Voice/Video** - Integrate with WebRTC services