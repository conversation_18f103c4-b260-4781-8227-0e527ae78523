import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import ServiceManager from '@/lib/services/service-manager';
import { authenticateUser, handleRouteError } from '@/lib/auth';
import { adminLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for orchestrator operations
    const clientIP = getClientIP(request);
    const rateLimitResult = adminLimiter.check(10, clientIP); // 10 requests per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Authentication required for orchestrator management
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }

    const body = await request.json();
    const { action, ...params } = body;
    
    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const orchestrator = await serviceManager.getAIOrchestrator();
    
    if (!orchestrator) {
      return NextResponse.json(
        { error: 'AI Orchestrator service not available' },
        { status: 503 }
      );
    }

    switch (action) {
      case 'submit_task':
        const taskResult = await serviceManager.submitWritingTask(params);
        return NextResponse.json({ 
          success: !!taskResult, 
          taskId: taskResult 
        });

      case 'get_task_status':
        const statusResult = await orchestrator.getTaskStatus(params.taskId);
        return NextResponse.json(statusResult);

      case 'get_agents':
        const agentsResult = await orchestrator.getAvailableAgents();
        return NextResponse.json(agentsResult);

      case 'reassign_task':
        const reassignResult = await orchestrator.reassignTask(params.taskId, params.agentId);
        return NextResponse.json(reassignResult);

      case 'register_agent':
        const registerResult = await orchestrator.registerAgent(params.agent);
        return NextResponse.json(registerResult);

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    return handleRouteError(error, 'Orchestrator POST');
  }
}

export async function GET(request: NextRequest) {
  try {
    // Rate limiting for orchestrator status queries
    const clientIP = getClientIP(request);
    const rateLimitResult = adminLimiter.check(30, clientIP); // 30 requests per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Authentication required for orchestrator status
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }

    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    
    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const orchestrator = await serviceManager.getAIOrchestrator();
    
    if (!orchestrator) {
      return NextResponse.json(
        { error: 'AI Orchestrator service not available' },
        { status: 503 }
      );
    }

    switch (type) {
      case 'agents':
        const agentsResult = await orchestrator.getAvailableAgents();
        return NextResponse.json(agentsResult);

      case 'health':
        const healthResult = await orchestrator.healthCheck();
        return NextResponse.json(healthResult);

      default:
        return NextResponse.json({
          service: 'ai-orchestrator',
          version: '1.0.0',
          endpoints: [
            'POST /api/services/orchestrator - Submit tasks and manage agents',
            'GET /api/services/orchestrator?type=agents - Get available agents',
            'GET /api/services/orchestrator?type=health - Health check'
          ]
        });
    }
  } catch (error) {
    return handleRouteError(error, 'Orchestrator GET');
  }
}