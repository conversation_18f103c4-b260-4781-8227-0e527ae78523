import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { updateCharacterSchema } from '@/lib/validation/schemas'
import { z } from 'zod'

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get character with project ownership check
    const { data: character, error } = await supabase
      .from('characters')
      .select(`
        *,
        projects!inner (
          id,
          title,
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Character not found' }, { status: 404 })
      }
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Clean up the response to remove nested project data
    const { projects: _, ...cleanCharacter } = character
    // Suppress unused variable warning for intentionally unused destructured property
    void _

    return NextResponse.json({ character: cleanCharacter })

  } catch (error) {
    console.error('Error fetching character:', error)
    return NextResponse.json(
      { error: 'Failed to fetch character' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate input
    const validatedData = updateCharacterSchema.parse(body)

    // First verify ownership through project
    const { data: ownershipCheck } = await supabase
      .from('characters')
      .select(`
        project_id,
        character_id,
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (!ownershipCheck) {
      return NextResponse.json({ error: 'Character not found' }, { status: 404 })
    }

    // If character_id is being updated, check for conflicts
    if (validatedData.character_id && validatedData.character_id !== ownershipCheck.character_id) {
      const { data: existingCharacter } = await supabase
        .from('characters')
        .select('id')
        .eq('project_id', ownershipCheck.project_id)
        .eq('character_id', validatedData.character_id)
        .neq('id', id)
        .single()

      if (existingCharacter) {
        return NextResponse.json({ 
          error: 'Character with this ID already exists in the project' 
        }, { status: 409 })
      }
    }

    // Update character
    const updateFields = {
      ...validatedData,
      updated_at: new Date().toISOString()
    }

    const { data: character, error } = await supabase
      .from('characters')
      .update(updateFields)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating character:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ character })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid character data',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Error updating character:', error)
    return NextResponse.json(
      { error: 'Failed to update character' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First verify ownership through project
    const { data: character } = await supabase
      .from('characters')
      .select(`
        id,
        name,
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (!character) {
      return NextResponse.json({ error: 'Character not found' }, { status: 404 })
    }

    // Delete character
    const { error } = await supabase
      .from('characters')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting character:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: `Character "${character.name}" deleted successfully`
    })

  } catch (error) {
    console.error('Error deleting character:', error)
    return NextResponse.json(
      { error: 'Failed to delete character' },
      { status: 500 }
    )
  }
}