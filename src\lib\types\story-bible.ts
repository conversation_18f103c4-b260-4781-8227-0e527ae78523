// Type definitions for Story Bible entries and related data structures

export interface PersonalityTrait {
  name: string;
  description: string;
  strength: number; // 0-100
}

export interface CharacterArc {
  start: string;
  middle: string;
  end: string;
  growthPoints: string[];
  conflicts: string[];
}

export interface CharacterRelationship {
  characterId: string;
  characterName: string;
  relationshipType: 'family' | 'friend' | 'rival' | 'romantic' | 'professional' | 'other';
  description: string;
  dynamics: string;
  evolution?: string;
}

export interface VoiceData {
  tone: string;
  vocabulary: string[];
  speechPatterns: string[];
  catchphrases?: string[];
  dialect?: string;
}

export interface WorldRule {
  name: string;
  description: string;
  category: 'physical' | 'magical' | 'social' | 'technological' | 'other';
  implications: string[];
}

export interface PlotThread {
  id: string;
  name: string;
  description: string;
  status: 'setup' | 'active' | 'resolved' | 'abandoned';
  chapters: number[];
  relatedCharacters: string[];
}

export interface EstablishedFact {
  id: string;
  fact: string;
  category: string;
  establishedIn: number; // chapter number
  references: number[]; // chapter numbers where referenced
}

export interface ContinuityData {
  plotThreads: PlotThread[];
  establishedFacts: EstablishedFact[];
  timeline: TimelineEvent[];
}

export interface TimelineEvent {
  id: string;
  date: string;
  description: string;
  chapters: number[];
  importance: 'major' | 'minor' | 'background';
}

export interface WorldData {
  rules: WorldRule[];
  locations: Location[];
  cultures: Culture[];
  history: HistoryEvent[];
}

export interface Location {
  id: string;
  name: string;
  description: string;
  parentLocation?: string;
  features: string[];
  significance: string;
}

export interface Culture {
  id: string;
  name: string;
  description: string;
  values: string[];
  customs: string[];
  beliefs: string[];
}

export interface HistoryEvent {
  id: string;
  name: string;
  date: string;
  description: string;
  impact: string;
  relatedLocations: string[];
  relatedCultures: string[];
}

export interface AINote {
  timestamp: string;
  note: string;
  category: string;
  confidence: number;
}

export type StoryBibleEntryData = Record<string, unknown>;

export interface StoryBibleAINotes {
  [key: string]: AINote | AINote[] | string | unknown;
}